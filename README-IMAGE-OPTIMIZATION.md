# Image Optimization Guide for Auctus

This guide explains how to use the image optimization tools to improve your site's performance without having to manually update image paths in your code.

## Option 1: Direct Replacement (Recommended)

The simplest approach is to directly replace your original large images with optimized versions. This preserves the original file paths so you don't need to update any code.

```bash
npm run optimize-images:replace
```

This command:
1. Creates backups of your original images in a `backups` subdirectory
2. Replaces the original files with optimized versions
3. Maintains the same filenames and locations

### Benefits:
- No need to update any image import paths in your code
- Immediate performance improvement
- Original images are backed up and can be restored if needed

### Example:

```jsx
// Your existing code will work without changes
import backgroundImage from '@assets/Background.png';

function MyComponent() {
  return <img src={backgroundImage} alt="Background" />;
}
```

## Option 2: Create Optimized Copies (Advanced)

If you prefer to keep your original images unchanged and create optimized versions alongside them:

```bash
npm run optimize-large-images
```

This command:
1. Creates an `optimized` subdirectory in each image's location
2. Generates optimized versions with the same filename
3. Also creates WebP versions for modern browsers

### Using the optimized images:

You'll need to update your imports or paths:

```jsx
// Before
import backgroundImage from '@assets/Background.png';

// After
import backgroundImage from '@assets/optimized/Background.png';
// Optional: Use WebP with fallback
import backgroundImageWebP from '@assets/optimized/Background.webp';

function MyComponent() {
  return (
    <picture>
      <source srcSet={backgroundImageWebP} type="image/webp" />
      <img src={backgroundImage} alt="Background" />
    </picture>
  );
}
```

## Best Practices

Regardless of which option you choose:

1. **Always specify image dimensions**:
   ```jsx
   <img 
     src={image} 
     width="800" 
     height="600" 
     alt="Description" 
   />
   ```

2. **Use lazy loading for below-the-fold images**:
   ```jsx
   <img 
     src={image} 
     loading="lazy" 
     alt="Description" 
   />
   ```

3. **Add responsive image sizing**:
   ```jsx
   <img 
     src={image} 
     sizes="(max-width: 768px) 100vw, 50vw" 
     alt="Description" 
   />
   ```

## Restoring Original Images

If you used Option 1 (direct replacement) and need to restore the original images:

1. Find the backup in the `backups` directory
2. Copy it back to the original location

## Optimizing New Images

When adding new images to your project:

1. Run the optimization script after adding the images:
   ```bash
   npm run optimize-images:replace
   ```

2. Or add them to the `largeImages` array in the script if they're not automatically detected.

## Advanced: Custom Image Component

For the best results, consider creating a custom Image component that handles responsive images, lazy loading, and WebP support:

```jsx
// components/OptimizedImage.tsx
import React from 'react';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width: number;
  height: number;
  className?: string;
  loading?: 'lazy' | 'eager';
}

export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src, 
  alt, 
  width, 
  height, 
  className = '', 
  loading = 'lazy'
}) => {
  // Check if we have a WebP version available
  const hasWebP = src.endsWith('.jpg') || src.endsWith('.jpeg') || src.endsWith('.png');
  const webPSrc = hasWebP ? src.substring(0, src.lastIndexOf('.')) + '.webp' : null;
  
  if (webPSrc) {
    return (
      <picture className={className}>
        <source srcSet={webPSrc} type="image/webp" />
        <img 
          src={src} 
          alt={alt} 
          width={width} 
          height={height} 
          loading={loading} 
          className={className}
        />
      </picture>
    );
  }
  
  return (
    <img 
      src={src} 
      alt={alt} 
      width={width} 
      height={height} 
      loading={loading} 
      className={className}
    />
  );
};
```

Then use it in your components:

```jsx
import { OptimizedImage } from '@components/OptimizedImage';
import backgroundImage from '@assets/Background.png';

function MyComponent() {
  return (
    <OptimizedImage 
      src={backgroundImage} 
      alt="Background" 
      width={1200} 
      height={800} 
    />
  );
}
```

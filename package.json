{"name": "test1", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:full": "tsc && vite build && node scripts/copy-assets.js", "build:analyze": "vite-bundle-visualizer", "typecheck": "tsc --noEmit", "convert": "node scripts/convert-jsx-to-tsx.js", "cleanup": "node scripts/cleanup-jsx-files.js", "check-imports": "node scripts/check-jsx-imports.js", "find-references": "node scripts/find-references.js", "optimize-images": "node scripts/optimize-images.js", "optimize-large-images": "node scripts/optimize-large-images.js", "optimize-images:replace": "node scripts/optimize-large-images.js --replace", "optimize-images:webp": "node scripts/optimize-large-images.js --webp", "optimize-images:both": "node scripts/optimize-large-images.js --replace --webp", "update-deps": "bash update-dependencies.sh", "analyze": "vite-bundle-visualizer", "preview": "vite preview", "preview:prod": "npm run build && npm run preview", "lighthouse": "lighthouse http://localhost:4173 --view", "lint": "eslint ."}, "dependencies": {"@radix-ui/react-label": "^2.1.0", "@tabler/icons-react": "^3.17.0", "axios": "^1.7.9", "clsx": "^2.1.1", "framer-motion": "^11.18.2", "next": "^15.1.7", "prop-types": "^15.8.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-responsive": "^10.0.0", "react-router-dom": "^6.26.2", "react-router-hash-link": "^2.4.3", "react-slick": "^0.30.2", "react-text-gradients": "^1.0.2", "react-transition-group": "^4.4.5", "slick-carousel": "^1.8.1", "smooth-scrollbar": "^8.8.4", "tailwind-merge": "^2.5.2"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/node": "^20.10.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-router-dom": "^5.3.3", "@types/react-slick": "^0.23.13", "@types/react-transition-group": "^4.4.10", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "create-blog-post": "node scripts/create-blog-post.js", "create-design-post": "node scripts/create-blog-post.js design", "create-marketing-post": "node scripts/create-blog-post.js marketing", "create-test-post": "node src/utils/createTestPost.js", "create-webdev-post": "node scripts/create-blog-post.js webdev", "eslint": "^9.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.4.47", "sass-embedded": "^1.83.4", "sharp": "^0.32.6", "tailwindcss": "^3.4.11", "terser": "^5.26.0", "typescript": "^5.4.2", "vite": "^5.4.1", "vite-bundle-visualizer": "^1.1.0", "vite-plugin-compression": "^0.5.1"}}
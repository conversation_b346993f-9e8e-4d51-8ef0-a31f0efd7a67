# TypeScript Troubleshooting Guide

## Common Issues and Solutions

### 1. ES Module vs CommonJS Issues

**Problem:** Error message like "require is not defined in ES module scope"

**Solution:** Use ES module import syntax:

```javascript
// Instead of this (CommonJS)
const fs = require('fs');

// Use this (ES Module)
import fs from 'fs';
```

### 2. Import Path Issues

**Problem:** Cannot find module errors with import paths

**Solution:** Update import paths:

- Remove file extensions from imports (TypeScript will find the correct file)
- Update absolute imports to use aliases
- Ensure you're using the correct path aliases as defined in tsconfig.json

```typescript
// Instead of
import Something from './Component.jsx';
import OtherThing from '/src/components/Other/index.jsx';

// Use
import Something from './Component';
import OtherThing from '@components/Other/index';
```

### 3. React Import Issues

**Problem:** "React must be in scope when using JSX" errors

**Solution:** Make sure to import React:

```typescript

```

### 4. Type Declaration Issues

**Problem:** Missing types for specific libraries

**Solution:** 

1. Check if type definitions exist for the library:
   ```bash
   npm install --save-dev @types/library-name
   ```

2. If type definitions don't exist, create a declaration file in `src/types/declarations.d.ts`:
   ```typescript
   declare module 'untyped-library' {
     export function someFunction(): any;
     // Add more type definitions as needed
   }
   ```

### 5. React Component Type Issues

**Problem:** "Property 'X' does not exist on type '{}'" when using props

**Solution:** Define proper interfaces for component props:

```typescript
interface ButtonProps {
  label: string;
  onClick: () => void;
  disabled?: boolean;
}

function Button({ label, onClick, disabled }: ButtonProps): JSX.Element {
  // ...
}
```

### 6. useState Type Issues

**Problem:** TypeScript inferring incorrect types for useState

**Solution:** Explicitly define the type:

```typescript
// Instead of
const [value, setValue] = useState('');

// Use
const [value, setValue] = useState<string>('');

// For complex types
const [user, setUser] = useState<User | null>(null);
```

### 7. Event Handler Type Issues

**Problem:** TypeScript errors in event handlers

**Solution:** Properly type your event handlers:

```typescript
// For click events
function handleClick(event: React.MouseEvent<HTMLButtonElement>): void {
  // ...
}

// For input changes
function handleChange(event: React.ChangeEvent<HTMLInputElement>): void {
  const newValue = event.target.value;
  // ...
}
```

### 8. Module Not Found Errors for CSS/SCSS/Assets

**Problem:** TypeScript can't find non-JS/TS files like CSS, images, etc.

**Solution:** Add declarations for these files in `src/types/declarations.d.ts`:

```typescript
declare module '*.scss' {
  const content: { [className: string]: string };
  export default content;
}

declare module '*.css' {
  const content: { [className: string]: string };
  export default content;
}

declare module '*.svg' {
  const content: string;
  export default content;
}

declare module '*.png' {
  const content: string;
  export default content;
}
```

### 9. Path Alias Issues

**Problem:** TypeScript can't resolve path aliases defined in tsconfig.json

**Solution:** Make sure your path aliases are correctly defined in both:

- `tsconfig.json`
- `vite.config.ts`

And that they match exactly.

### 10. Type Errors in External Libraries

**Problem:** Type errors coming from node_modules

**Solution:** Use the `skipLibCheck` option in tsconfig.json (already set) or add more specific type overrides in your declaration files.

## Running TypeScript Type Checking

To check for TypeScript errors:

```bash
npm run typecheck
```

## Converting JSX to TSX

To convert more JSX files to TSX:

```bash
npm run convert
```

## Fixing Path Import Errors

If you encounter path import errors after conversion:

1. Check if you're using absolute paths (starting with "/")
2. Replace them with alias paths (@components, @pages, etc.)
3. Remove file extensions from import statements

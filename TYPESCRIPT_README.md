# Auctus: TypeScript Migration Guide

## What Has Been Set Up

1. **TypeScript Configuration**
   - Added `tsconfig.json` with React JSX support
   - Added `tsconfig.node.json` for Vite
   - Set up path aliases to match your existing configuration
   - Initially configured with `noImplicitAny: false` for easier migration

2. **Package Dependencies**
   - Added TypeScript and related type definitions
   - Updated `package.json` with TypeScript-related scripts:
     - `npm run typecheck`: Run TypeScript compiler to check for errors
     - `npm run convert`: Run automated JSX to TSX conversion
     - `npm run build`: Updated to run TypeScript compiler before build

3. **Type Definitions**
   - Created `src/types/index.ts` with base types:
     - `PricingPlan` and `PricingFeature` interfaces
     - `BlogPost` interface
     - `PortfolioProject` interface
     - `QuizAnswer` interface for the quote quiz

4. **Conversion Utilities**
   - Added `scripts/convertJsxToTsx.js` to help automate the conversion process
   - Created `TYPESCRIPT_CONVERSION_GUIDE.md` with detailed steps and best practices

5. **Initial Conversions**
   - Converted `App.jsx` to `App.tsx` with proper type annotations
   - Updated `vite.config.js` to `vite.config.ts`

## How to Complete the Migration

### Step 1: Install Dependencies

Run the following command to install the required TypeScript dependencies:

```bash
npm install
```

### Step 2: Automated Conversion (Optional)

You can use the automated conversion script to create initial TypeScript versions of your JSX files:

```bash
npm run convert
```

This will create `.tsx` versions of all your `.jsx` files without deleting the originals. Note that this script only adds basic TypeScript support—you'll still need to manually add proper type annotations.

### Step 3: Manual Type Enhancement

For each component, you'll need to:

1. Add proper type definitions for props
2. Add return type annotations to functions
3. Add type annotations for state hooks
4. Type any event handlers

Example of adding types to a component:

```typescript
// Before (JavaScript)
function Button({ text, onClick, variant = 'primary' }) {
  return (
    <button 
      onClick={onClick}
      className={`btn ${variant === 'primary' ? 'btn-primary' : 'btn-secondary'}`}
    >
      {text}
    </button>
  );
}

// After (TypeScript)
interface ButtonProps {
  text: string;
  onClick: () => void;
  variant?: 'primary' | 'secondary';
}

function Button({ text, onClick, variant = 'primary' }: ButtonProps): JSX.Element {
  return (
    <button 
      onClick={onClick}
      className={`btn ${variant === 'primary' ? 'btn-primary' : 'btn-secondary'}`}
    >
      {text}
    </button>
  );
}
```

### Step 4: Update Imports

After converting files to TypeScript, update your imports throughout the project:

1. Remove explicit `.jsx` and `.tsx` extensions in import statements (TypeScript will find the correct files)
2. Update any dynamic imports to use TypeScript types

### Step 5: Testing

After each round of conversions, test your application:

1. Run `npm run typecheck` to check for TypeScript errors
2. Run `npm run dev` to make sure the application works as expected

### Step 6: Stricter Type Checking (Later)

Once the conversion is complete and working, you can enable stricter type checking:

1. Update `tsconfig.json` to set `"noImplicitAny": true`
2. Run `npm run typecheck` again and fix any new errors
3. Consider adding other strict checks like `"strictNullChecks": true`

## Best Practices for TypeScript in React

### Component Types

Use these patterns for defining component types:

```typescript
// Function Component with React.FC


interface HeaderProps {
  title: string;
  subtitle?: string;
}

const Header: React.FC<HeaderProps> = ({ title, subtitle }) => {
  return (
    <header>
      <h1>{title}</h1>
      {subtitle && <h2>{subtitle}</h2>}
    </header>
  );
};

// Alternative: Function Component with explicit return type
function Header({ title, subtitle }: HeaderProps): JSX.Element {
  // Component code
}
```

### Hooks with TypeScript

```typescript
// useState
const [count, setCount] = useState<number>(0);
const [user, setUser] = useState<User | null>(null);

// useRef
const inputRef = useRef<HTMLInputElement>(null);

// useEffect with TypeScript
useEffect(() => {
  // Effect code
}, [dependency1, dependency2]);
```

### Event Handlers

```typescript
// Click events
function handleClick(event: React.MouseEvent<HTMLButtonElement>): void {
  // Handler code
}

// Form events
function handleSubmit(event: React.FormEvent<HTMLFormElement>): void {
  event.preventDefault();
  // Submit handler code
}

// Input changes
function handleChange(event: React.ChangeEvent<HTMLInputElement>): void {
  const value = event.target.value;
  // Change handler code
}
```

## Troubleshooting

### Missing Type Definitions

If you encounter errors about missing type definitions for a library:

1. Try to install types: `npm install --save-dev @types/[library-name]`
2. If types don't exist, create a declaration file:

```typescript
// src/types/declarations.d.ts
declare module 'untyped-library' {
  // Define types based on how you use the library
  export function someFunction(): void;
  // ...
}
```

### "Could not find a declaration file for module"

This error occurs when TypeScript can't find type definitions for an imported module:

1. Check if the module has built-in TypeScript support
2. Install `@types/[module-name]` if available
3. Create a declaration file as shown above

### Property does not exist on type

When TypeScript doesn't recognize a property on an object:

1. Use type assertions (as a last resort): `(user as any).unknownProperty`
2. Better: Extend the interface to include the property
3. Use optional chaining: `user?.unknownProperty`

## Additional Resources

- [TypeScript Handbook](https://www.typescriptlang.org/docs/handbook/intro.html)
- [React TypeScript Cheatsheet](https://react-typescript-cheatsheet.netlify.app/)
- [TypeScript with React](https://www.typescriptlang.org/docs/handbook/react.html)

// hooks/useMediaQuery.js
import { useState, useEffect } from "react";

const useMediaQuery = (query) => {
  // Set the initial value correctly
  const [matches, setMatches] = useState(() => window.matchMedia(query).matches);

  useEffect(() => {
    const mediaQueryList = window.matchMedia(query);
    const listener = (event) => {
      setMatches(event.matches);
    };
    
    // Modern browsers
    mediaQueryList.addEventListener("change", listener);

    // Cleanup on unmount
    return () => {
      mediaQueryList.removeEventListener("change", listener);
    };
  }, [query]);

  return matches;
};

export default useMediaQuery;
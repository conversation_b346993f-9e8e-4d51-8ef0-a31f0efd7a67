# CRM Field Mapping for Quote Quiz

## Field Mapping Reference

The quote quiz data is mapped to the following CRM fields:

| Quiz Answer | CRM Field | Transformation |
|-------------|-----------|----------------|
| Website type selection | `websiteType` | "Sell my products online" → "E-commerce"<br>"Advertise my business" → "Business Website" |
| Business type selection | `businessType` | Direct mapping (no transformation) |
| Existing website (Yes/No) | `hasExistingWebsite` | String to boolean ("Yes" → true, "No" → false) |
| Timeline selection | `timeline` | "As soon as possible" → "Urgent"<br>"Within a month" → "1 Month"<br>"Within a few months" → "3 Months" |
| Budget range | `budget` | "£1,000 - £1,999" → 1500<br>"£2,000 - £4,999" → 3500<br>"£5,000+" → 7500 |
| Requirements text | `requirements` | Direct mapping (no transformation) |
| Email address | `email` | Direct mapping (no transformation) |
| Contact name | `clientName` | Split into firstName and lastName |
| Phone number | `contactNumber` | Complex object with primaryPhoneNumber, countryCode, etc. |

## Additional Fields Set Automatically

| CRM Field | Value | Purpose |
|-----------|-------|---------|
| `name` | "Website Quote - [Client Name]" | Quote identifier |
| `status` | "New" | Initial quote status |
| `source` | "FORM" | Indicates quote came from website form |
| `toDo` | false | To-do flag |
| `quoteDate` | Current timestamp | When quote was submitted |
| `quoteAmount` | null | To be filled by sales team |
| `services` | null | To be filled by sales team |
| `createdBy.source` | "FORM" | Creator source |
| `context` | JSON string | Original quiz answers for reference |

## Important Notes

1. **Budget Field**: The budget is stored as a number (not a string) in the CRM. We convert the ranges to their midpoint values.

2. **Phone Numbers**: UK phone numbers are automatically formatted with:
   - Country code: "GB"
   - Calling code: "+44"
   - Number: Phone number without leading 0

3. **Website Type**: Simplified from the quiz options to cleaner values for CRM reporting.

4. **Timeline**: Simplified from descriptive text to concise values.

5. **Context Field**: Still used to store the original quiz answers as a JSON string for reference.

## Enum Values

Make sure your CRM has these as valid enum values:

- **source**: Must include "FORM" as a valid option
- **status**: Should include "New" as a valid option
- **websiteType**: Should accept "E-commerce" and "Business Website"
- **timeline**: Should accept "Urgent", "1 Month", "3 Months"

## Error Handling

If the CRM rejects certain values, check:
1. Enum fields have the correct allowed values
2. Number fields are receiving numbers (not strings)
3. Boolean fields are receiving true/false (not "Yes"/"No")
4. Required fields are all being provided

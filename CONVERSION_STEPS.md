# Step-by-Step TypeScript Conversion Guide

## Setup (Already Completed)

1. ✅ Install TypeScript and required type definitions
2. ✅ Create tsconfig.json and tsconfig.node.json
3. ✅ Set up types directory with basic interfaces
4. ✅ Add TypeScript scripts to package.json
5. ✅ Convert vite.config.js to vite.config.ts
6. ✅ Create a sample converted component (App.tsx and PricingSection.tsx)

## Step 1: Install Dependencies

Run the following command to install all required packages:

```bash
npm install
```

## Step 2: Run the TypeScript Checker

Check if the setup is working:

```bash
npm run typecheck
```

You'll likely see errors since most files are still in JSX, but the TypeScript compiler should run without crashing.

## Step 3: Conversion Strategy

Convert your files in this order:

1. Shared types and utilities
2. Common/shared components
3. Feature-specific components
4. Page components
5. Main app components

This bottom-up approach ensures you have the necessary types defined before tackling more complex components.

## Step 4: Converting Components

For each component, follow these steps:

### Option 1: Automatic Initial Conversion

```bash
node scripts/convertJsxToTsx.js
```

This creates `.tsx` versions of all your `.jsx` files with minimal changes.

### Option 2: Manual Conversion (Recommended for better control)

For each component:

1. Create a `.tsx` copy of your `.jsx` file
2. Add React import if needed: ``
3. Add type definitions:
   - Create interfaces for props
   - Add return type annotations
   - Type hooks and event handlers

### Example: Converting a Component

**Original component.jsx:**
```jsx
import { useState } from "react";

function Counter({ initialCount, step }) {
  const [count, setCount] = useState(initialCount);

  function increment() {
    setCount(count + step);
  }

  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={increment}>Increment</button>
    </div>
  );
}

export default Counter;
```

**Converted component.tsx:**
```tsx
import React, { useState } from "react";

interface CounterProps {
  initialCount: number;
  step: number;
}

function Counter({ initialCount, step }: CounterProps): JSX.Element {
  const [count, setCount] = useState<number>(initialCount);

  function increment(): void {
    setCount(count + step);
  }

  return (
    <div>
      <p>Count: {count}</p>
      <button onClick={increment}>Increment</button>
    </div>
  );
}

export default Counter;
```

## Step 5: Converting Files One by One

Use these commands to test after converting each file or group of files:

```bash
# Run TypeScript type checking
npm run typecheck

# Start development server
npm run dev
```

### Common Components to Convert First:

1. `/src/components/PricingCard`
2. `/src/components/Footer`
3. `/src/components/Navbar`
4. `/src/components/Button` (if it exists)
5. `/src/components/Newsletter`

### Next, Convert Feature Components:

1. `/src/components/AnimatedCards`
2. `/src/components/QuoteQuiz`
3. `/src/components/ParallaxHero`
4. `/src/components/AboutUs`
5. `/src/components/CardCarousel`

### Then, Convert Page Components:

1. Blog pages
2. Portfolio pages
3. Service pages

### Finally, Top-Level Components:

1. App.tsx (already converted)
2. main.tsx

## Step 6: Update Imports

Once you've converted files:

1. Update imports by removing explicit `.jsx` extensions
2. Fix any import errors flagged by TypeScript

## Step 7: Clean Up

1. Remove PropTypes once TypeScript interfaces are in place:
   ```tsx
   // Remove
   Component.propTypes = {
     prop1: PropTypes.string.isRequired
   }
   
   // Keep TypeScript interface
   interface ComponentProps {
     prop1: string;
   }
   ```

2. Fix any remaining TypeScript errors.

## Step 8: Enable Stricter Type Checking

Once everything is working:

1. Edit `tsconfig.json` to enable stricter checking:
   ```json
   {
     "compilerOptions": {
       "noImplicitAny": true,
       "strictNullChecks": true
     }
   }
   ```

2. Run the type checker again and fix any new errors:
   ```bash
   npm run typecheck
   ```

## Step 9: Clean Up Old Files

Once you're confident everything is working:

1. Remove the original `.jsx` files
2. Update any remaining imports that might still reference them

## Step 10: Update Build Process

Make sure your build process works with TypeScript:

```bash
npm run build
```

## Common TypeScript Patterns for React Projects

### Type Definitions for Props

```typescript
interface ButtonProps {
  text: string;
  onClick: () => void;
  variant?: 'primary' | 'secondary';
  disabled?: boolean;
}
```

### Functional Components

```typescript
// With React.FC
const Button: React.FC<ButtonProps> = ({ text, onClick, variant = 'primary', disabled = false }) => {
  // ...
};

// With function declaration
function Button({ text, onClick, variant = 'primary', disabled = false }: ButtonProps): JSX.Element {
  // ...
}
```

### Hooks

```typescript
// useState
const [value, setValue] = useState<string>('');
const [items, setItems] = useState<string[]>([]);
const [user, setUser] = useState<User | null>(null);

// useRef
const inputRef = useRef<HTMLInputElement>(null);

// useReducer
interface State {
  count: number;
  isLoading: boolean;
}

type Action = 
  | { type: 'increment'; payload: number }
  | { type: 'set_loading'; payload: boolean };

const reducer = (state: State, action: Action): State => {
  // ...
};

const [state, dispatch] = useReducer(reducer, initialState);
```

### Event Handlers

```typescript
// Click event
const handleClick = (event: React.MouseEvent<HTMLButtonElement>): void => {
  // ...
};

// Change event
const handleChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
  setValue(event.target.value);
};

// Form submission
const handleSubmit = (event: React.FormEvent<HTMLFormElement>): void => {
  event.preventDefault();
  // ...
};
```

### Async Functions

```typescript
// Async function with proper types
const fetchData = async (): Promise<User[]> => {
  try {
    const response = await fetch('/api/users');
    if (!response.ok) {
      throw new Error('Failed to fetch');
    }
    return await response.json() as User[];
  } catch (error) {
    console.error(error);
    return [];
  }
};
```

## Troubleshooting Common Issues

### Missing Type Definitions

```typescript
// Create a declaration file for libraries without type definitions
// src/types/declarations.d.ts
declare module 'untyped-library' {
  export default function doSomething(): void;
}
```

### Type Assertions (Use Sparingly)

```typescript
// When TypeScript doesn't understand a type
const element = document.getElementById('root') as HTMLDivElement;

// When working with complex data structures
const data = JSON.parse(jsonString) as { users: User[] };
```

### Unknown Properties on Objects

```typescript
// Option 1: Use interface extension
interface User {
  id: string;
  name: string;
}

interface ExtendedUser extends User {
  additionalProp?: string;
}

// Option 2: Use type assertion (less preferred)
const user = data as any;
```

### Dynamic Content from APIs

```typescript
// Option 1: Type guards
function isUser(obj: any): obj is User {
  return obj && typeof obj.id === 'string' && typeof obj.name === 'string';
}

// Option 2: Generic fetch function
async function fetchData<T>(url: string): Promise<T> {
  const response = await fetch(url);
  return await response.json() as T;
}

const users = await fetchData<User[]>('/api/users');
```

#!/bin/bash

# Install TypeScript and required type definitions
echo "Installing TypeScript and type definitions..."

npm install --save-dev typescript @types/node @types/react @types/react-dom @types/react-router-dom

# Add additional type definitions for your specific dependencies
echo "Installing additional type definitions..."
npm install --save-dev @types/react-slick @types/react-transition-group

echo "TypeScript setup complete!"
echo "Now you can run:"
echo "  npm run convert - to convert JSX files to TSX"
echo "  npm run typecheck - to check for TypeScript errors"

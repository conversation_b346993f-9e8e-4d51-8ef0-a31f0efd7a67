#!/usr/bin/env node
/**
 * Copy Assets Script
 * 
 * This script copies static assets from the public directory to the dist directory
 * during the build process. This ensures that all required files are available in
 * the production build.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');
const publicDir = path.join(rootDir, 'public');
const distDir = path.join(rootDir, 'dist');

// Function to create directory if it doesn't exist
function ensureDirectoryExists(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

// Function to copy a directory recursively
function copyDirectory(source, destination) {
  // Create destination directory if it doesn't exist
  ensureDirectoryExists(destination);
  
  // Get all files in the source directory
  const files = fs.readdirSync(source);
  
  // Copy each file/directory
  files.forEach(file => {
    const sourcePath = path.join(source, file);
    const destPath = path.join(destination, file);
    
    // Check if it's a file or directory
    const stats = fs.statSync(sourcePath);
    
    if (stats.isDirectory()) {
      // Recursively copy directory
      copyDirectory(sourcePath, destPath);
    } else {
      // Copy file
      fs.copyFileSync(sourcePath, destPath);
      console.log(`Copied: ${sourcePath} → ${destPath}`);
    }
  });
}

// Main function
function copyAssets() {
  if (!fs.existsSync(publicDir)) {
    console.log('Public directory not found. Nothing to copy.');
    return;
  }
  
  if (!fs.existsSync(distDir)) {
    console.log('Dist directory not found. Creating it...');
    ensureDirectoryExists(distDir);
  }
  
  console.log('Copying assets from public to dist...');
  copyDirectory(publicDir, distDir);
  
  // Specifically ensure aat directory exists if it's not in public
  const aatSourceDir = path.join(rootDir, 'aat');
  if (fs.existsSync(aatSourceDir)) {
    const aatDestDir = path.join(distDir, 'aat');
    console.log('Copying aat directory...');
    copyDirectory(aatSourceDir, aatDestDir);
  } else {
    console.log('aat directory not found in root. Skipping.');
  }
  
  console.log('Asset copying complete!');
}

// Run the script
copyAssets();

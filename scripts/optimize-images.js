#!/usr/bin/env node
/**
 * Image Optimization Script for Auctus
 * 
 * This script optimizes images in the project by:
 * 1. Compressing images
 * 2. Creating WebP versions
 * 3. Generating responsive sizes
 * 
 * Usage: node scripts/optimize-images.js [directory]
 * If no directory is specified, it processes all images in src/assets
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

// Check if sharp is installed
try {
  execSync('npm list sharp', { stdio: 'ignore' });
} catch (e) {
  console.log('Installing sharp for image processing...');
  execSync('npm install sharp --save-dev', { stdio: 'inherit' });
}

// Now import sharp
import sharp from 'sharp';

// Get the current directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');

// Default source directory is src/assets
const sourceDir = process.argv[2] 
  ? path.resolve(process.cwd(), process.argv[2])
  : path.join(rootDir, 'src', 'assets');

console.log(`Optimizing images in: ${sourceDir}`);

// Supported image types
const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif'];

// Responsive sizes to generate
const sizes = [640, 1024, 1600];

// Quality settings
const jpegQuality = 80;
const webpQuality = 75;
const pngQuality = 80;

// Function to find all images
function findImages(dir) {
  let results = [];
  
  try {
    const list = fs.readdirSync(dir);
    
    for (const file of list) {
      const filePath = path.join(dir, file);
      
      try {
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
          // Recurse into subdirectory
          results = results.concat(findImages(filePath));
        } else {
          const ext = path.extname(file).toLowerCase();
          if (imageExtensions.includes(ext)) {
            results.push(filePath);
          }
        }
      } catch (err) {
        console.error(`Error accessing ${filePath}:`, err.message);
      }
    }
  } catch (err) {
    console.error(`Error reading directory ${dir}:`, err.message);
  }
  
  return results;
}

// Function to create directory if it doesn't exist
function ensureDirectoryExists(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

// Function to optimize an image
async function optimizeImage(imagePath) {
  const fileExt = path.extname(imagePath).toLowerCase();
  const fileName = path.basename(imagePath, fileExt);
  const dirName = path.dirname(imagePath);
  
  // Create optimized directory
  const optimizedDir = path.join(dirName, 'optimized');
  ensureDirectoryExists(optimizedDir);
  
  // Get image metadata
  let metadata;
  try {
    metadata = await sharp(imagePath).metadata();
  } catch (err) {
    console.error(`Error processing ${imagePath}:`, err.message);
    return;
  }
  
  // Skip if image is already smaller than smallest size
  if (metadata.width < sizes[0]) {
    console.log(`Skipping ${imagePath} - already smaller than ${sizes[0]}px`);
    return;
  }
  
  // Process each size
  for (const size of sizes) {
    // Skip sizes larger than original
    if (size > metadata.width) continue;
    
    // Calculate height to maintain aspect ratio
    const height = Math.round(size * (metadata.height / metadata.width));
    
    try {
      // Original format (but optimized)
      const originalOutput = path.join(optimizedDir, `${fileName}-${size}${fileExt}`);
      
      if (fileExt === '.jpg' || fileExt === '.jpeg') {
        await sharp(imagePath)
          .resize(size, height)
          .jpeg({ quality: jpegQuality })
          .toFile(originalOutput);
      } else if (fileExt === '.png') {
        await sharp(imagePath)
          .resize(size, height)
          .png({ quality: pngQuality })
          .toFile(originalOutput);
      } else if (fileExt === '.gif') {
        // GIFs are tricky, just resize them
        await sharp(imagePath, { animated: true })
          .resize(size, height)
          .toFile(originalOutput);
      }
      
      // WebP version (usually better compression)
      const webpOutput = path.join(optimizedDir, `${fileName}-${size}.webp`);
      await sharp(imagePath)
        .resize(size, height)
        .webp({ quality: webpQuality })
        .toFile(webpOutput);
        
      console.log(`Processed: ${path.basename(originalOutput)} and ${path.basename(webpOutput)}`);
    } catch (err) {
      console.error(`Error optimizing ${imagePath} at size ${size}:`, err.message);
    }
  }
}

// Find and process all images
async function processAllImages() {
  const images = findImages(sourceDir);
  console.log(`Found ${images.length} images to process.`);
  
  if (images.length === 0) {
    console.log('No images found. Make sure the source directory exists and contains images.');
    return;
  }
  
  // Create a directory to document the image sizes
  const docsDir = path.join(rootDir, 'image-sizes');
  ensureDirectoryExists(docsDir);
  
  // Open a file to write image dimensions
  const docsFile = path.join(docsDir, 'image-dimensions.md');
  fs.writeFileSync(docsFile, '# Image Dimensions for Auctus Project\n\n');
  fs.appendFileSync(docsFile, 'This file documents the dimensions of images in the project to help with proper sizing in HTML/JSX.\n\n');
  
  // Process each image and document its dimensions
  for (const image of images) {
    try {
      // Get image metadata
      const metadata = await sharp(image).metadata();
      
      // Document the image dimensions
      const relativePath = path.relative(rootDir, image);
      fs.appendFileSync(docsFile, `## ${path.basename(image)}\n`);
      fs.appendFileSync(docsFile, `- Path: \`${relativePath}\`\n`);
      fs.appendFileSync(docsFile, `- Dimensions: ${metadata.width}×${metadata.height}\n`);
      fs.appendFileSync(docsFile, `- Size: ${(fs.statSync(image).size / 1024).toFixed(2)} KB\n`);
      fs.appendFileSync(docsFile, `- Type: ${metadata.format}\n`);
      
      // JSX example
      fs.appendFileSync(docsFile, `\n### JSX Implementation Example:\n`);
      fs.appendFileSync(docsFile, '```jsx\n');
      fs.appendFileSync(docsFile, `<img\n  src="/${relativePath}"\n  width="${metadata.width}"\n  height="${metadata.height}"\n  alt="Description of the image"\n/>\n`);
      
      // Add responsive example if the image is large
      if (metadata.width > 800) {
        fs.appendFileSync(docsFile, `\n// Responsive version:\n`);
        const sizesString = sizes.filter(size => size < metadata.width).map(size => `${size}w`).join(', ');
        if (sizesString) {
          fs.appendFileSync(docsFile, `<img\n  src="/${relativePath}"\n  srcSet="/${path.dirname(relativePath)}/optimized/${path.basename(image, path.extname(image))}-640${path.extname(image)} 640w, /${path.dirname(relativePath)}/optimized/${path.basename(image, path.extname(image))}-1024${path.extname(image)} 1024w"\n  sizes="(max-width: 640px) 640px, 1024px"\n  width="${metadata.width}"\n  height="${metadata.height}"\n  alt="Description of the image"\n  loading="lazy"\n/>\n`);
        }
      }
      fs.appendFileSync(docsFile, '```\n\n');
      
      // Process the image for optimization
      await optimizeImage(image);
    } catch (err) {
      console.error(`Error processing ${image}:`, err.message);
    }
  }
  
  console.log(`\nImage optimization complete!`);
  console.log(`Image dimensions have been documented in: ${docsFile}`);
  console.log(`\nTo use these optimized images, update your image paths and add width/height attributes.`);
}

// Run the script
processAllImages().catch(err => {
  console.error('An error occurred during processing:', err);
});

#!/usr/bin/env node
// This script finds references to a specific file across the entire project

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');

// What to search for
const searchTerm = process.argv[2] || 'main.jsx';

console.log(`Searching for '${searchTerm}' in project files...`);

// Skip these directories
const dirsToSkip = ['node_modules', '.git', 'dist', 'build', '.vscode', '.idea'];

// Extensions to search in
const extensionsToSearch = [
  '.js', '.jsx', '.ts', '.tsx', '.html', '.css', '.scss', 
  '.json', '.md', '.txt', '.svg', '.xml', '.yml', '.yaml'
];

// Function to find all files with given extensions
function findFiles(dir) {
  let results = [];
  
  try {
    const list = fs.readdirSync(dir);
    
    list.forEach(file => {
      const filePath = path.join(dir, file);
      
      try {
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
          // Skip directories we don't want to search
          if (!dirsToSkip.includes(file)) {
            results = results.concat(findFiles(filePath));
          }
        } else {
          const ext = path.extname(file).toLowerCase();
          if (extensionsToSearch.includes(ext)) {
            results.push(filePath);
          }
        }
      } catch (err) {
        console.error(`Error accessing ${filePath}:`, err.message);
      }
    });
  } catch (err) {
    console.error(`Error reading directory ${dir}:`, err.message);
  }
  
  return results;
}

// Find all files to search in
const filesToSearch = findFiles(rootDir);
console.log(`Found ${filesToSearch.length} files to search in.`);

// Search for the term
const matchingFiles = [];

filesToSearch.forEach(file => {
  try {
    // Only read files that are not too large
    const stat = fs.statSync(file);
    const MAX_SIZE = 5 * 1024 * 1024; // 5MB
    
    if (stat.size <= MAX_SIZE) {
      const content = fs.readFileSync(file, 'utf8');
      
      if (content.includes(searchTerm)) {
        // Get the line numbers and context
        const lines = content.split('\n');
        const matches = [];
        
        lines.forEach((line, i) => {
          if (line.includes(searchTerm)) {
            matches.push({
              lineNumber: i + 1,
              line: line.trim()
            });
          }
        });
        
        matchingFiles.push({
          file: path.relative(rootDir, file),
          matches
        });
      }
    }
  } catch (err) {
    console.error(`Error reading ${file}:`, err.message);
  }
});

// Report results
if (matchingFiles.length > 0) {
  console.log(`\nFound '${searchTerm}' in ${matchingFiles.length} files:`);
  
  matchingFiles.forEach(item => {
    console.log(`\n${item.file}:`);
    item.matches.forEach(match => {
      console.log(`  Line ${match.lineNumber}: ${match.line}`);
    });
  });
} else {
  console.log(`\nNo references to '${searchTerm}' found.`);
}

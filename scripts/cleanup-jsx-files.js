#!/usr/bin/env node
// This script finds all .jsx files that have corresponding .tsx files
// and generates commands to remove them

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');
const srcDir = path.join(rootDir, 'src');

console.log('Scanning for JSX files with TSX equivalents...');

// Function to find all JSX files
function findFiles(dir, extension) {
  let results = [];
  
  try {
    const list = fs.readdirSync(dir);
    
    list.forEach(file => {
      const filePath = path.join(dir, file);
      
      try {
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory() && file !== 'node_modules') {
          // Recurse into subdirectory
          results = results.concat(findFiles(filePath, extension));
        } else if (file.endsWith(extension)) {
          results.push(filePath);
        }
      } catch (err) {
        console.error(`Error accessing ${filePath}:`, err.message);
      }
    });
  } catch (err) {
    console.error(`Error reading directory ${dir}:`, err.message);
  }
  
  return results;
}

// Find all .jsx files
const jsxFiles = findFiles(srcDir, '.jsx');
console.log(`Found ${jsxFiles.length} JSX files.`);

// Check which ones have corresponding .tsx files
const filesToRemove = [];
const filesToKeep = [];

jsxFiles.forEach(jsxFile => {
  const tsxFile = jsxFile.replace('.jsx', '.tsx');
  
  if (fs.existsSync(tsxFile)) {
    filesToRemove.push(jsxFile);
  } else {
    filesToKeep.push(jsxFile);
  }
});

console.log(`Found ${filesToRemove.length} JSX files with TSX equivalents.`);

if (filesToRemove.length > 0) {
  console.log('\nJSX files that can be safely removed:');
  filesToRemove.forEach(file => {
    console.log(`- ${file}`);
  });
  
  // Generate removal script
  const removeScript = `#!/bin/bash
# This script removes JSX files that have corresponding TSX files
# Generated on ${new Date().toLocaleDateString()}

# Remove individual files
${filesToRemove.map(file => `rm "${file}"`).join('\n')}

echo "Removed ${filesToRemove.length} JSX files."
`;

  const scriptPath = path.join(rootDir, 'scripts', 'remove-jsx-files.sh');
  fs.writeFileSync(scriptPath, removeScript);
  fs.chmodSync(scriptPath, 0o755); // Make executable
  
  console.log(`\nRemoval script generated at: ${scriptPath}`);
  console.log('To remove the JSX files, run:');
  console.log('  ./scripts/remove-jsx-files.sh');
}

if (filesToKeep.length > 0) {
  console.log('\nJSX files without TSX equivalents (should be converted first):');
  filesToKeep.forEach(file => {
    console.log(`- ${file}`);
  });
}

console.log('\nAdditional steps:');
console.log('1. Update any imports in your codebase to use .ts/.tsx files instead of .js/.jsx');
console.log('2. Update your vite.config.ts to ensure it handles both .ts and .tsx files correctly');
console.log('3. Run "npm run typecheck" and "npm run dev" to verify everything still works after cleanup');

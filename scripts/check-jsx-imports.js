#!/usr/bin/env node
// This script scans all .ts and .tsx files for imports that still reference .jsx files

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');
const srcDir = path.join(rootDir, 'src');

console.log('Scanning for .jsx imports in TypeScript files...');

// Function to find all TS files
function findFiles(dir, extensions) {
  let results = [];
  
  try {
    const list = fs.readdirSync(dir);
    
    list.forEach(file => {
      const filePath = path.join(dir, file);
      
      try {
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory() && file !== 'node_modules') {
          // Recurse into subdirectory
          results = results.concat(findFiles(filePath, extensions));
        } else if (extensions.some(ext => file.endsWith(ext))) {
          results.push(filePath);
        }
      } catch (err) {
        console.error(`Error accessing ${filePath}:`, err.message);
      }
    });
  } catch (err) {
    console.error(`Error reading directory ${dir}:`, err.message);
  }
  
  return results;
}

// Find all .ts and .tsx files
const tsFiles = findFiles(srcDir, ['.ts', '.tsx']);
console.log(`Found ${tsFiles.length} TypeScript files to check.`);

// Check for .jsx imports
const problematicFiles = [];

tsFiles.forEach(file => {
  try {
    const content = fs.readFileSync(file, 'utf8');
    const jsxImportRegex = /from\s+['"](.+)\.jsx['"]/g;
    const matches = content.match(jsxImportRegex);
    
    if (matches && matches.length > 0) {
      problematicFiles.push({
        file,
        matches
      });
    }
  } catch (err) {
    console.error(`Error reading ${file}:`, err.message);
  }
});

if (problematicFiles.length > 0) {
  console.log('\nFound .jsx references in the following files:');
  problematicFiles.forEach(item => {
    console.log(`\n${item.file}:`);
    item.matches.forEach(match => {
      console.log(`  - ${match}`);
    });
  });
  
  console.log('\nRecommended actions:');
  console.log('1. Update these imports to remove the .jsx extension');
  console.log('2. Example: from "./Component.jsx" → from "./Component"');
} else {
  console.log('\nNo .jsx references found in TypeScript files. Great job!');
}

// Also check for potential .js imports (for utilities, services, etc.)
const jsImportRegex = /from\s+['"](.+)\.js['"]/g;
const jsImportFiles = [];

tsFiles.forEach(file => {
  try {
    const content = fs.readFileSync(file, 'utf8');
    const matches = content.match(jsImportRegex);
    
    if (matches && matches.length > 0) {
      jsImportFiles.push({
        file,
        matches
      });
    }
  } catch (err) {
    console.error(`Error reading ${file}:`, err.message);
  }
});

if (jsImportFiles.length > 0) {
  console.log('\nFound .js references in TypeScript files:');
  jsImportFiles.forEach(item => {
    console.log(`\n${item.file}:`);
    item.matches.forEach(match => {
      console.log(`  - ${match}`);
    });
  });
  
  console.log('\nConsider:');
  console.log('1. Converting these .js files to .ts');
  console.log('2. Updating imports to remove the .js extension');
}

console.log('\nRemember to also check your index.html for any script references that might still point to .jsx files.');

#!/usr/bin/env node
/**
 * Large Image Optimizer for Auctus
 * 
 * This script targets specific large images identified in the build output
 * and creates optimized versions of them.
 * 
 * Usage: 
 * - Default mode: node scripts/optimize-large-images.js
 *   (Creates optimized versions in an 'optimized' subdirectory)
 * 
 * - Direct replacement mode: node scripts/optimize-large-images.js --replace
 *   (Makes backups of originals and replaces them with optimized versions)
 * 
 * - WebP conversion mode: node scripts/optimize-large-images.js --webp
 *   (Creates WebP versions alongside originals without replacing them)
 * 
 * - Combined mode: node scripts/optimize-large-images.js --replace --webp
 *   (Replaces originals and adds WebP versions alongside them)
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

// Check command line arguments
const replaceOriginals = process.argv.includes('--replace');
const createWebP = process.argv.includes('--webp');

// Check if sharp is installed
try {
  execSync('npm list sharp', { stdio: 'ignore' });
} catch (e) {
  console.log('Installing sharp for image processing...');
  execSync('npm install sharp --save-dev', { stdio: 'inherit' });
}

// Now import sharp
import sharp from 'sharp';

// Get the current directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');
const srcDir = path.join(rootDir, 'src');

// List of large images to optimize specifically
const largeImages = [
  'Artboard 1 copy <EMAIL>',
  'Artboard 1 copy <EMAIL>',
  'AppDesign.jpg',
  'Background.png',
  'Tee.png',
  'WebDevelopment.jpg',
  'GraphicDesign.jpg'
];

// Function to find specific images
async function findLargeImages() {
  const results = [];
  
  // Helper function to recursively search directories
  async function searchDirectory(dir) {
    const files = fs.readdirSync(dir);
    
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        await searchDirectory(filePath);
      } else if (largeImages.some(largeName => file.includes(largeName))) {
        results.push(filePath);
      }
    }
  }
  
  // Start search from src directory
  await searchDirectory(srcDir);
  return results;
}

// Function to create directory if it doesn't exist
function ensureDirectoryExists(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

// Function to optimize a large image
async function optimizeLargeImage(imagePath) {
  const fileExt = path.extname(imagePath).toLowerCase();
  const fileName = path.basename(imagePath, fileExt);
  const dirName = path.dirname(imagePath);
  
  // Create output directory
  let outputDir, outputFile;
  
  if (replaceOriginals) {
    // Create backups directory
    const backupDir = path.join(dirName, 'backups');
    ensureDirectoryExists(backupDir);
    
    // Create backup of original
    const backupFile = path.join(backupDir, `${fileName}${fileExt}`);
    fs.copyFileSync(imagePath, backupFile);
    
    // Output to original location
    outputDir = dirName;
    outputFile = path.join(outputDir, `${fileName}${fileExt}`);
  } else {
    // Create optimized directory
    outputDir = path.join(dirName, 'optimized');
    ensureDirectoryExists(outputDir);
    outputFile = path.join(outputDir, `${fileName}${fileExt}`);
  }
  
  // WebP output file
  const webpOutputFile = path.join(dirName, `${fileName}.webp`);
  
  // Get image metadata
  let metadata;
  try {
    metadata = await sharp(imagePath).metadata();
    console.log(`Processing ${fileName}${fileExt} (${metadata.width}x${metadata.height}, ${(fs.statSync(imagePath).size / 1024 / 1024).toFixed(2)}MB)`);
  } catch (err) {
    console.error(`Error processing ${imagePath}:`, err.message);
    return;
  }
  
  try {
    // Determine optimal size - reduce by 50% if very large
    const targetWidth = metadata.width > 2000 ? Math.round(metadata.width * 0.5) : metadata.width;
    const targetHeight = Math.round(targetWidth * (metadata.height / metadata.width));
    
    // Optimize the original format
    if (fileExt === '.webp') {
      await sharp(imagePath)
        .resize(targetWidth, targetHeight)
        .webp({ quality: 75, effort: 6 })
        .toFile(outputFile + '.tmp');
    }
    else if (fileExt === '.jpg' || fileExt === '.jpeg') {
      await sharp(imagePath)
        .resize(targetWidth, targetHeight)
        .jpeg({ quality: 80, progressive: true })
        .toFile(outputFile + '.tmp');
    }
    else if (fileExt === '.png') {
      await sharp(imagePath)
        .resize(targetWidth, targetHeight)
        .png({ quality: 80, progressive: true, compressionLevel: 9 })
        .toFile(outputFile + '.tmp');
    }
    
    // Move the temp file to the target location (avoids issues with overwriting)
    fs.renameSync(outputFile + '.tmp', outputFile);
    
    // Create WebP version if requested
    if (createWebP && fileExt !== '.webp') {
      await sharp(imagePath)
        .resize(targetWidth, targetHeight)
        .webp({ quality: 75, effort: 6 })
        .toFile(webpOutputFile);
    }
      
    // Calculate savings
    const originalSize = fs.statSync(replaceOriginals ? path.join(dirName, 'backups', `${fileName}${fileExt}`) : imagePath).size / 1024;
    const optimizedSize = fs.statSync(outputFile).size / 1024;
    
    console.log(`  Original: ${originalSize.toFixed(2)} KB`);
    console.log(`  Optimized: ${optimizedSize.toFixed(2)} KB (${((1 - optimizedSize/originalSize) * 100).toFixed(2)}% reduction)`);
    
    if (createWebP && fileExt !== '.webp') {
      const webpSize = fs.statSync(webpOutputFile).size / 1024;
      console.log(`  WebP version: ${webpSize.toFixed(2)} KB (${((1 - webpSize/originalSize) * 100).toFixed(2)}% reduction)`);
    }
    
    if (replaceOriginals) {
      console.log(`  ✅ Original file replaced. Backup saved to: ${path.join('backups', fileName + fileExt)}`);
      
      if (createWebP && fileExt !== '.webp') {
        console.log(`  ✅ WebP version created alongside original: ${fileName}.webp`);
        
        // Show implementation example
        console.log(`\n  Implementation example for using both formats:`);
        console.log(`  <picture>`);
        console.log(`    <source srcset="${fileName}.webp" type="image/webp" />`);
        console.log(`    <img src="${fileName}${fileExt}" width="${targetWidth}" height="${targetHeight}" alt="Description" />`);
        console.log(`  </picture>\n`);
      }
    } else {
      // Generate HTML implementation example
      console.log(`\n  Implementation example:`);
      console.log(`  <img src="/${path.relative(rootDir, outputFile)}" width="${targetWidth}" height="${targetHeight}" alt="Description" />`);
      
      // Generate responsive implementation example if WebP is created
      if (!replaceOriginals && fileExt !== '.webp') {
        const webpOutput = path.join(outputDir, `${fileName}.webp`);
        if (fs.existsSync(webpOutput)) {
          console.log(`\n  Responsive implementation example:`);
          console.log(`  <picture>`);
          console.log(`    <source srcset="/${path.relative(rootDir, webpOutput)}" type="image/webp" />`);
          console.log(`    <source srcset="/${path.relative(rootDir, outputFile)}" type="${fileExt === '.jpg' || fileExt === '.jpeg' ? 'image/jpeg' : 'image/png'}" />`);
          console.log(`    <img src="/${path.relative(rootDir, outputFile)}" width="${targetWidth}" height="${targetHeight}" alt="Description" />`);
          console.log(`  </picture>\n`);
        }
      }
    }
    
  } catch (err) {
    console.error(`Error optimizing ${imagePath}:`, err.message);
  }
}

// Main function
async function optimizeLargeImages() {
  let modeDescription = '';
  if (replaceOriginals && createWebP) {
    modeDescription = 'REPLACE + WEBP mode - originals will be replaced and WebP versions created alongside them';
  } else if (replaceOriginals) {
    modeDescription = 'REPLACE mode - original files will be replaced (with backups)';
  } else if (createWebP) {
    modeDescription = 'WEBP mode - WebP versions will be created alongside originals';
  } else {
    modeDescription = 'standard mode - optimized versions will be created in "optimized" subdirectories';
  }

  console.log('Searching for large images...');
  const images = await findLargeImages();
  
  if (images.length === 0) {
    console.log('No large images found. Check if the path is correct or if the images have been renamed.');
    return;
  }
  
  console.log(`Found ${images.length} large images to optimize.`);
  console.log(`⚠️ Running in ${modeDescription}`);
  
  // Process each image
  for (const image of images) {
    await optimizeLargeImage(image);
  }
  
  console.log('\nImage optimization complete!');
  
  if (replaceOriginals) {
    console.log('All original images have been replaced with optimized versions.');
    console.log('Backups of the original files are stored in "backups" directories.');
    
    if (createWebP) {
      console.log('\n✅ WebP versions have been created alongside your original images.');
      console.log('\nTo use both formats for maximum compatibility and performance:');
      console.log('\n1. Import both formats in your component:');
      console.log('   import myImage from \'@assets/images/myImage.jpg\';');
      console.log('   import myImageWebP from \'@assets/images/myImage.webp\';');
      console.log('\n2. Use the <picture> element:');
      console.log('   <picture>');
      console.log('     <source srcSet={myImageWebP} type="image/webp" />');
      console.log('     <img src={myImage} alt="Description" width="800" height="600" />');
      console.log('   </picture>');
    }
  } else {
    console.log('To use these optimized images:');
    console.log('1. Update your image paths to point to the optimized versions');
    console.log('2. Add width and height attributes to all <img> tags');
    console.log('3. Consider using <picture> elements with WebP sources for better performance');
    console.log('\nTip: Run this script with --replace to directly replace the original files:');
    console.log('  npm run optimize-images:replace');
    console.log('\nTip: Run with both --replace and --webp for the best of both worlds:');
    console.log('  npm run optimize-images -- --replace --webp');
  }
}

// Run the script
optimizeLargeImages().catch(err => {
  console.error('An error occurred during processing:', err);
});

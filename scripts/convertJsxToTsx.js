// Convert JSX files to TSX files
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');

// Function to find all JSX files
function findFiles(dir, extension) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && file !== "node_modules") {
      // Recurse into subdirectory
      results = results.concat(findFiles(filePath, extension));
    } else if (file.endsWith(extension)) {
      results.push(filePath);
    }
  });
  
  return results;
}

// Find all .jsx files
const jsxFiles = findFiles(path.join(rootDir, "src"), ".jsx");

console.log(`Found ${jsxFiles.length} JSX files to convert.`);

// Process them one by one
jsxFiles.forEach(file => {
  const tsxFile = file.replace(".jsx", ".tsx");
  let contents = fs.readFileSync(file, "utf8");
  
  // Add React import if missing
  if (!contents.includes("import React")) {
    contents = `\n${contents}`;
  }
  
  // Fix extension references in imports
  contents = contents.replace(/from\s+["'](.+)\.jsx["']/g, 'from "$1"');
  
  // Convert absolute imports to relative or alias
  contents = contents.replace(/from\s+["']\/src\//g, 'from "@');
  
  console.log(`Converting ${file} to ${tsxFile}`);
  fs.writeFileSync(tsxFile, contents);
});

console.log("Conversion completed!");

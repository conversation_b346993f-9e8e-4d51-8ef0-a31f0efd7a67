#!/usr/bin/env node
// Convert JSX files to TSX files
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');
const srcDir = path.join(rootDir, 'src');

console.log('Starting JSX to TSX conversion...');
console.log(`Scanning directory: ${srcDir}`);

// Function to find all JSX files
function findFiles(dir, extension) {
  let results = [];
  
  try {
    const list = fs.readdirSync(dir);
    
    list.forEach(file => {
      const filePath = path.join(dir, file);
      
      try {
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory() && file !== 'node_modules') {
          // Recurse into subdirectory
          results = results.concat(findFiles(filePath, extension));
        } else if (file.endsWith(extension)) {
          results.push(filePath);
        }
      } catch (err) {
        console.error(`Error accessing ${filePath}:`, err.message);
      }
    });
  } catch (err) {
    console.error(`Error reading directory ${dir}:`, err.message);
  }
  
  return results;
}

// Find all .jsx files
const jsxFiles = findFiles(srcDir, '.jsx');

console.log(`Found ${jsxFiles.length} JSX files to convert.`);

if (jsxFiles.length === 0) {
  console.log('No JSX files found. Exiting...');
  process.exit(0);
}

// Count of successfully converted files
let successCount = 0;

// Process them one by one
jsxFiles.forEach(file => {
  const tsxFile = file.replace('.jsx', '.tsx');
  
  try {
    let contents = fs.readFileSync(file, 'utf8');
    
    // Add React import if missing
    if (!contents.includes('import React')) {
      contents = `\n${contents}`;
    }
    
    // Fix extension references in imports (.jsx)
    contents = contents.replace(/from\s+["'](.+)\.jsx["']/g, 'from "$1"');
    
    // Convert absolute imports to use alias
    contents = contents.replace(/from\s+["']\/src\/components\//g, 'from "@components/');
    contents = contents.replace(/from\s+["']\/src\/pages\//g, 'from "@pages/');
    contents = contents.replace(/from\s+["']\/src\/assets\//g, 'from "@assets/');
    contents = contents.replace(/from\s+["']\/src\//g, 'from "../');
    
    // Basic React component function type annotation
    contents = contents.replace(
      /function\s+([A-Za-z0-9_]+)\s*\(\s*(\{[^}]*\}|\w+)\s*\)\s*\{/g,
      'function $1($2): JSX.Element {'
    );
    
    // Basic arrow function type annotation for components
    contents = contents.replace(
      /const\s+([A-Za-z0-9_]+)\s*=\s*\(\s*(\{[^}]*\}|\w+)\s*\)\s*=>\s*\{/g,
      'const $1 = ($2): JSX.Element => {'
    );
    
    // PropTypes to interface conversion (simplified, will need manual cleanup)
    if (contents.includes('PropTypes')) {
      // Extract component name
      const componentNameMatch = contents.match(/function\s+([A-Za-z0-9_]+)|const\s+([A-Za-z0-9_]+)\s*=/);
      const componentName = componentNameMatch ? (componentNameMatch[1] || componentNameMatch[2]) : null;
      
      if (componentName) {
        // Add TypeScript interface before the component
        contents = contents.replace(
          new RegExp(`(import[\\s\\S]*?;\\s*)(function\\s+${componentName}|const\\s+${componentName}\\s*=)`),
          `$1\ninterface ${componentName}Props {\n  // TODO: Convert PropTypes to TypeScript interface\n}\n\n$2`
        );
      }
    }
    
    console.log(`Converting ${file} to ${tsxFile}`);
    fs.writeFileSync(tsxFile, contents);
    successCount++;
  } catch (err) {
    console.error(`Error converting ${file}:`, err.message);
  }
});

console.log(`Conversion completed! Successfully converted ${successCount} of ${jsxFiles.length} files.`);
console.log('Next steps:');
console.log('1. Run "npm run typecheck" to see TypeScript errors');
console.log('2. Fix any TypeScript errors in the converted files');
console.log('3. Add proper type annotations to props and state');

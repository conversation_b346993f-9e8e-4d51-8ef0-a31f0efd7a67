# Build Error Fix: Removing Unused Script

## The Issue

The build was failing with this error:

```
error during build:
[vite]: Rollup failed to resolve import "/aat/dist/aat.js" from "/Users/<USER>/Development/Claude/Auctus/Auctus-Site/index.html".
```

## The Solution

Since you confirmed that the `aat.js` script is not in use, we've simply removed it from your project:

1. Removed the script tag from `index.html`:
   ```html
   <!-- Removed -->
   <script defer src="/aat/dist/aat.js"></script>
   ```

2. Removed the external declaration from `vite.config.ts`:
   ```typescript
   // Removed
   external: ['/aat/dist/aat.js'],
   ```

## How to Build

You can now build your project normally:

```bash
npm run build
```

The build should complete without the previous error.

## Understanding Why This Fixes the Issue

Vite was trying to find and bundle the `aat.js` script during the build process, but since the file doesn't exist or is in a location Vite can't access, it was causing a build failure. By removing the reference to this unused script, we eliminate the error.

## Best Practices for External Scripts

When adding external scripts to your project:

1. Only include scripts that are actually used
2. For third-party scripts, consider using:
   - `defer` attribute for non-critical scripts
   - Dynamic loading for analytics and tracking scripts
3. Add appropriate `preconnect` hints for domains hosting external scripts

## Next Steps

With this fix in place, I recommend:

1. Run a build to verify it succeeds: `npm run build`
2. Test your site's functionality: `npm run preview`
3. Proceed with the image optimization we set up earlier: `npm run optimize-images:both`

# Image Dimensions for Auctus Project

This file documents the dimensions of images in the project to help with proper sizing in HTML/JSX.

## AppDesign-640.webp
- Path: `src/assets/AppDesign-640.webp`
- Dimensions: 4592×3448
- Size: 1373.96 KB
- Type: jpeg

### JSX Implementation Example:
```jsx
<img
  src="/src/assets/AppDesign-640.webp"
  width="4592"
  height="3448"
  alt="Description of the image"
/>

// Responsive version:
<img
  src="/src/assets/AppDesign-640.webp"
  srcSet="/src/assets/optimized/AppDesign-640.jpg 640w, /src/assets/optimized/AppDesign-1024.jpg 1024w"
  sizes="(max-width: 640px) 640px, 1024px"
  width="4592"
  height="3448"
  alt="Description of the image"
  loading="lazy"
/>
```

## Automation.jpg
- Path: `src/assets/Automation.jpg`
- Dimensions: 1280×854
- Size: 142.26 KB
- Type: jpeg

### JSX Implementation Example:
```jsx
<img
  src="/src/assets/Automation.jpg"
  width="1280"
  height="854"
  alt="Description of the image"
/>

// Responsive version:
<img
  src="/src/assets/Automation.jpg"
  srcSet="/src/assets/optimized/Automation-640.jpg 640w, /src/assets/optimized/Automation-1024.jpg 1024w"
  sizes="(max-width: 640px) 640px, 1024px"
  width="1280"
  height="854"
  alt="Description of the image"
  loading="lazy"
/>
```

## Background.png
- Path: `src/assets/Background.png`
- Dimensions: 1367×848
- Size: 1286.37 KB
- Type: png

### JSX Implementation Example:
```jsx
<img
  src="/src/assets/Background.png"
  width="1367"
  height="848"
  alt="Description of the image"
/>

// Responsive version:
<img
  src="/src/assets/Background.png"
  srcSet="/src/assets/optimized/Background-640.png 640w, /src/assets/optimized/Background-1024.png 1024w"
  sizes="(max-width: 640px) 640px, 1024px"
  width="1367"
  height="848"
  alt="Description of the image"
  loading="lazy"
/>
```

## Deffz.png
- Path: `src/assets/Deffz.png`
- Dimensions: 850×2048
- Size: 1119.18 KB
- Type: png

### JSX Implementation Example:
```jsx
<img
  src="/src/assets/Deffz.png"
  width="850"
  height="2048"
  alt="Description of the image"
/>

// Responsive version:
<img
  src="/src/assets/Deffz.png"
  srcSet="/src/assets/optimized/Deffz-640.png 640w, /src/assets/optimized/Deffz-1024.png 1024w"
  sizes="(max-width: 640px) 640px, 1024px"
  width="850"
  height="2048"
  alt="Description of the image"
  loading="lazy"
/>
```

## Dev_icons.png
- Path: `src/assets/Dev_icons.png`
- Dimensions: 5260×1518
- Size: 189.88 KB
- Type: png

### JSX Implementation Example:
```jsx
<img
  src="/src/assets/Dev_icons.png"
  width="5260"
  height="1518"
  alt="Description of the image"
/>

// Responsive version:
<img
  src="/src/assets/Dev_icons.png"
  srcSet="/src/assets/optimized/Dev_icons-640.png 640w, /src/assets/optimized/Dev_icons-1024.png 1024w"
  sizes="(max-width: 640px) 640px, 1024px"
  width="5260"
  height="1518"
  alt="Description of the image"
  loading="lazy"
/>
```

## Emails.jpg
- Path: `src/assets/Emails.jpg`
- Dimensions: 1280×853
- Size: 247.08 KB
- Type: jpeg

### JSX Implementation Example:
```jsx
<img
  src="/src/assets/Emails.jpg"
  width="1280"
  height="853"
  alt="Description of the image"
/>

// Responsive version:
<img
  src="/src/assets/Emails.jpg"
  srcSet="/src/assets/optimized/Emails-640.jpg 640w, /src/assets/optimized/Emails-1024.jpg 1024w"
  sizes="(max-width: 640px) 640px, 1024px"
  width="1280"
  height="853"
  alt="Description of the image"
  loading="lazy"
/>
```

## Foreground.png
- Path: `src/assets/Foreground.png`
- Dimensions: 1367×768
- Size: 116.29 KB
- Type: png

### JSX Implementation Example:
```jsx
<img
  src="/src/assets/Foreground.png"
  width="1367"
  height="768"
  alt="Description of the image"
/>

// Responsive version:
<img
  src="/src/assets/Foreground.png"
  srcSet="/src/assets/optimized/Foreground-640.png 640w, /src/assets/optimized/Foreground-1024.png 1024w"
  sizes="(max-width: 640px) 640px, 1024px"
  width="1367"
  height="768"
  alt="Description of the image"
  loading="lazy"
/>
```

## GraphicDesign.jpg
- Path: `src/assets/GraphicDesign.jpg`
- Dimensions: 3024×4032
- Size: 836.44 KB
- Type: jpeg

### JSX Implementation Example:
```jsx
<img
  src="/src/assets/GraphicDesign.jpg"
  width="3024"
  height="4032"
  alt="Description of the image"
/>

// Responsive version:
<img
  src="/src/assets/GraphicDesign.jpg"
  srcSet="/src/assets/optimized/GraphicDesign-640.jpg 640w, /src/assets/optimized/GraphicDesign-1024.jpg 1024w"
  sizes="(max-width: 640px) 640px, 1024px"
  width="3024"
  height="4032"
  alt="Description of the image"
  loading="lazy"
/>
```

## Marketing.jpg
- Path: `src/assets/Marketing.jpg`
- Dimensions: 2000×1334
- Size: 337.35 KB
- Type: jpeg

### JSX Implementation Example:
```jsx
<img
  src="/src/assets/Marketing.jpg"
  width="2000"
  height="1334"
  alt="Description of the image"
/>

// Responsive version:
<img
  src="/src/assets/Marketing.jpg"
  srcSet="/src/assets/optimized/Marketing-640.jpg 640w, /src/assets/optimized/Marketing-1024.jpg 1024w"
  sizes="(max-width: 640px) 640px, 1024px"
  width="2000"
  height="1334"
  alt="Description of the image"
  loading="lazy"
/>
```

## Tee.png
- Path: `src/assets/Tee.png`
- Dimensions: 930×2048
- Size: 1249.29 KB
- Type: png

### JSX Implementation Example:
```jsx
<img
  src="/src/assets/Tee.png"
  width="930"
  height="2048"
  alt="Description of the image"
/>

// Responsive version:
<img
  src="/src/assets/Tee.png"
  srcSet="/src/assets/optimized/Tee-640.png 640w, /src/assets/optimized/Tee-1024.png 1024w"
  sizes="(max-width: 640px) 640px, 1024px"
  width="930"
  height="2048"
  alt="Description of the image"
  loading="lazy"
/>
```

## WebDevelopment.jpg
- Path: `src/assets/WebDevelopment.jpg`
- Dimensions: 4288×2848
- Size: 1169.88 KB
- Type: jpeg

### JSX Implementation Example:
```jsx
<img
  src="/src/assets/WebDevelopment.jpg"
  width="4288"
  height="2848"
  alt="Description of the image"
/>

// Responsive version:
<img
  src="/src/assets/WebDevelopment.jpg"
  srcSet="/src/assets/optimized/WebDevelopment-640.jpg 640w, /src/assets/optimized/WebDevelopment-1024.jpg 1024w"
  sizes="(max-width: 640px) 640px, 1024px"
  width="4288"
  height="2848"
  alt="Description of the image"
  loading="lazy"
/>
```

## sculpt.png
- Path: `src/assets/sculpt.png`
- Dimensions: 467×491
- Size: 76.87 KB
- Type: png

### JSX Implementation Example:
```jsx
<img
  src="/src/assets/sculpt.png"
  width="467"
  height="491"
  alt="Description of the image"
/>
```


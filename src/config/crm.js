// CRM API Configuration
export const CRM_CONFIG = {
  // API Base URL - In Vite, use import.meta.env instead of process.env
  API_URL: import.meta.env.VITE_CRM_API_URL || 'http://192.168.0.11:3000/rest',
  
  // API Authentication
  API_TOKEN: import.meta.env.VITE_CRM_API_TOKEN || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIyMWQ0NmQ0ZS02YTU4LTRlODktYjdmZi1jMTcwZDI3MDgzNDYiLCJ0eXBlIjoiQVBJX0tFWSIsIndvcmtzcGFjZUlkIjoiMjFkNDZkNGUtNmE1OC00ZTg5LWI3ZmYtYzE3MGQyNzA4MzQ2IiwiaWF0IjoxNzQ1MDIwNzkzLCJleHAiOjQ4OTg2MjA3OTIsImp0aSI6IjUzNTM4YjVmLTFlZGYtNDYzYS1iZTY2LWI3YmUzYTc2MzAyZSJ9.vRWw_nkDdIdc-NDHDcPDa2byCCRDL8Lw5OFMpSmQ6DM',
  
  // Quotes endpoint
  QUOTES_ENDPOINT: '/quotes',
  
  // Default values for quotes
  QUOTE_DEFAULTS: {
    source: 'Website Form', // Text field
    status: 'New', // Text field
    toDo: false // Boolean field
  }
};

// Helper function to format phone numbers
export const formatPhoneNumber = (phone) => {
  // Remove any non-digit characters
  const cleaned = phone.replace(/\D/g, '');
  
  // Attempt to detect UK phone numbers
  if (cleaned.startsWith('44')) {
    return {
      countryCode: 'GB',
      callingCode: '+44',
      number: cleaned.substring(2)
    };
  } else if (cleaned.startsWith('0')) {
    return {
      countryCode: 'GB',
      callingCode: '+44',
      number: cleaned.substring(1)
    };
  }
  
  // Default fallback
  return {
    countryCode: '',
    callingCode: '',
    number: cleaned
  };
};

// Helper function to create the quote payload
export const createQuotePayload = (quizAnswers) => {
  // Check if we have valid quiz answers
  if (!quizAnswers || !quizAnswers.contactDetails) {
    console.error('Invalid quiz answers provided to createQuotePayload');
    return null;
  }

  try {
    // Extract contact details
    const [fullName = '', phoneNumber = ''] = quizAnswers.contactDetails.split('|');
    const [firstName = '', ...lastNameParts] = fullName.trim().split(' ');
    const lastName = lastNameParts.join(' ');
    
    const phoneData = formatPhoneNumber(phoneNumber);

    // Map budget to numeric values
    const budgetMap = {
      '£1,000 - £2,999': 2000,
      '£3,000 - £5,999': 4500,
      '£6,000 - £9,999': 8000,
      '£10,000+': 15000
    };

    // Map timeline to text values
    const timelineMap = {
      'As soon as possible': 'Urgent',
      'Within a month': '1 Month',
      'Within a few months': '3 Months'
    };

    // Format services array as comma-separated string
    const servicesString = Array.isArray(quizAnswers.services) 
      ? quizAnswers.services.join(', ') 
      : '';

    // Build context object with all the service-specific details
    const contextData = {
      services: quizAnswers.services,
      websiteType: quizAnswers.websiteType,
      appType: quizAnswers.appType,
      designType: quizAnswers.designType,
      automationType: quizAnswers.automationType,
      marketingType: quizAnswers.marketingType,
      emailType: quizAnswers.emailType,
      businessType: quizAnswers.businessType,
      existingWebsite: quizAnswers.existingWebsite,
      timeline: quizAnswers.timeline,
      budget: quizAnswers.budget,
      requirements: quizAnswers.requirements,
      email: quizAnswers.email,
      submittedAt: new Date().toISOString()
    };

    // Remove undefined values from context
    const cleanContext = Object.fromEntries(
      Object.entries(contextData).filter(([, v]) => v !== undefined)
    );

    // Build the CRM payload
    return {
      // Basic fields
      name: `Quote - ${firstName} ${lastName}`, // Text field
      status: CRM_CONFIG.QUOTE_DEFAULTS.status, // Text field
      source: CRM_CONFIG.QUOTE_DEFAULTS.source, // Text field
      toDo: CRM_CONFIG.QUOTE_DEFAULTS.toDo, // True/False field
      
      // Client information
      clientName: {
        firstName: firstName,
        lastName: lastName || ''
      },
      email: quizAnswers.email, // Email from the form
      contactNumber: {
        primaryPhoneNumber: phoneData.number,
        primaryPhoneCountryCode: phoneData.countryCode,
        primaryPhoneCallingCode: phoneData.callingCode,
        additionalPhones: []
      },
      
      // Project details
      services: servicesString, // Services as comma-separated string
      businessType: quizAnswers.businessType, // Business type
      hasExistingWebsite: quizAnswers.existingWebsite === 'Yes', // Boolean
      timeline: timelineMap[quizAnswers.timeline] || quizAnswers.timeline, // Timeline
      budget: budgetMap[quizAnswers.budget] || 0, // Budget as number
      requirements: quizAnswers.requirements, // Requirements text
      
      // Quote details
      quoteAmount: null, // Number field - to be filled by sales team
      
      // Dates
      quoteDate: new Date().toISOString(), // Date and Time field
      
      // Store the context with service-specific details
      context: JSON.stringify(cleanContext),
      
      // If website type is provided, store it in the websiteType field
      websiteType: quizAnswers.websiteType || null
    };
  } catch (error) {
    console.error('Error creating quote payload:', error);
    return null;
  }
};
import { useMediaQuery } from "react-responsive";
import { BrowserRouter as Router, Route, Routes, useLocation, useNavigationType } from "react-router-dom";
import { AnimatePresence } from "framer-motion";
import { useEffect } from "react";
import Navbar from "@components/Navbar/index";
import ParallaxHero from "@components/ParallaxHero/index";
import AnimatedCards from "@components/AnimatedCards/index";
import AnimatedCardsMobile from "@components/AnimatedCards/AnimatedCardsMobile";
import AboutUs from "@components/AboutUs/index";
import Footer from "@components/Footer/index";
import Newsletter from "@components/Newsletter/index";
import QuoteQuiz from "@components/QuoteQuiz/index";
import AppleCardsCarouselDemo from "@components/CardCarousel/index";
// Removed unused HomeBlog import
import Services from "./pages/services/Services";
// Removed unused PricingCard import
import PricingSection from "@components/PricingSection/index";
import BlogPost from "./pages/blog/[slug]";

// Import Portfolio Pages
import Portfolio from "./pages/portfolio/Portfolio";
import WebDevelopment from "./pages/portfolio/WebDevelopment";
import AppDevelopment from "./pages/portfolio/AppDevelopment";
import GraphicDesign from "./pages/portfolio/GraphicDesign";


// Import Blog Pages
import Blog from "@pages/blog/Blog";

// Import Services Pages
import WebDevelopment2 from "./pages/services/WebDevelopment";
import GraphicDesign2 from "./pages/services/GraphicDesign";
import AppDevelopment2 from "./pages/services/AppDevelopment";
import Automation from "./pages/services/Automation";
import Marketing from "./pages/services/Marketing";
import Email from "./pages/services/Email";
import PortfolioProject from "./pages/portfolio/graphic-design-projects/[slug]";
import PortfolioProjectApp from "./pages/portfolio/app-development-projects/[slug]";
import PortfolioProjectWeb from "./pages/portfolio/web-development-projects/[slug]";

// ScrollToTop component that handles navigation changes
function ScrollToTop() {
  const { pathname } = useLocation();
  const navigationType = useNavigationType();

  useEffect(() => {
    // Handle different types of navigation
    if (navigationType !== 'POP') {
      // Scroll to top with a small delay to ensure page has rendered
      setTimeout(() => {
        window.scrollTo(0, 0);
      }, 0);
    }
  }, [pathname, navigationType]);

  return null;
}

function AnimatedRoutes() {
  const location = useLocation();
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });

  // Define pricing plans
  const pricingPlans = [
    {
      plan: "Basic Package",
      price: "400",
      regularPrice: "700",
      description: "Perfect for small businesses getting started with their online presence",
      features: [
        {
          title: "Website Pages",
          description: "Up to 5 pages with 50 optimized images and media content included"
        },
        {
          title: "Forms & Security",
          description: "Contact form, 2 custom forms and SSL security certificate included"
        },
        {
          title: "Responsive Design",
          description: "Fully optimized for desktop, tablet and mobile devices with testing"
        },
        {
          title: "SEO Foundation",
          description: "Basic search engine optimization setup and configuration included"
        },
        {
          title: "Core Features",
          description: "Media gallery, sliders, booking system and blog section with setup"
        },
        {
          title: "Maintenance",
          description: "Bi-annual website maintenance and technical support included"
        }
      ],
      isPopular: false
    },
    {
      plan: "Premium Package",
      price: "800",
      regularPrice: "1200",
      description: "Advanced solution for businesses ready to expand their digital reach",
      features: [
        {
          title: "Website Pages",
          description: "Up to 12 pages with 100 optimized images and media content"
        },
        {
          title: "Forms & Security",
          description: "Contact form, 6 custom forms and SSL security certificate"
        },
        {
          title: "Responsive Design",
          description: "Fully optimized for desktop, tablet and mobile devices"
        },
        {
          title: "SEO Advanced",
          description: "Comprehensive search engine optimization and analytics"
        },
        {
          title: "Premium Features",
          description: "Advanced media, booking system, blog and member access"
        },
        {
          title: "Maintenance",
          description: "Quarterly website maintenance and priority support"
        }
      ],
      isPopular: true
    },
    {
      plan: "E-Commerce Package",
      price: "1500",
      regularPrice: "2000",
      description: "Complete solution for businesses ready to sell products online",
      features: [
        {
          title: "Website Pages",
          description: "Up to 12 pages with support for 70 product listings"
        },
        {
          title: "Store Features",
          description: "Product comparison, filtering and recommendation system"
        },
        {
          title: "Payment System",
          description: "Multiple payment gateways and shipping integrations"
        },
        {
          title: "Price Management",
          description: "Dynamic pricing, discounts and subscription options"
        },
        {
          title: "Premium Features",
          description: "Advanced media, booking system, blog and member access"
        },
        {
          title: "Maintenance",
          description: "Monthly website maintenance and priority support"
        }
      ],
      isPopular: false
    }
  ];

  return (
    <AnimatePresence mode="wait">
      <Routes location={location} key={location.pathname}>
        {/* Home Route */}
        <Route
          path="/"
          element={
            <div className="bg-sDarkGreen">
              <section id="home">
                <ParallaxHero />
              </section>

              <section id="about" className="bg-sDarkGreen">
                <AboutUs />
              </section>

              <section id="carousel" className="bg-sDarkGreen">
                <AppleCardsCarouselDemo />
              </section>

              <section id="cards" className="bg-[#121B1B]">
                {isMobile ? <AnimatedCardsMobile /> : <AnimatedCards />}
              </section>

              <section id="quote" className="bg-[#121B1B]">
                <QuoteQuiz />
              </section>

              <PricingSection pricingPlans={pricingPlans} />
            </div>
          }
        />

        {/* Portfolio Routes */}
        <Route path="/portfolio" element={<Portfolio />} />
        <Route path="/portfolio/web-development" element={<WebDevelopment />} />
        <Route path="/portfolio/app-development" element={<AppDevelopment />} />
        <Route path="/portfolio/graphic-design" element={<GraphicDesign />} />

        {/* ✅ Dynamic Route for Individual Graphic Design Projects */}
        <Route path="/portfolio/graphic-design-projects/:slug" element={<PortfolioProject />} />
        <Route path="/portfolio/app-development-projects/:slug" element={<PortfolioProjectApp />} />
        <Route path="/portfolio/web-development-projects/:slug" element={<PortfolioProjectWeb />} />

        {/* Blog Routes */}
        <Route path="/blog" element={<Blog />} />
        <Route path="/blog/:slug" element={<BlogPost />} />

        {/* Services Routes */}
        <Route path="/services" element={<Services />} />
        <Route path="/services/web-development" element={<WebDevelopment2 />} />
        <Route path="/services/graphic-design" element={<GraphicDesign2 />} />
        <Route path="/services/app-development" element={<AppDevelopment2 />} />
        <Route path="/services/automation" element={<Automation />} />
        <Route path="/services/marketing" element={<Marketing />} />
        <Route path="/services/email" element={<Email />} />
      </Routes>
    </AnimatePresence>
  );
}

function App() {
  return (
    <Router>
      <ScrollToTop /> {/* Updated ScrollToTop component */}
      <div>
        <Navbar /> {/* Fixed navbar at the top */}
        <AnimatedRoutes />
        <Newsletter />
        <Footer />
      </div>
    </Router>
  );
}

export default App;

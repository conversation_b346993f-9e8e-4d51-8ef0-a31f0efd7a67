// src/services/crmService.js

import { CRM_CONFIG } from '../config/crm';

export class CRMService {
  static async createQuote(quoteData) {
    const apiUrl = `${CRM_CONFIG.API_URL}${CRM_CONFIG.QUOTES_ENDPOINT}`;
    
    try {
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${CRM_CONFIG.API_TOKEN}`
        },
        body: JSON.stringify(quoteData),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to create quote: ${response.status} ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating quote:', error);
      throw error;
    }
  }

  static async getQuotes(params = {}) {
    const queryParams = new URLSearchParams(params).toString();
    const apiUrl = `${CRM_CONFIG.API_URL}${CRM_CONFIG.QUOTES_ENDPOINT}${queryParams ? `?${queryParams}` : ''}`;
    
    try {
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${CRM_CONFIG.API_TOKEN}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch quotes: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching quotes:', error);
      throw error;
    }
  }

  static async getQuoteById(id) {
    const apiUrl = `${CRM_CONFIG.API_URL}${CRM_CONFIG.QUOTES_ENDPOINT}/${id}`;
    
    try {
      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${CRM_CONFIG.API_TOKEN}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch quote: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching quote:', error);
      throw error;
    }
  }

  static async updateQuote(id, updateData) {
    const apiUrl = `${CRM_CONFIG.API_URL}${CRM_CONFIG.QUOTES_ENDPOINT}/${id}`;
    
    try {
      const response = await fetch(apiUrl, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${CRM_CONFIG.API_TOKEN}`
        },
        body: JSON.stringify(updateData),
      });

      if (!response.ok) {
        throw new Error(`Failed to update quote: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error updating quote:', error);
      throw error;
    }
  }

  static async deleteQuote(id) {
    const apiUrl = `${CRM_CONFIG.API_URL}${CRM_CONFIG.QUOTES_ENDPOINT}/${id}`;
    
    try {
      const response = await fetch(apiUrl, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${CRM_CONFIG.API_TOKEN}`
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to delete quote: ${response.status}`);
      }

      return true;
    } catch (error) {
      console.error('Error deleting quote:', error);
      throw error;
    }
  }
}
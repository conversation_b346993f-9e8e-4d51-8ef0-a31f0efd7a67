/* global process */
import fetch from 'node-fetch';
import FormData from 'form-data';

// Define different blog post types
const blogPosts = {
  webdev: {
    title: "The Future of Web Development: Trends to Watch in 2024",
    slug: "future-web-development-trends-2024",
    heroImage: "https://images.unsplash.com/photo-1555066931-4365d14bab8c?q=80&w=2070&auto=format&fit=crop",
    coverImage: "https://images.unsplash.com/photo-1517694712202-14dd9538aa97?q=80&w=2070&auto=format&fit=crop"
  },
  marketing: {
    title: "Effective Digital Marketing Strategies for 2024",
    slug: "effective-digital-marketing-strategies-2024",
    heroImage: "https://images.unsplash.com/photo-1533750349088-cd871a92f312?q=80&w=2070&auto=format&fit=crop",
    coverImage: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?q=80&w=2015&auto=format&fit=crop"
  },
  design: {
    title: "Graphic Design Trends Reshaping Brand Identity in 2024",
    slug: "graphic-design-trends-reshaping-brand-identity-2024",
    heroImage: "https://images.unsplash.com/photo-1561070791-2526d30994b5?q=80&w=2000&auto=format&fit=crop",
    coverImage: "https://images.unsplash.com/photo-1626785774573-4b799315345d?q=80&w=2071&auto=format&fit=crop"
  }
};

// Get post type from command line arguments
const getPostType = () => {
  let postType = 'webdev'; // Default to webdev

  // Check if we're in a Node.js environment
  if (typeof process !== 'undefined' && process.argv) {
    const args = process.argv.slice(2);
    if (args.length > 0) {
      postType = args[0];
    }
  }

  // Check if the post type exists
  if (!blogPosts[postType]) {
    console.log(`Post type "${postType}" not found. Using default "webdev" post.`);
    return 'webdev';
  }

  return postType;
};

async function createBlogPost() {
  const postType = getPostType();
  console.log(`Creating ${postType} blog post...`);

  const selectedPost = blogPosts[postType];
  const API_URL = 'http://127.0.0.1:1337/api';
  const API_TOKEN = 'fea61574043e64953487ff75e73a68055f7a62617e4ff17d2dff9b859bb400f8ca403b4eadc7393ece2239f5f7cfd144be877a39319dd536a95fd0ae927e2fda41e2a929ddfa7995e26c9df7904b281787e432bc846459ee7d5ab2945da370bb2e5bd573bd685532bc563cae5d56a237dcdaf856e74398469eaf2af9f47ccd68';

  // First test the connection
  try {
    console.log('Testing connection to Strapi...');
    const testConnection = await fetch(`${API_URL}/blogs`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json'
      }
    });

    if (!testConnection.ok) {
      const errorText = await testConnection.text();
      console.log('Connection test failed:', errorText);
      return;
    }

    const connectionData = await testConnection.json();
    console.log('Successfully connected to Strapi. Current posts:', connectionData.data.length);
  } catch (error) {
    console.error('Connection test failed. Details:', {
      message: error.message,
      cause: error.cause
    });
    return;
  }

  // Upload hero image from Unsplash URL
  let heroImageId = null;
  try {
    console.log('Uploading hero image from Unsplash...');
    const heroImageUrl = selectedPost.heroImage;

    // Fetch the image from Unsplash
    const imageResponse = await fetch(heroImageUrl);
    if (!imageResponse.ok) {
      throw new Error(`Failed to fetch image: ${imageResponse.statusText}`);
    }

    // Get the image as a buffer
    const imageBuffer = await imageResponse.buffer();

    // Create a form with the image buffer
    const heroImageForm = new FormData();
    heroImageForm.append('files', imageBuffer, {
      filename: 'hero-image.jpg',
      contentType: 'image/jpeg'
    });

    // Upload to Strapi
    const heroImageResponse = await fetch(`${API_URL}/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${API_TOKEN}`
      },
      body: heroImageForm
    });

    if (!heroImageResponse.ok) {
      const errorData = await heroImageResponse.json();
      console.error('Hero image upload failed:', errorData);
      throw new Error(`HTTP error! status: ${heroImageResponse.status}`);
    }

    const heroImageData = await heroImageResponse.json();
    heroImageId = heroImageData[0].id;
    console.log('Hero image uploaded successfully with ID:', heroImageId);
  } catch (error) {
    console.error('Error uploading hero image:', {
      message: error.message,
      cause: error.cause
    });
    // Continue without hero image
  }

  // Upload cover image from Unsplash URL
  let coverImageId = null;
  try {
    console.log('Uploading cover image from Unsplash...');
    const coverImageUrl = selectedPost.coverImage;

    // Fetch the image from Unsplash
    const imageResponse = await fetch(coverImageUrl);
    if (!imageResponse.ok) {
      throw new Error(`Failed to fetch image: ${imageResponse.statusText}`);
    }

    // Get the image as a buffer
    const imageBuffer = await imageResponse.buffer();

    // Create a form with the image buffer
    const coverImageForm = new FormData();
    coverImageForm.append('files', imageBuffer, {
      filename: 'cover-image.jpg',
      contentType: 'image/jpeg'
    });

    // Upload to Strapi
    const coverImageResponse = await fetch(`${API_URL}/upload`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${API_TOKEN}`
      },
      body: coverImageForm
    });

    if (!coverImageResponse.ok) {
      const errorData = await coverImageResponse.json();
      console.error('Cover image upload failed:', errorData);
      throw new Error(`HTTP error! status: ${coverImageResponse.status}`);
    }

    const coverImageData = await coverImageResponse.json();
    coverImageId = coverImageData[0].id;
    console.log('Cover image uploaded successfully with ID:', coverImageId);
  } catch (error) {
    console.error('Error uploading cover image:', {
      message: error.message,
      cause: error.cause
    });
    // Continue without cover image
  }

  // Create blog post
  const blogPost = {
    data: {
      Title: selectedPost.title,
      Slug: selectedPost.slug,
      Content: [
        {
          type: "paragraph",
          children: [
            {
              type: "text",
              text: "The web development landscape is constantly evolving, with new technologies, frameworks, and design paradigms emerging at a rapid pace. As we move further into 2024, several key trends are shaping the future of web development and how businesses approach their digital presence."
            }
          ]
        },
        {
          type: "paragraph",
          children: [
            {
              type: "text",
              text: "In this article, we'll explore the most significant web development trends that are defining the industry this year and how they might impact your business's digital strategy."
            }
          ]
        },
        {
          type: "heading",
          level: 2,
          children: [
            {
              type: "text",
              text: "1. AI-Powered Development Tools"
            }
          ]
        },
        {
          type: "paragraph",
          children: [
            {
              type: "text",
              text: "Artificial Intelligence is revolutionizing how developers work. AI-powered coding assistants like GitHub Copilot and ChatGPT are becoming essential tools in a developer's arsenal, helping to automate repetitive tasks, suggest code improvements, and even generate entire code blocks based on natural language descriptions."
            }
          ]
        },
        {
          type: "paragraph",
          children: [
            {
              type: "text",
              text: "These tools are not replacing developers but rather enhancing their productivity and allowing them to focus on more complex, creative aspects of web development. As AI continues to advance, we can expect even more sophisticated development assistance that will further streamline the coding process."
            }
          ]
        },
        {
          type: "heading",
          level: 2,
          children: [
            {
              type: "text",
              text: "2. Progressive Web Apps (PWAs)"
            }
          ]
        },
        {
          type: "paragraph",
          children: [
            {
              type: "text",
              text: "Progressive Web Apps continue to gain traction as they bridge the gap between traditional websites and native mobile applications. PWAs offer the best of both worlds: the accessibility and reach of websites combined with the functionality and user experience of native apps."
            }
          ]
        },
        {
          type: "paragraph",
          children: [
            {
              type: "text",
              text: "Key benefits of PWAs include offline functionality, push notifications, and app-like interfaces, all without requiring users to download and install an app from an app store. This approach is particularly valuable for businesses looking to enhance user engagement without the development costs associated with maintaining separate web and mobile applications."
            }
          ]
        },
        {
          type: "heading",
          level: 2,
          children: [
            {
              type: "text",
              text: "3. WebAssembly (Wasm)"
            }
          ]
        },
        {
          type: "paragraph",
          children: [
            {
              type: "text",
              text: "WebAssembly is transforming what's possible in browser-based applications by allowing code written in languages like C, C++, and Rust to run in the browser at near-native speed. This technology enables web applications to perform computationally intensive tasks that were previously only possible in desktop applications."
            }
          ]
        },
        {
          type: "paragraph",
          children: [
            {
              type: "text",
              text: "From advanced graphics processing to complex calculations, WebAssembly is opening new possibilities for web-based games, scientific visualizations, and professional tools. As WebAssembly continues to mature, we can expect to see more sophisticated web applications that rival the performance of their desktop counterparts."
            }
          ]
        },
        {
          type: "heading",
          level: 2,
          children: [
            {
              type: "text",
              text: "4. Serverless Architecture"
            }
          ]
        },
        {
          type: "paragraph",
          children: [
            {
              type: "text",
              text: "Serverless computing continues to reshape how web applications are built and deployed. By abstracting away server management and infrastructure concerns, serverless architectures allow developers to focus solely on writing code that responds to events."
            }
          ]
        },
        {
          type: "paragraph",
          children: [
            {
              type: "text",
              text: "This approach offers significant benefits in terms of scalability, cost-efficiency, and reduced maintenance overhead. Services like AWS Lambda, Azure Functions, and Google Cloud Functions are making it easier than ever to implement serverless architectures, making this trend particularly relevant for businesses of all sizes."
            }
          ]
        },
        {
          type: "heading",
          level: 2,
          children: [
            {
              type: "text",
              text: "5. Micro-Frontends"
            }
          ]
        },
        {
          type: "paragraph",
          children: [
            {
              type: "text",
              text: "Just as microservices revolutionized backend development, micro-frontends are changing how teams approach frontend architecture. This architectural style involves breaking down a web application's frontend into smaller, more manageable pieces that can be developed, tested, and deployed independently."
            }
          ]
        },
        {
          type: "paragraph",
          children: [
            {
              type: "text",
              text: "Micro-frontends are particularly valuable for large organizations with multiple teams working on different aspects of a single application. By allowing teams to work autonomously on separate features, micro-frontends can significantly improve development efficiency and make it easier to maintain complex web applications."
            }
          ]
        },
        {
          type: "heading",
          level: 2,
          children: [
            {
              type: "text",
              text: "Conclusion"
            }
          ]
        },
        {
          type: "paragraph",
          children: [
            {
              type: "text",
              text: "The web development landscape in 2024 is characterized by technologies that enhance performance, improve user experience, and increase developer productivity. By staying informed about these trends and strategically incorporating them into your digital strategy, your business can create more engaging, efficient, and competitive web experiences."
            }
          ]
        },
        {
          type: "paragraph",
          children: [
            {
              type: "text",
              text: "At Veltrix Digital Solutions, we're committed to helping businesses navigate these evolving trends and implement the technologies that best serve their unique needs. Whether you're looking to revamp your existing web presence or build something entirely new, our team of experts is here to guide you through the process."
            }
          ]
        }
      ],
      DatePublished: new Date().toISOString()
    }
  };

  // Add image IDs if available - try different formats for Strapi
  if (heroImageId) {
    // Try direct ID assignment first (works in some Strapi versions)
    blogPost.data.HeroImage = heroImageId;
    console.log('Setting Hero Image ID:', heroImageId);
  }
  if (coverImageId) {
    // Try direct ID assignment first (works in some Strapi versions)
    blogPost.data.CoverImage = coverImageId;
    console.log('Setting Cover Image ID:', coverImageId);
  }

  // Log the final blog post data being sent
  console.log('Final blog post data:', JSON.stringify(blogPost, null, 2));

  try {
    console.log('Creating blog post...');
    const response = await fetch(`${API_URL}/blogs`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${API_TOKEN}`
      },
      body: JSON.stringify(blogPost)
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error('Server responded with error:', errorData);
      throw new Error(`HTTP error! status: ${response.status} - ${JSON.stringify(errorData)}`);
    }

    const data = await response.json();
    console.log('Successfully created blog post:', data);
    return data;
  } catch (error) {
    console.error('Error creating blog post:', {
      message: error.message,
      cause: error.cause
    });
    throw error;
  }
}

// Execute the function
createBlogPost().catch(error => {
  console.error('Script failed:', error);
});

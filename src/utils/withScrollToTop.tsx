import React, { useEffect } from 'react';

/**
 * Higher-Order Component (HOC) that adds scroll-to-top behavior to any component.
 * 
 * Example usage:
 * 
 * // Original component
 * const MyPage = () => {
 *   return <div>My Page Content</div>;
 * }
 * 
 * // Enhanced component with scroll-to-top behavior
 * export default withScrollToTop(MyPage);
 * 
 * @param Component The component to enhance with scroll-to-top behavior
 * @returns Enhanced component
 */
const withScrollToTop = <P extends object>(Component: React.ComponentType<P>): React.FC<P> => {
  const WithScrollToTop: React.FC<P> = (props) => {
    useEffect(() => {
      // Scroll to top when the component mounts
      window.scrollTo({
        top: 0,
        left: 0,
        behavior: 'auto'
      });
    }, []);

    return <Component {...props} />;
  };

  // Set the display name for debugging
  const displayName = Component.displayName || Component.name || 'Component';
  WithScrollToTop.displayName = `withScrollToTop(${displayName})`;

  return WithScrollToTop;
};

export default withScrollToTop;

/* styles.css */

/* Fade In from Left */
@keyframes fadeInLeft {
    0% {
      opacity: 0;
      transform: translateX(-100px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  /* Fade In from Right */
  @keyframes fadeInRight {
    0% {
      opacity: 0;
      transform: translateX(100px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  /* Bounce In */
  @keyframes bounceIn {
    0% {
      opacity: 0;
      transform: translateY(50px);
    }
    60% {
      opacity: 1;
      transform: translateY(-10px);
    }
    100% {
      transform: translateY(0);
    }
  }
  
  /* Animation Classes */
  .fade-in-left {
    opacity: 0;
    animation: fadeInLeft 2s ease forwards;
  }
  
  .fade-in-right {
    opacity: 0;
    animation: fadeInRight 2s ease forwards;
  }
  
  .bounce-in {
    opacity: 0;
    animation: bounceIn 2 ease forwards;
  }

  .no-scrollbar {
    -ms-overflow-style: none; /* for Internet Explorer, Edge */
    scrollbar-width: none; /* for Firefox */
 }
 .no-scrollbar::-webkit-scrollbar {
    display: none; /* for Chrome, Safari, and Opera */
 }
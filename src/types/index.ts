// Pricing Plan Types
export interface PricingFeature {
  title: string;
  description: string;
}

export interface PricingPlan {
  plan: string;
  price: string;
  regularPrice: string;
  description: string;
  features: PricingFeature[];
  isPopular: boolean;
}

// Blog Post Types
export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  coverImage: string;
  publishedAt: string;
  category: string;
  author: {
    name: string;
    avatar?: string;
  };
}

// Portfolio Project Types
export interface PortfolioProject {
  id: string;
  title: string;
  slug: string;
  description: string;
  category: 'web-development' | 'app-development' | 'graphic-design';
  coverImage: string;
  images: string[];
  technologies: string[];
  clientName?: string;
  projectUrl?: string;
  completed: string; // Date
}

// Quote Quiz Types
export interface QuizAnswer {
  websiteType: string;
  businessType: string;
  hasExistingWebsite: string;
  timeline: string;
  budget: string;
  requirements: string;
  email: string;
  clientName: string;
  contactNumber: string;
}

// Allow importing of various file types
declare module '*.scss' {
  const content: { [className: string]: string };
  export default content;
}

declare module '*.css' {
  const content: { [className: string]: string };
  export default content;
}

declare module '*.svg' {
  import React = require('react');
  export const ReactComponent: React.FC<React.SVGProps<SVGSVGElement>>;
  const src: string;
  export default src;
}

declare module '*.png' {
  const content: string;
  export default content;
}

declare module '*.jpg' {
  const content: string;
  export default content;
}

declare module '*.jpeg' {
  const content: string;
  export default content;
}

declare module '*.gif' {
  const content: string;
  export default content;
}

// Add declarations for modules without type definitions
// Example:
// declare module 'react-text-gradients' {
//   export const LinearGradient: React.FC<{text: string; gradient: string}>;
//   export const RadialGradient: React.FC<{text: string; gradient: string}>;
// }

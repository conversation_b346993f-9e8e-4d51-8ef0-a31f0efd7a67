
import { useState } from "react";
import { HashLink } from "react-router-hash-link";
import { Link } from "react-router-dom";
import styles from "./MobileNav.module.scss";

const MobileNav = () => {
  const [menuOpen, setMenuOpen] = useState(false);

  // Toggle menu open/close
  const toggleMenu = () => setMenuOpen((prev) => !prev);

  // Explicit close function
  const closeMenu = () => setMenuOpen(false);

  return (
    <nav className={styles.mobileNav}>
      <button className={styles.menuButton} onClick={toggleMenu}>
        <img
          src="/MobileNav.svg"
          alt={menuOpen ? "Close menu" : "Open menu"}
          className={styles.navIcon}
        />
      </button>
      {menuOpen && (
        <div className={styles.menuOverlay} onClick={closeMenu}>
          <div className={styles.menuContent} onClick={(e) => e.stopPropagation()}>
            <button className={styles.closeButton} onClick={closeMenu}>
              &times;
            </button>
            <HashLink smooth to="/#home" className={styles.link} onClick={closeMenu}>
              Home
            </HashLink>
            <HashLink smooth to="/#about" className={styles.link} onClick={closeMenu}>
              About
            </HashLink>
            <HashLink smooth to="/#services" className={styles.link} onClick={closeMenu}>
              What We Do
            </HashLink>
            <Link to="/portfolio" className={styles.link} onClick={closeMenu}>
              Portfolio
            </Link>
            <HashLink smooth to="/#pricing" className={styles.link} onClick={closeMenu}>
              Pricing
            </HashLink>
            <Link to="/blog" className={styles.link} onClick={closeMenu}>
              Blog
            </Link>
            <HashLink smooth to="/#contact" className={styles.link} onClick={closeMenu}>
              Contact Us
            </HashLink>
          </div>
        </div>
      )}
    </nav>
  );
};

export default MobileNav;

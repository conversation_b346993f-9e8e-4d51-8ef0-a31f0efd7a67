.headerContainer {
  position: relative;
  height: 3.5rem; /* 56px */
  padding: 0.5rem 1.5rem; /* py-2, px-6 */
  border-radius: 9999px; /* rounded-full */
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1.5rem; /* gap-6 */
  background-color: #182b2a; /* Dark background */
  border: 0.5px solid #23452e;
}

.link {
  padding: 0.25rem 0.5rem; /* py-1, px-2 */
  position: relative;
  font-size: 0.75rem; /* text-xs */
  font-weight: 500; /* font-medium */
  text-decoration: none;
  color: white; /* default inactive color */
  transition: color 0.3s ease;
}

/* Hover state: change text color */
.link:hover {
  color: #3fa98e;
}

/* Active state: explicitly set the color */
.activeLink {
  color: #3fa98e;
}

/* Underline effect */
.underline {
  position: absolute;
  bottom: -2px; /* adjust if needed to sit right below text */
  left: 0;
  right: 0;
  height: 2px;
  background-color: #3fa98e;
  transform: scaleX(0);
  transform-origin: left;
  transition: transform 0.3s ease;
}

/* When hovering or when active, show the underline */
.link:hover .underline,
.activeLink .underline {
  transform: scaleX(1);
}
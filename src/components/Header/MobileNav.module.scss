/* MobileNav.module.scss */

/* Container for the mobile nav button */
.mobileNav {
  position: relative;
}

/* <PERSON>u <PERSON>yl<PERSON> */
.menuButton {
  background-color: rgba(63, 169, 143, 0.1); // Lighter, more subtle background
  border: none;
  cursor: pointer;
  padding: 12px; // Reduced padding
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
  border-radius: 12px; // Slightly reduced border radius
  border: 0.5px solid rgba(35, 69, 46, 0.3); // More subtle border
  margin-right: 0; // Remove right margin
}

.menuButton:hover {
  background-color: rgba(63, 169, 143, 0.2);
}

/* Icon Styling */
.navIcon {
  width: 24px; // Reduced size
  height: 24px; // Reduced size
}

/* Full-Screen Overlay with Transparent Blur */
.menuOverlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* Centered Menu Content */
.menuContent {
  background-color: rgba(24, 43, 42, 0.9);
  backdrop-filter: blur(10px);
  padding: 2rem;
  padding-inline: 3rem;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  text-align: center;
  max-width: 90%;
  border: 0.5px solid rgba(35, 69, 46, 0.5);
}

/* Close Button in the Menu */
.closeButton {
  background: transparent;
  border: none;
  color: white;
  font-size: 2rem;
  cursor: pointer;
}

/* Link Styling */
.link {
  text-decoration: none;
  color: white;
  font-size: 1rem;
  font-weight: 500;
  padding: 0.5rem;
  transition: color 0.3s ease;
}

.link:hover {
  color: #3fa98e;
}

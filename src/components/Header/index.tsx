
// Header.jsx
import { HashLink } from 'react-router-hash-link';
import { Link } from 'react-router-dom';
import styles from "./index.module.scss";

const Header = () => {
  return (
    <div className={styles.headerContainer}>
      <HashLink smooth to="/#home" className={styles.link}>
        <div>HOME</div>
      </HashLink>
      <HashLink smooth to="/#about" className={styles.link}>
        <div>ABOUT</div>
      </HashLink>
      <HashLink smooth to="/#carousel" className={styles.link}>
        <div>WHAT WE DO</div>
      </HashLink>
      <Link to="/portfolio" className={styles.link}>
        <div>PORTFOLIO</div>
      </Link>
      <HashLink smooth to="/#pricing" className={styles.link}>
        <div>PRICING</div>
      </HashLink>
      <Link to="/blog" className={styles.link}>
        <div>BLOG</div>
      </Link>
      <HashLink smooth to="/#quote" className={styles.link}>
        <div>CONTACT US</div>
      </HashLink>
    </div>
  );
};

export default Header;

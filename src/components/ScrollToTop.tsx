import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * Component that forcefully scrolls to the top of the page when the route changes.
 * Uses multiple techniques to handle different edge cases.
 */
function ScrollToTop(): null {
  const { pathname, hash } = useLocation();

  useEffect(() => {
    // Skip scroll restoration if there's a hash in the URL (e.g., anchor links)
    if (hash) return;

    // Force scroll to top with a small delay to ensure rendering completes
    // We use multiple techniques for better reliability
    const scrollToTop = () => {
      // First attempt - immediate
      window.scrollTo({
        top: 0,
        left: 0,
        behavior: 'auto' // Using 'auto' instead of 'smooth' for immediate scroll
      });

      // Second attempt with a slight delay
      setTimeout(() => {
        window.scrollTo(0, 0);
        
        // Third attempt with a longer delay as a fallback
        setTimeout(() => {
          if (window.pageYOffset > 0) {
            window.scrollTo(0, 0);
          }
        }, 50);
      }, 10);
    };

    // Execute scroll
    scrollToTop();

    // Also handle the case when scroll doesn't happen properly
    const checkAndScrollAgain = () => {
      if (window.pageYOffset > 0) {
        window.scrollTo(0, 0);
      }
    };

    // Add event listener for when page becomes visible (in case of tab switching)
    document.addEventListener('visibilitychange', checkAndScrollAgain);
    // Also check after animation frames complete
    requestAnimationFrame(() => {
      requestAnimationFrame(checkAndScrollAgain);
    });

    return () => {
      document.removeEventListener('visibilitychange', checkAndScrollAgain);
    };
  }, [pathname, hash]); // Re-run effect when pathname or hash changes

  return null;
}

export default ScrollToTop;
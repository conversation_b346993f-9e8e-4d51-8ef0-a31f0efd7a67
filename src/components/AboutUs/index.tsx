
import { useEffect, useRef } from "react";
import styles from "./index.module.scss";
import Tee from "../../assets/Tee.png";
import DevIcons from "../../assets/Dev_icons.png";

// Define a custom type for elements with data-type attribute
interface ElementWithDataType extends HTMLElement {
  dataset: {
    type: "characters" | "text";
  };
}

const AboutUs = () => {
  const charactersRef = useRef<HTMLDivElement | null>(null);
  const textIconsRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          // Type assertion to tell TypeScript this is our custom element type
          const target = entry.target as ElementWithDataType;

          if (entry.isIntersecting) {
            if (target.dataset.type === "characters") {
              target.classList.add(styles.animateCharacters);
            } else if (target.dataset.type === "text") {
              target.classList.add(styles.animateText);
            }
          } else {
            if (target.dataset.type === "characters") {
              target.classList.remove(styles.animateCharacters);
            } else if (target.dataset.type === "text") {
              target.classList.remove(styles.animateText);
            }
          }
        });
      },
      { threshold: 0.2 }
    );

    const charactersElement = charactersRef.current;
    const textIconsElement = textIconsRef.current;

    if (charactersElement) observer.observe(charactersElement);
    if (textIconsElement) observer.observe(textIconsElement);

    return () => {
      if (charactersElement) observer.unobserve(charactersElement);
      if (textIconsElement) observer.unobserve(textIconsElement);
    };
  }, []);

  return (
    <section className={styles.aboutSection}>
      <h2 className={styles.title}>ABOUT ME</h2>

      <div className={styles.container}>
        <div
          className={styles.characters}
          ref={charactersRef}
          data-type="characters"
        >
          <img src={Tee} alt="Tarik Character" className={styles.character} />
        </div>

        <div
          className={styles.textIconsContainer}
          ref={textIconsRef}
          data-type="text"
        >
          <div className={styles.textBox}>
            <p>
              HI, I&apos;M <span className={styles.highlight}>TARIK</span>, THE FOUNDER OF{" "}
              <span className={styles.highlight}>Veltrix</span>. WITH A{" "}
              <span className={styles.highlight}>DEGREE IN VFX AND 3D</span>, I&apos;VE EVOLVED INTO A{" "}
              <span className={styles.highlight}>MULTIDISCIPLINARY CREATIVE</span>{" "}
              WHO COMBINES <span className={styles.highlight}>ARTISTIC VISION</span> WITH{" "}
              <span className={styles.highlight}>TECHNICAL INNOVATION</span>.
            </p>
            <p data-type="text" className={styles.text}>
              I&apos;M A DIGITAL DESIGNER AND DEVELOPER WITH EXPERTISE IN{" "}
              <span className={styles.highlight}>WEB DEVELOPMENT</span>,{" "}
              <span className={styles.highlight}>APP DEVELOPMENT</span>, AND{" "}
              <span className={styles.highlight}>3D VISUALIZATION</span>. THIS UNIQUE BLEND OF{" "}
              <span className={styles.highlight}>CREATIVE DESIGN</span> AND{" "}
              <span className={styles.highlight}>TECHNICAL SKILLS</span> ALLOWS ME TO CREATE{" "}
              <span className={styles.highlight}>IMMERSIVE DIGITAL EXPERIENCES</span> THAT CAPTIVATE
              AND ENGAGE AUDIENCES.
            </p>
            <p style={{ marginTop: '20px' }}>
              I SPECIALIZE IN TRANSFORMING BUSINESSES THROUGH{" "}
              <span className={styles.highlight}>STRIKING VISUAL IDENTITIES</span> AND{" "}
              <span className={styles.highlight}>CUTTING-EDGE DIGITAL SOLUTIONS</span>. WHETHER IT&apos;S
              CRAFTING A MEMORABLE BRAND, BUILDING A RESPONSIVE WEBSITE, OR DEVELOPING A CUSTOM
              APPLICATION, I BRING THE PERFECT BLEND OF{" "}
              <span className={styles.highlight}>CREATIVITY</span> AND{" "}
              <span className={styles.highlight}>TECHNICAL EXPERTISE</span> TO EVERY PROJECT.
            </p>
          </div>

          <div className={styles.devIcons}>
            <img src={DevIcons} alt="Developer Icons" className={styles.icons} />
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutUs;

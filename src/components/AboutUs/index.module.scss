.aboutSection {
  text-align: center;
  color: white;
  padding: 100px;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 100px;
}

.title {
  font-size: 80px;
  font-weight: 300;
  margin-bottom: 30px;
  padding-bottom: 100px;
}

/* Main layout: Characters on the left, text+icons on the right */
.container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 100px;
  width: 80%;
}

/* ----------------- */
/* Characters Section */
/* ----------------- */
.characters {
  display: flex;
  align-items: center;
  position: relative;
  
  /* Start hidden and shifted to the left */
  opacity: 0;
  transform: translateX(-50px);
  transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

/* This class will be added to animate the characters in */
.animateCharacters {
  opacity: 1;
  transform: translateX(0);
}

.character {
  width: 400px; /* Increased size */
  height: auto;
}

.character:first-child {
  margin-right: -40px; /* Move left character slightly to the right */
  z-index: 2; /* Ensure it's in front */
}

.character:last-child {
  z-index: 1; /* Behind the first character */
}

/* --------------------- */
/* Text & Icons Section  */
/* --------------------- */
.textIconsContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  /* Start hidden and shifted to the right */
  opacity: 0;
  transform: translateX(50px);
  transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

/* This class will be added to animate the text & icons in */
.animateText {
  opacity: 1;
  transform: translateX(0);
}

/* Text box positioned above icons */
.textBox {
  max-width: 600px;
  font-size: 16px;
  background: rgb(26 38 38 / var(--tw-bg-opacity)); /* #1a2626 */
  padding: 20px;
  border-radius: 15px;
  line-height: 1.6;
  text-align: left;
  margin-bottom: 20px; /* Added spacing for icons */
  border: 0.5px solid #23452e;
}

/* Highlights for keywords */
.highlight {
  color: #97fbd385;
  font-weight: bold;
}

/* Dev icons positioned directly below text */
.devIcons {
  display: flex;
  justify-content: center;
  padding: 20px;
  border-radius: 15px;
  background: rgb(26 38 38 / var(--tw-bg-opacity)); /* #1a2626 */
  border: 0.5px solid #23452e;
}

.icons {
  width: 100%;
  max-width: 560px;
}

/* --------------------- */
/* Mobile Adjustments */
/* --------------------- */
@media (max-width: 768px) {
  .aboutSection {
    padding: 50px 20px;
    padding-bottom: 50px;
  }
  
  .title {
    font-size: 40px;  /* Smaller title for mobile */
    margin-bottom: 20px;
    padding-bottom: 20px;
  }
  
  .container {
    flex-direction: column;
    gap: 20px;
    width: 100%;
  }
  
  /* Center characters and remove horizontal translation */
  .characters {
    justify-content: center;
    transform: translateX(0);
    opacity: 1; /* Optionally keep them visible without delay */
  }
  
  .character {
    width: 150px;  /* Reduced image size */
    margin-right: 0; /* Reset margin if necessary */
  }
  
  .textIconsContainer {
    transform: translateX(0);
    opacity: 1;
  }
  
  .textBox {
    max-width: 100%;
    font-size: 14px;  /* Slightly smaller font size */
    text-align: center;
  }
  
  .devIcons {
    padding: 10px;
  }
  
  .icons {
    max-width: 90%;
  }
}
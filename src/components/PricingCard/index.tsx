
import { FiChe<PERSON>, <PERSON><PERSON>rrowRight } from "react-icons/fi";
import styles from "./index.module.scss";
import { PricingFeature } from "../../types";

interface PricingCardProps {
  plan: string;
  price: string | number;
  regularPrice: string | number;
  description: string;
  features: PricingFeature[];
  isPopular?: boolean;
}

const PricingCard = ({
  plan,
  price,
  regularPrice,
  description,
  features,
  isPopular = false
}: PricingCardProps): JSX.Element => {
  return (
    <div className={styles.cardWrapper}>
      <div className={styles.cardContainer}>
        <div className={styles.gradientOverlay} />
        <div className={styles.topBlob} />
        <div className={styles.bottomBlob} />

        {isPopular && (
          <div className={styles.popularTag}>
            <span>MOST POPULAR</span>
          </div>
        )}

        <div className={styles.cardContent}>
          <h3 className={styles.planName}>
            {plan.split(' ')[0]}<br />{plan.split(' ')[1]}
          </h3>
          <div className={styles.pricing}>
            <span className={styles.price}>£{price}</span>
            <span className={styles.regularPrice}>£{regularPrice}</span>
          </div>
          <p className={styles.description}>{description}</p>

          <div className={styles.featuresList}>
            {features.map((feature, index) => (
              <div key={index} className={styles.featureItem}>
                <div className={styles.checkmarkContainer}>
                  <FiCheck className={styles.checkmark} />
                </div>
                <div className={styles.featureText}>
                  <span className={styles.featureTitle}>{feature.title}</span>
                  <span className={styles.featureDescription}>
                    {feature.description}
                  </span>
                </div>
              </div>
            ))}
          </div>

          <button className={styles.ctaButton}>
            <div className={styles.ctaContent}>
              Get Started
              <FiArrowRight className={styles.ctaArrow} />
            </div>
          </button>

          <div className={styles.guardLogoContainer}>
            <FiCheck className="w-5 h-5 text-[#94a3b8] mr-2" />
            <span>SSL Secured</span>
          </div>
        </div>
      </div>
    </div>
  );
};



export default PricingCard;

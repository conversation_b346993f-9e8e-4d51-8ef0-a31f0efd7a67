/* index.module.scss */

/* Card wrapper styling */
.cardWrapper {
  position: relative;
  width: 20rem;
  height: fit-content;
  padding-top: 100px;
  padding-bottom: 100px;
}

/* Main card container styling */
.cardContainer {
  position: relative;
  overflow: hidden;
  border-radius: 1rem;
  background: linear-gradient(
    145deg,
    rgba(26, 38, 38, 0.95) 0%,
    rgba(35, 69, 46, 0.98) 100%
  );
  padding: 1px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transition: all 0.3s ease;
  height: 100%;
  border: 0.5px solid rgba(63, 169, 142, 0.3);

  &:hover {
    transform: translateY(-0.5rem);
    box-shadow: 0 20px 25px -5px rgba(63, 169, 142, 0.3);
    border-color: rgba(63, 169, 142, 0.6);
  }
}

/* Gradient overlay styling */
.gradientOverlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(
    135deg,
    rgba(63, 169, 142, 0.15) 0%,
    rgba(35, 69, 46, 0.3) 100%
  );
  opacity: 1;
  pointer-events: none;
}

/* Card content styling */
.cardContent {
  position: relative;
  border-radius: 1rem;
  background: rgba(26, 38, 38, 0.95);
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  height: 100%;
  z-index: 1;
}

/* Gradient blob effects */
.topBlob {
  position: absolute;
  top: -4rem;
  left: -4rem;
  height: 12rem;
  width: 12rem;
  border-radius: 9999px;
  background: linear-gradient(135deg, rgba(93, 167, 148, 0.3) 0%, rgba(150, 224, 174, 0) 100%);
  filter: blur(2rem);
  opacity: 0.6;
  transition: all 0.5s ease;

  &:hover {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

.bottomBlob {
  position: absolute;
  bottom: -4rem;
  right: -4rem;
  height: 12rem;
  width: 12rem;
  border-radius: 9999px;
  background: linear-gradient(135deg, rgba(63, 169, 142, 0.3) 0%, rgba(35, 69, 46, 0) 100%);
  filter: blur(2rem);
  opacity: 0.6;
  transition: all 0.5s ease;

  &:hover {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

/* Popular tag styling with gradient */
.popularTag {
  position: absolute;
  top: 1.25rem; /* Adjusted to align with title */
  right: 1rem;
  background: linear-gradient(
    135deg,
    rgba(63, 169, 142, 1) 0%,
    rgba(45, 138, 127, 1) 100%
  );
  color: white;
  padding: 0.35rem 0.75rem; /* Slightly reduced padding */
  border-radius: 0.5rem;
  font-size: 0.75rem; /* Reduced from 0.875rem */
  font-weight: 500;
  z-index: 2;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Plan name styling */
.planName {
  font-size: 1.25rem;
  font-weight: bold;
  color: white;
  margin-bottom: 1rem;
  padding-right: 6rem;
  line-height: 1.2; /* Add proper line spacing for the two lines */
  br {
    display: block; /* Ensure line break works */
    content: ""; /* Required for empty br */
  }
}

/* Pricing section styling */
.pricing {
  display: flex;
  align-items: baseline;
  gap: 1rem;
  margin-bottom: 1rem;
}

.price {
  font-size: 2.5rem;
  font-weight: bold;
  color: #3fa98e;
}

.regularPrice {
  font-size: 1.5rem;
  color: #a1c3ba;
  text-decoration: line-through;
  opacity: 0.8;
}

/* Description styling */
.description {
  color: #a1c3ba;
  margin-bottom: 2rem;
}

/* Features list styling */
.featuresList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
}

.featureItem {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.checkmarkContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 1.5rem;
  width: 1.5rem;
  flex-shrink: 0;
  border-radius: 9999px;
  background-color: rgba(63, 169, 142, 0.1);
}

.checkmark {
  height: 1rem;
  width: 1rem;
  color: #3fa98e;
}

.featureText {
  display: flex;
  flex-direction: column;
}

.featureTitle {
  font-size: 0.875rem;
  font-weight: 500;
  color: white;
}

.featureDescription {
  font-size: 0.75rem;
  color: #a1c3ba;
}

/* Guard logo container styling */
.guardLogoContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 1rem;
  color: #a1c3ba;
  font-size: 0.875rem;
}

/* CTA button styling */
.ctaButton {
  width: 100%;
  background: linear-gradient(
    135deg,
    #3fa98e 0%,
    #2d8a7f 100%
  );
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-size: 1.125rem;
  border: 1px solid rgba(63, 169, 142, 0.4);
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: auto;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    background: linear-gradient(
      135deg,
      #34a084 0%,
      #267a70 100%
    );
    border-color: rgba(63, 169, 142, 0.6);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(63, 169, 142, 0.25);

    &::before {
      opacity: 1;
    }
  }
}

.ctaContent {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  position: relative;
  z-index: 1;
}

.ctaArrow {
  height: 1rem;
  width: 1rem;
}

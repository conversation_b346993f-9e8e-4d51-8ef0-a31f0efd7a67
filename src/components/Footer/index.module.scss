/* index.module.scss */

/* Footer Container */
.footerContainer {
  background-color: #0a1212; /* Darker background for better contrast */
  color: white;
  padding: 6rem 3rem 3rem; /* Adjusted padding */
  @media (max-width: 1023px) {
    padding: 4rem 1.5rem 2rem;
  }
}

/* Footer Title */
.footerTitle {
  font-size: 3rem; /* text-5xl on desktop */
  font-weight: bold;
  margin-bottom: 4rem;
  @media (max-width: 1023px) {
    font-size: 2rem;
    margin-bottom: 2rem;
    text-align: center;
  }
}

/* Footer Content: Contact and Links */
.footerContent {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  @media (min-width: 1024px) {
    flex-direction: row;
  }
}

/* Footer Contact Information */
.footerContact {
  font-size: 1.875rem; /* text-3xl on desktop */
  margin-bottom: 2rem;
  @media (max-width: 1023px) {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
    text-align: center;
  }
}

/* Footer Address */
.footerAddress {
  font-size: 1.125rem; /* text-lg on desktop */
  margin-bottom: 1rem;
  @media (max-width: 1023px) {
    font-size: 1rem;
    text-align: center;
  }
}

/* Footer Navigation Links */
.footerLinks {
  display: grid;
  grid-template-columns: repeat(2, auto);
  gap: 1.5rem 8rem; /* Increased horizontal spacing between columns */
  max-width: 800px;
  margin: 0 auto 4rem; /* Center the grid and add bottom margin */
  
  a {
    color: white;
    font-size: 1rem;
    letter-spacing: 0.5px;
    transition: color 0.2s ease;
    
    &:hover {
      color: #3fa98e; /* Teal accent color on hover */
    }
  }
  
  @media (max-width: 1023px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    text-align: center;
    margin-bottom: 3rem;
  }
}

/* Footer Bottom Section */
.footerBottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid rgba(255, 255, 255, 0.1); /* Subtle separator line */
  padding-top: 2rem;
  
  @media (max-width: 1023px) {
    flex-direction: column;
    gap: 1.5rem;
  }
}

/* Footer Brand */
.footerBrand {
  font-size: 2.5rem;
  font-weight: bold;
  color: white;
}

/* Footer Bottom Links */
.footerBottomLinks {
  display: flex;
  gap: 2rem;
  color: rgba(255, 255, 255, 0.7); /* Slightly muted color */
  font-size: 0.875rem;
  
  p {
    transition: color 0.2s ease;
    cursor: pointer;
    
    &:hover {
      color: white;
    }
  }
  
  @media (max-width: 1023px) {
    gap: 1.5rem;
    flex-direction: column;
    text-align: center;
  }
}

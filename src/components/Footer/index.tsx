
// Footer.jsx
import styles from "./index.module.scss";

const Footer = () => {
  return (
    <footer className={styles.footerContainer}>
      <div className="container mx-auto">

        {/* Content Section (Contact and Links) */}
        <div className={styles.footerContent}>

          {/* Right Section (Navigation Links) */}
          <div className={styles.footerLinks}>
            <a href="/services" className="hover:underline">
              SERVICES
            </a>
            <a href="/services/web-development" className="hover:underline">
              WEB DEVELOPMENT
            </a>
            <a href="/services/app-development" className="hover:underline">
              APP DEVELOPMENT
            </a>
            <a href="/services/graphic-design" className="hover:underline">
              GRAPHIC DESIGN
            </a>
            <a href="/services/automation" className="hover:underline">
              AUTOMATION
            </a>
            <a href="/services/marketing" className="hover:underline">
              MARKETING
            </a>
            <a href="/services/email" className="hover:underline">
              EMAIL
            </a>
            <a href="/contact" className="hover:underline">
              CONTACT
            </a>
          </div>
        </div>

        {/* Footer Bottom Section */}
        <div className={styles.footerBottom}>
          <p className={styles.footerBrand}>Veltrix</p>
          <div className={styles.footerBottomLinks}>
            <p>Privacy Policy</p>
            <p>Websites by Us</p>
            <p>2024 Veltrix</p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;


"use client";

import { <PERSON>actNode, FormEvent } from "react";
import { Label } from "../../label"; // Adjust the path if necessary
import { Input } from "../../input"; // Adjust the path if necessary
import { cn } from "../../../lib/utils";

interface LabelInputContainerProps {
  children: ReactNode;
  className?: string;
}

export function ContactForm() {
  const handleSubmit = (e: FormEvent<HTMLFormElement>): void => {
    e.preventDefault();
    console.log("Form submitted");
  };

  return (
    <div className="max-w-lg w-full mx-auto rounded-none md:rounded-2xl py-60 md:p-8 shadow-input bg-darkGreen border-lightGreen30 border-0.5">
      <h2 className="font-bold text-xl text-neutral-200">
        Contact Us
      </h2>
      <p className="text-sm max-w-sm mt-2 text-neutral-300">
        Please fill out the form below and we will get back to you as soon as possible.
      </p>
      <form className="my-8" onSubmit={handleSubmit}>
        <div className="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-2 mb-4">
          <LabelInputContainer>
            <Label htmlFor="firstname">First Name</Label>
            <Input id="firstname" placeholder="John" type="text" />
          </LabelInputContainer>
          <LabelInputContainer>
            <Label htmlFor="lastname">Last Name</Label>
            <Input id="lastname" placeholder="Doe" type="text" />
          </LabelInputContainer>
        </div>
        <div className="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-2 mb-4">
          <LabelInputContainer>
            <Label htmlFor="contactNumber">Contact Number</Label>
            <Input id="contactNumber" placeholder="123-456-7890" type="text" />
          </LabelInputContainer>
          <LabelInputContainer>
            <Label htmlFor="serviceNeeded">Service Needed</Label>
            <Input id="serviceNeeded" placeholder="Web Design" type="text" />
          </LabelInputContainer>
        </div>
        <LabelInputContainer className="mb-4">
          <Label htmlFor="preferredDateTime">Preferred Date/Time</Label>
          <Input id="preferredDateTime" placeholder="MM/DD/YYYY - HH:MM AM/PM" type="text" />
        </LabelInputContainer>
        <LabelInputContainer className="mb-8">
          <Label htmlFor="additionalDetails">Additional Details</Label>
          {/* Use a standard HTML textarea element */}
          <textarea
            id="additionalDetails"
            placeholder="Provide any additional details..."
            rows={4}
            className="w-full rounded-md border border-gray-300 dark:border-neutral-600 p-2 dark:bg-neutral-800 dark:text-white"
          />
        </LabelInputContainer>

        <button
          className="bg-gradient-to-br relative group/btn from-darkGreen to-lightGreen block w-full text-white rounded-md h-10 font-medium shadow-[0px_1px_0px_0px_#ffffff40_inset,0px_-1px_0px_0px_#ffffff40_inset]"
          type="submit"
        >
          Sign Up &rarr;
          <BottomGradient />
        </button>
      </form>
    </div>
  );
}

const BottomGradient = () => {
  return (
    <>
      <span className="group-hover/btn:opacity-100 block transition duration-500 opacity-0 absolute h-px w-full -bottom-px inset-x-0 bg-gradient-to-r from-transparent via-cyan-500 to-transparent" />
      <span className="group-hover/btn:opacity-100 blur-sm block transition duration-500 opacity-0 absolute h-px w-1/2 mx-auto -bottom-px inset-x-10 bg-gradient-to-r from-transparent via-indigo-500 to-transparent" />
    </>
  );
};

const LabelInputContainer = ({ children, className }: LabelInputContainerProps): JSX.Element => {
  return (
    <div className={cn("flex flex-col space-y-2 w-full", className)}>
      {children}
    </div>
  );
};
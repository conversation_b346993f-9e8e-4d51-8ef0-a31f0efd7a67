/* index.module.scss */

/* Newsletter container styling */
.newsletterContainer {
  background-color: #3fa98e;
  color: white;
  /* Reduced padding for mobile; larger padding for desktop */
  padding: 4rem 1.5rem;

  @media (min-width: 1024px) {
    padding: 8rem 3rem;
  }
}

/* Content container: column layout on mobile, row on desktop */
.newsletterContent {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;

  @media (min-width: 1024px) {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    text-align: left;
  }
}

/* Newsletter title styling */
.newsletterTitle {
  font-size: 2.25rem; /* Smaller title on mobile */
  font-weight: bold;
  margin-bottom: 1.5rem;

  @media (min-width: 1024px) {
    font-size: 3rem; /* Larger title on desktop */
    margin-bottom: 0;
  }
}

/* Form container styling */
.newsletterForm {
  display: flex;
  justify-content: center;
  background-color: white;
  border-radius: 9999px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  width: 100%;
  max-width: 500px; /* Ensure it doesn't stretch too wide on mobile */

  @media (min-width: 1024px) {
    width: auto;
    min-width: 450px; /* Minimum width for the form on desktop */
  }
}

/* Input styling */
.newsletterInput {
  padding: 1rem 1.25rem; /* Slightly reduced padding on mobile */
  width: 100%;
  font-size: 1rem;
  color: #374151;
  border: none;
  outline: none;
  min-width: 280px; /* Minimum width for the input field */
  
  @media (min-width: 1024px) {
    min-width: 350px; /* Larger minimum width on desktop */
  }
}

/* Button styling */
.newsletterButton {
  background-color: transparent;
  color: black;
  padding: 1rem 1.25rem;
  font-weight: 600;
  border: none;
  border-radius: 9999px;
  cursor: pointer;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #e5e7eb;
  }
}

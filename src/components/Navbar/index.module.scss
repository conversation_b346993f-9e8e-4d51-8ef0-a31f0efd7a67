.navbarContainer {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 30;
  padding-bottom: 0.75rem;
  background-color: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(6px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.navbarInner {
  width: 100%;
  max-width: 1420px;
  height: 80px; // Reduced default height
  padding: 0 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 auto;
}

.logoContainer {
  width: 300px;
  height: 170px;
  margin-bottom: 20px;
}

.logo {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* Desktop navigation is visible by default */
.desktopNav {
  display: block;
}

/* Mobile navigation is hidden by default */
.mobileNav {
  display: none;
}

/* Mobile Styles */
@media (max-width: 1024px) {
  .navbarInner {
    padding: 0 1.5rem;
  }
  
  .logoContainer {
    width: 200px;
    height: 120px;
  }
}

@media (max-width: 768px) {
  .desktopNav {
    display: none;
  }
  
  .mobileNav {
    display: block;
    position: relative;
    margin-left: auto; // Push to right side
  }
  
  .navbarInner {
    height: 70px; // Further reduced height for mobile
    padding: 0 1rem;
  }
  
  .logoContainer {
    width: 130px; // Adjusted logo size
    height: auto; // Let height adjust automatically
    margin-bottom: 0; // Remove bottom margin
  }
}

@media (max-width: 480px) {
  .navbarInner {
    padding: 0 0.75rem;
  }
  
  .logoContainer {
    width: 120px;
  }
}

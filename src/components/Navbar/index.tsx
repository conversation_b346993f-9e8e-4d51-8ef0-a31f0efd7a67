
import Header from "@components/Header";      // Desktop navigation
import MobileNav from "../Header/MobileNav"; // Mobile navigation
import styles from "./index.module.scss";

const Navbar = () => {
  return (
    <div className={styles.navbarContainer}>
      <div className={styles.navbarInner}>
        {/* Left Side: Logo */}
        <div className={styles.logoContainer}>
          <img src="/logo.webp" alt="Logo" className={styles.logo} />
        </div>
        {/* Desktop Navigation (hidden on mobile) */}
        <div className={styles.desktopNav}>
          <Header />
        </div>
        {/* Mobile Navigation Button (shown on mobile) */}
        <div className={styles.mobileNav}>
          <MobileNav />
        </div>
      </div>
    </div>
  );
};

export default Navbar;
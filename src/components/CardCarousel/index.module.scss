/* Main container and title */
.mainContainer {
  min-height: 100vh;
  padding: 2rem;
}

.mainTitle {
  font-size: 4rem; /* text-6xl */
  font-weight: bold;
  text-align: center;
  color: #ffffff;
  margin-bottom: 120px;
}

/* Carousel container */
.carouselContainer {
  position: relative;
  width: 100%;
  padding: 0 10vw; /* Add horizontal padding of 15% of viewport width */
  margin: 0 auto;
  max-width: 1920px; /* Optional: maximum width for very large screens */
}

.carouselItems {
  overflow-x: hidden;
  scroll-behavior: smooth;
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none;    /* Firefox */
}

.carouselItems::-webkit-scrollbar {
  display: none;
}

.itemsWrapper {
  display: flex;
  gap: 1rem;
  padding: 1rem;
}

/* Add responsive padding adjustments */
@media (max-width: 1440px) {
  .carouselContainer {
    padding: 0 10vw;
  }
}

@media (max-width: 768px) {
  .carouselContainer {
    padding: 0 5vw;
  }
}

/* Card wrapper */
.cardWrapper {
  flex: 0 0 auto;
}

/* Scroll buttons */
.scrollButtons {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin: 1rem 1rem 0 0;
}

.scrollButton {
  height: 2.5rem;
  width: 2.5rem;
  border: none;
  border-radius: 50%;
  background-color: #e5e7eb;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: opacity 0.3s;
}

.scrollButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.icon {
  height: 1.5rem;
  width: 1.5rem;
  color: #6b7280;
}

/* Card styles */
.card {
  position: relative;
  border: none;
  border-radius: 1.5rem;
  overflow: hidden;
  background-color: #f3f4f6;
  padding: 0;
  cursor: pointer;
  width: 350px;
  height: 650px;
}

.cardOverlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(0,0,0,0.3), transparent);
  z-index: 1;
}

.cardContent {
  position: absolute;
  top: 2rem;
  right: 2rem;
  left: 2rem;
  text-align: left;
  z-index: 2;
  padding: 0.5rem;
  border-radius: 5px;
}

.cardCategory {
  color: #fff;
  font-size: 1rem;
  margin: 0;
}

.cardTitle {
  color: #fff;
  font-size: 22px;
  font-weight: 600;
  margin: 1rem 0 0;
}

.cardImage {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 0;
}

.cardExtra {
  position: relative;
  z-index: 3;
  padding: 0.5rem 1rem;
  color: #fff;
}

/* Pop-out modal styles */
.popOutWrapper {
  position: relative;
  z-index: 100;
}

.popOutBackdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 10;
  /* Optional blur effect:
  backdrop-filter: blur(3px);
  */
}

.popOutContainer {
  position: fixed;
  top: 0;
  left: 0;
  /* Make the container scrollable if content is tall */
  width: 100%;
  height: 100%;
  display: grid;
  place-items: center;
  overflow-y: auto;
  padding: 2rem;  /* Adds space so content doesn't touch screen edges */
  box-sizing: border-box;
  z-index: 101;
}

.popOutContent {
  position: relative;
  width: 90%;
  max-width: 600px;
  max-height: 90vh; /* Ensures popout won't exceed screen height */
  background-color: #fff;
  border-radius: 1.5rem;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  z-index: 103;
  /* Subtle box-shadow to lift it off the background */
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.popOutHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #eee;
}

.popOutTitle {
  font-size: 1.5rem;
  color: #333;
  margin: 0;
  flex: 1;
}

.closeButton {
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f3f4f6;
  }
}

.closeIcon {
  height: 1.5rem;
  width: 1.5rem;
  color: #6b7280;
}

.popOutImage {
  width: 100%;
  height: 15rem;
  object-fit: cover;
}

/* Scrollable text area within the pop-out */
.popOutText {
  padding: 1rem;
  overflow-y: auto;
}

.viewMoreButton {
  display: block;
  width: 100%;
  padding: 1rem;
  background-color: #10b981; /* Green color */
  color: #fff;
  font-size: 1rem;
  font-weight: bold;
  text-align: center;
  text-decoration: none;
  border: none;
  border-radius: 0 0 1.5rem 1.5rem; /* Rounded bottom corners matching popOutContent */
  transition: background-color 0.3s ease-in-out;
  cursor: pointer;
}

.viewMoreButton:hover {
  background-color: #059669;
}

.popOutDescription {
  color: #4b5563;
  margin: 0.5rem 0;
}

.popOutContentText {
  color: #4b5563;
  line-height: 1.6;        /* Increase line spacing */
  padding: 0.5rem 1rem;    /* Add side padding for a cleaner look */
}

.popOutContentText p {
  margin-bottom: 1rem;     /* Space below each paragraph */
}

.popOutContentText ul {
  list-style-type: disc;
  margin: 1rem 0;
  padding-left: 2rem;
}

.popOutContentText li {
  margin-bottom: 0.5rem;
}

/* Content wrappers for card content */
.contentWrapper {
  padding: 0.5rem;
}

.contentSubWrapper {
  margin-top: 0.5rem;
}

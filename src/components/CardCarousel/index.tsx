
// AppleCardsCarouselDemo.tsx

"use client";
import { useEffect, useRef, useState, createContext, ReactNode } from "react";
import { motion, AnimatePresence, useScroll, useTransform } from "framer-motion";
import { IconArrowNarrowLeft, IconArrowNarrowRight, IconX } from "@tabler/icons-react";
import { Link } from "react-router-dom"; // Using Link for client-side navigation
import { useOutsideClick } from "../../../hooks/use-outside-click";
import styles from "./index.module.scss";
import AppDesign from "../../assets/AppDesign.jpg";
import GraphicDesign from "../../assets/GraphicDesign.jpg";
import Automation from "../../assets/Automation.jpg";
import Emails from "../../assets/Emails.jpg";
import WebDevelopment from "../../assets/WebDevelopment.jpg";
import Marketing from "../../assets/Marketing.jpg";

// Define interfaces for our data structures
export interface CardItem {
  category: string;
  title: string;
  src: string;
  description?: string;
  content: ReactNode | (() => ReactNode);
  ctaLink?: string;
}

interface CarouselContextType {
  onCardClose: () => void;
  currentIndex: number;
}

/* Context to share carousel data */
export const CarouselContext = createContext<CarouselContextType>({
  onCardClose: () => {},
  currentIndex: 0,
});

/* Pop-out modal for a selected card */
interface PopOutBoxProps {
  activeItem: CardItem | null;
  setActiveItem: React.Dispatch<React.SetStateAction<CardItem | null>>;
}

export function PopOutBox({ activeItem, setActiveItem }: PopOutBoxProps): JSX.Element | null {
  const popOutRef = useRef<HTMLDivElement | null>(null);

  useOutsideClick(popOutRef, () => {
    setActiveItem(null);
  });

  if (!activeItem) return null;

  return (
    <div className={styles.popOutWrapper}>
      <AnimatePresence>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className={styles.popOutBackdrop}
        />
      </AnimatePresence>
      <AnimatePresence>
        {activeItem && (
          <div className={styles.popOutContainer}>
            <motion.div
              ref={popOutRef}
              layoutId={`card-${activeItem.title}`}
              className={styles.popOutContent}
            >
              <div className={styles.popOutHeader}>
                <motion.h3 className={styles.popOutTitle}>{activeItem.title}</motion.h3>
                <motion.button
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className={styles.closeButton}
                  onClick={() => setActiveItem(null)}
                >
                  <IconX className={styles.closeIcon} />
                </motion.button>
              </div>
              <motion.div layoutId={`image-${activeItem.title}`}>
                <img src={activeItem.src} alt={activeItem.title} className={styles.popOutImage} />
              </motion.div>
              <div className={styles.popOutText}>
                {activeItem.description && (
                  <motion.p className={styles.popOutDescription}>
                    {activeItem.description}
                  </motion.p>
                )}
                <motion.div className={styles.popOutContentText}>
                  {typeof activeItem.content === "function"
                    ? activeItem.content()
                    : activeItem.content}
                </motion.div>
              </div>
              {activeItem.ctaLink && (
                <motion.div className={styles.viewMoreWrapper}>
                  <Link to={activeItem.ctaLink} className={styles.viewMoreButton}>
                    View More
                  </Link>
                </motion.div>
              )}
            </motion.div>
          </div>
        )}
      </AnimatePresence>
    </div>
  );

}

/* Carousel component that scrolls horizontally and shows scroll buttons */
interface CarouselProps {
  items: CardItem[];
  initialScroll?: number;
}

export const Carousel = ({ items, initialScroll = 0 }: CarouselProps): JSX.Element => {
  const carouselRef = useRef<HTMLDivElement | null>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);
  const [currentIndex] = useState(0);
  const [activeItem, setActiveItem] = useState<CardItem | null>(null);

  useEffect(() => {
    if (carouselRef.current) {
      carouselRef.current.scrollLeft = initialScroll;
      checkScrollability();
    }
  }, [initialScroll]);

  const checkScrollability = () => {
    if (carouselRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = carouselRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft + clientWidth < scrollWidth);
    }
  };

  const scrollLeftFunc = () => {
    if (carouselRef.current) {
      carouselRef.current.scrollBy({ left: -300, behavior: "smooth" });
    }
  };

  const scrollRightFunc = () => {
    if (carouselRef.current) {
      carouselRef.current.scrollBy({ left: 300, behavior: "smooth" });
    }
  };

  // Define onCardClose function for context
  const onCardClose = () => {
    setActiveItem(null);
  };

  return (
    <CarouselContext.Provider value={{ currentIndex, onCardClose }}>
      <div className={styles.carouselContainer}>
        <div className={styles.carouselItems} ref={carouselRef} onScroll={checkScrollability}>
          <div className={styles.itemsWrapper}>
            {items.map((item: CardItem, index: number) => (
              <motion.div
                key={"card" + index}
                initial={{ opacity: 0, y: 20 }}
                animate={{
                  opacity: 1,
                  y: 0,
                  transition: { duration: 0.5, delay: 0.2 * index, ease: "easeOut" },
                }}
                className={styles.cardWrapper}
              >
                <Card card={item} setActiveItem={setActiveItem} />
              </motion.div>
            ))}
          </div>
        </div>
        <div className={styles.scrollButtons}>
          <button
            className={styles.scrollButton}
            onClick={scrollLeftFunc}
            disabled={!canScrollLeft}
          >
            <IconArrowNarrowLeft className={styles.icon} />
          </button>
          <button
            className={styles.scrollButton}
            onClick={scrollRightFunc}
            disabled={!canScrollRight}
          >
            <IconArrowNarrowRight className={styles.icon} />
          </button>
        </div>
      </div>
      <PopOutBox activeItem={activeItem} setActiveItem={setActiveItem} />
    </CarouselContext.Provider>
  );
};

/* Card component for each carousel item */
interface CardProps {
  card: CardItem;
  setActiveItem: React.Dispatch<React.SetStateAction<CardItem | null>>;
}

export const Card = ({ card, setActiveItem }: CardProps): JSX.Element => {
  return (
    <motion.button onClick={() => setActiveItem(card)} className={styles.card}>
      <div className={styles.cardOverlay} />
      <div className={styles.cardContent}>
        <motion.p className={styles.cardCategory}>{card.category}</motion.p>
        <motion.p className={styles.cardTitle}>{card.title}</motion.p>
      </div>
      <img src={card.src} alt={card.title} className={styles.cardImage} />
    </motion.button>
  );
};

/* Updated Services data with more informative content */
const servicesData = [
  {
    category: "Web Development",
    title: "Building robust and scalable web solutions",
    src: WebDevelopment,
    ctaLink: "/services/web-development",
    content: (
      <div className={styles.contentWrapper}>
        <p>
          We create high-performing websites and web applications using modern frameworks like
          React, Node.js, Django, and more. Our team ensures each solution is:
        </p>
        <ul>
          <li>Optimized for speed and reliability</li>
          <li>Scalable to accommodate future growth</li>
          <li>Secure with best-in-class data protection practices</li>
          <li>Responsive across all devices</li>
        </ul>
        <p>
          From e-commerce platforms to custom portals, we tailor each project to your unique business
          goals and user needs.
        </p>
      </div>
    ),
  },
  {
    category: "App Development",
    title: "Empowering businesses with custom applications",
    src: AppDesign,
    ctaLink: "/services/app-development",
    content: (
      <div className={styles.contentWrapper}>
        <p>
          Whether you need a mobile app, desktop solution, or cross-platform experience, our experts
          deliver intuitive applications that engage users and drive results:
        </p>
        <ul>
          <li>iOS and Android native development</li>
          <li>Cross-platform solutions (e.g., React Native, Flutter)</li>
          <li>Performance-focused architecture and UX design</li>
          <li>Ongoing support and feature enhancements</li>
        </ul>
        <p>
          We work closely with you to ensure each application aligns with your brand and meets your
          long-term business objectives.
        </p>
      </div>
    ),
  },
  {
    category: "Graphic Design",
    title: "Captivating visuals that define your brand",
    src: GraphicDesign,
    ctaLink: "/services/graphic-design",
    content: (
      <div className={styles.contentWrapper}>
        <p>
          From logos and marketing materials to social media graphics and presentations, our design
          services elevate your brand’s visual identity:
        </p>
        <ul>
          <li>Logo creation and complete brand style guides</li>
          <li>Custom illustrations and iconography</li>
          <li>Print and digital marketing collateral</li>
          <li>Consistent, on-brand design elements</li>
        </ul>
        <p>
          We blend creativity with strategy to ensure every design piece resonates with your
          audience and strengthens your brand’s presence.
        </p>
      </div>
    ),
  },
  {
    category: "Automation",
    title: "Streamline your workflows with advanced automation",
    src: Automation,
    ctaLink: "/services/automation",
    content: (
      <div className={styles.contentWrapper}>
        <p>
          Free up valuable time and reduce manual tasks by leveraging our automation expertise. We
          help you:
        </p>
        <ul>
          <li>Identify repetitive processes suitable for automation</li>
          <li>Implement workflow tools and APIs for seamless data transfer</li>
          <li>Integrate with popular platforms (CRM, ERP, etc.)</li>
          <li>Monitor and maintain automated systems for reliability</li>
        </ul>
        <p>
          By automating routine tasks, you can focus on strategic initiatives that drive real
          growth.
        </p>
      </div>
    ),
  },
  {
    category: "Marketing",
    title: "Boost your online visibility and engagement",
    src: Marketing,
    ctaLink: "/services/marketing",
    content: (
      <div className={styles.contentWrapper}>
        <p>
          Reach the right audience with our comprehensive marketing services. We develop and execute
          strategies that include:
        </p>
        <ul>
          <li>Search Engine Optimization (SEO) and content marketing</li>
          <li>Pay-per-click (PPC) campaigns and social media advertising</li>
          <li>Analytics and performance tracking</li>
          <li>Conversion rate optimization (CRO) for higher ROI</li>
        </ul>
        <p>
          Our data-driven approach ensures every marketing effort aligns with your goals and
          delivers measurable results.
        </p>
      </div>
    ),
  },
  {
    category: "Email",
    title: "Drive conversions with strategic email campaigns",
    src: Emails,
    ctaLink: "/services/email",
    content: (
      <div className={styles.contentWrapper}>
        <p>
          Maximize your customer engagement and retention through effective email marketing. Our
          services include:
        </p>
        <ul>
          <li>Newsletter design and automation</li>
          <li>Segmentation and personalization strategies</li>
          <li>Drip campaigns to nurture leads</li>
          <li>Analytics to track open rates, clicks, and conversions</li>
        </ul>
        <p>
          With carefully crafted content and targeted messaging, we help you build lasting
          relationships with your subscribers.
        </p>
      </div>
    ),
  },
];

/* Main demo component with a scroll-driven animation */
export default function AppleCardsCarouselDemo() {
  const { scrollYProgress } = useScroll();
  const y = useTransform(scrollYProgress, [0.1, 0.3], [300, 0]);
  const opacity = useTransform(scrollYProgress, [0.2, 0.3, 0.3, 0.45], [0, 1, 1, 0]);
  const titleY = useTransform(scrollYProgress, [0, 1], [-50, 0]);
  const titleOpacity = useTransform(scrollYProgress, [0.3, 0.3], [0, 1]);
  const scaleExit = useTransform(scrollYProgress, [0.5, 0.7], [1, 0.7]);
  const fadeExit = useTransform(scrollYProgress, [0.5, 0.6], [1, 0]);

  return (
    <motion.div className={styles.mainContainer} style={{ y, opacity }}>
      <motion.h2 className={styles.mainTitle} style={{ y: titleY, opacity: titleOpacity }}>
        OUR SERVICES
      </motion.h2>
      <motion.div style={{ scale: scaleExit, opacity: fadeExit }}>
        <Carousel items={servicesData} />
      </motion.div>
    </motion.div>
  );
}

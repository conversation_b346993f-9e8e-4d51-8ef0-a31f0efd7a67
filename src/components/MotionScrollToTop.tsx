import { useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { useIsPresent } from 'framer-motion';

/**
 * Enhanced ScrollToTop component that works with Framer Motion animations.
 * This component will scroll to top when the route changes,
 * but only after Framer Motion exit animations have completed.
 */
function MotionScrollToTop(): null {
  const { pathname } = useLocation();
  const isPresent = useIsPresent();

  useEffect(() => {
    // Only scroll to top when the new page has appeared
    if (isPresent) {
      window.scrollTo(0, 0);
    }
  }, [pathname, isPresent]);

  return null;
}

export default MotionScrollToTop;

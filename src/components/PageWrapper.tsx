import React, { useEffect } from 'react';
import { motion } from 'framer-motion';

interface PageWrapperProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * Page wrapper component with automatic scroll-to-top functionality.
 * This component ensures that each page starts at the top when navigated to.
 * It also applies consistent motion animations for page transitions.
 * 
 * Usage:
 * <PageWrapper>
 *   <YourPageContent />
 * </PageWrapper>
 */
const PageWrapper: React.FC<PageWrapperProps> = ({ children, className = '' }) => {
  // Scroll to top when the component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  // Animation variants
  const pageVariants = {
    initial: {
      opacity: 0,
      y: 20
    },
    animate: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: 'easeInOut'
      }
    },
    exit: {
      opacity: 0,
      y: -20,
      transition: {
        duration: 0.3,
        ease: 'easeInOut'
      }
    }
  };

  return (
    <motion.div
      className={`page-wrapper ${className}`}
      initial="initial"
      animate="animate"
      exit="exit"
      variants={pageVariants}
    >
      {children}
    </motion.div>
  );
};

export default PageWrapper;

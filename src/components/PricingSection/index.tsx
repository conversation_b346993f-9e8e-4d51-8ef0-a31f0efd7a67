
import { useEffect } from "react";
import { motion, useAnimation } from "framer-motion";
import { useInView } from "react-intersection-observer";
import PricingCard from "../PricingCard";
import { PricingPlan } from "../../types";

interface PricingSectionProps {
  pricingPlans: PricingPlan[];
}

const PricingSection = ({ pricingPlans }: PricingSectionProps): JSX.Element => {
  const controls = useAnimation();
  const [ref, inView] = useInView({
    threshold: 0.2,
    triggerOnce: false, // Changed to false to allow repeated animations
  });

  useEffect(() => {
    if (inView) {
      controls.start({
        y: 0,
        opacity: 1,
        transition: {
          duration: 0.8,
          ease: "easeOut"
        }
      });
    } else {
      controls.start({
        y: 50,
        opacity: 0,
        transition: {
          duration: 0.8,
          ease: "easeIn"
        }
      });
    }
  }, [controls, inView]);

  return (
    <section id="pricing" className="bg-[#121B1B] py-20">
      <div className="container mx-auto px-4" ref={ref}>
        <motion.h2
          initial={{ y: 50, opacity: 0 }}
          animate={controls}
          className="text-6xl font-bold text-white text-center mb-[120px]"
        >
          CHOOSE YOUR PERFECT PACKAGE
        </motion.h2>
        <motion.div
          initial={{ y: 50, opacity: 0 }}
          animate={controls}
          className="flex flex-wrap justify-center gap-16"
        >
          {pricingPlans.map((plan, index) => (
            <PricingCard
              key={index}
              plan={plan.plan}
              price={plan.price}
              regularPrice={plan.regularPrice}
              description={plan.description}
              features={plan.features}
              isPopular={plan.isPopular}
            />
          ))}
        </motion.div>
      </div>
    </section>
  );
};



export default PricingSection;


import { useState, useRef, useEffect, useCallback, useMemo, ChangeEvent } from 'react';
import { motion, useInView } from 'framer-motion';
import styles from './index.module.scss';
import { CRM_CONFIG, createQuotePayload, QuizAnswersData } from '../../config/crm';

interface QuoteQuizStepProps {
  step: number;
  totalSteps: number;
  question: string;
  options: string[];
  selectedAnswer: any;
  saveAnswer: (key: string, value: any, shouldAdvance?: boolean) => void;
  prevStep: (() => void) | null;
  nextStep: () => void;
  isMultiSelect?: boolean;
  stepKey: string;
}

const QuoteQuizStep = ({
  step,
  totalSteps,
  question,
  options,
  selectedAnswer,
  saveAnswer,
  prevStep,
  nextStep,
  isMultiSelect = false,
  stepKey,
}: QuoteQuizStepProps): JSX.Element => {
  // Initialize input value from selected answer
  const [inputValue, setInputValue] = useState(() => {
    if (question.includes("email")) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(selectedAnswer) ? selectedAnswer : "";
    }
    if (options[0] === "Form") {
      return selectedAnswer || "";
    }
    if (question.includes("requirements") || question.includes("describe")) {
      return selectedAnswer || "";
    }
    if (options[0] === "Text box") {
      return selectedAnswer || "";
    }
    return selectedAnswer || "";
  });

  // For multi-select, track selected options as an array
  const [selectedOptions, setSelectedOptions] = useState(() => {
    return isMultiSelect && Array.isArray(selectedAnswer) ? selectedAnswer : [];
  });

  // Create separate refs for input and textarea
  const inputRef = useRef<HTMLInputElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleOptionClick = (option: string): void => {
    if (isMultiSelect) {
      // Toggle selection for multi-select
      const newSelection = selectedOptions.includes(option)
        ? selectedOptions.filter((item: string) => item !== option)
        : [...selectedOptions, option];

      setSelectedOptions(newSelection);
      saveAnswer(stepKey, newSelection);
    } else {
      // Single selection - save and advance immediately
      saveAnswer(stepKey, option, true);
    }
  };

  const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>): void => {
    setInputValue(e.target.value);
    // Save immediately for text inputs
    saveAnswer(stepKey, e.target.value);
  };

  const handleInputBlur = () => {
    if (inputValue.trim()) {
      saveAnswer(stepKey, inputValue);
    }
  };

  const handleNextClick = () => {
    if (isMultiSelect) {
      if (selectedOptions.length > 0) {
        saveAnswer(stepKey, selectedOptions);
        nextStep();
      }
    } else if (options.length === 1) {
      if (inputValue.trim()) {
        saveAnswer(stepKey, inputValue);
        nextStep();
      }
    } else {
      if (selectedAnswer) {
        nextStep();
      }
    }
  };

  useEffect(() => {
    // Focus on the appropriate input element based on the question type
    if (options.length === 1 && options[0] === 'Description box') {
      if (textareaRef.current) {
        textareaRef.current.focus();
      }
    } else if (options.length === 1 && (options[0] === 'Text box' || options[0] === 'Form')) {
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }
  }, [step, options]);

  // Update local state when selectedAnswer changes
  useEffect(() => {
    if (selectedAnswer && options[0] === "Text box") {
      setInputValue(selectedAnswer);
    }
  }, [selectedAnswer, options]);

  return (
    <div className={styles.quizStepContainer}>
      <div className={styles.progressContainer}>
        {Array.from({ length: totalSteps }, (_, i) => (
          <div
            key={i}
            className={`${styles.stepIndicator} ${
              i + 1 === step
                ? styles.activeStep
                : i + 1 < step
                ? styles.completedStep
                : styles.inactiveStep
            }`}
          >
            <span className={styles.stepNumber}>{i + 1}</span>
          </div>
        ))}
      </div>

      <h2 className={styles.question}>
        {question}
        {isMultiSelect && <span className={styles.subtext}>(Select all that apply)</span>}
      </h2>

      <div className={styles.optionsContainer}>
        {options.length === 1 && options[0] === 'Form' ? (
          <div className={styles.formContainer}>
            <input
              type="text"
              placeholder="Your Name"
              className={styles.inputField}
              value={inputValue.split('|')[0] || ''}
              onChange={(e) => {
                const parts = inputValue.split('|') || ['', ''];
                parts[0] = e.target.value;
                const newValue = parts.join('|');
                setInputValue(newValue);
                saveAnswer(stepKey, newValue);
              }}
              onBlur={handleInputBlur}
              ref={inputRef}
            />
            <input
              type="tel"
              placeholder="Phone Number"
              className={styles.inputField}
              value={inputValue.split('|')[1] || ''}
              onChange={(e) => {
                const parts = inputValue.split('|') || ['', ''];
                parts[1] = e.target.value;
                const newValue = parts.join('|');
                setInputValue(newValue);
                saveAnswer(stepKey, newValue);
              }}
              onBlur={handleInputBlur}
            />
          </div>
        ) : options.length === 1 && options[0] === 'Description box' ? (
          <textarea
            ref={textareaRef}
            placeholder="Describe what you're looking for..."
            className={styles.textareaField}
            value={inputValue}
            onChange={handleInputChange}
            onBlur={handleInputBlur}
            rows={6}
          />
        ) : options.length === 1 && options[0] === 'Text box' ? (
          <input
            ref={inputRef}
            type={question.includes("email") ? "email" : "text"}
            placeholder={question.includes("email") ? "Enter your email address" : "Enter your answer"}
            className={styles.inputField}
            value={inputValue}
            onChange={handleInputChange}
            onBlur={handleInputBlur}
          />
        ) : (
          options.map((option, index) => (
            <button
              key={index}
              onClick={() => handleOptionClick(option)}
              className={`${styles.optionButton} ${
                isMultiSelect
                  ? selectedOptions.includes(option) ? styles.selectedOption : ""
                  : selectedAnswer === option ? styles.selectedOption : ""
              }`}
            >
              {option}
              {isMultiSelect && selectedOptions.includes(option) && (
                <span className={styles.checkmark}>✓</span>
              )}
            </button>
          ))
        )}
      </div>

      <div className={styles.navButtons}>
        {prevStep && (
          <button onClick={prevStep} className={styles.backButton}>
            Back
          </button>
        )}
        {(options.length === 1 || isMultiSelect) && (
          <button
            onClick={handleNextClick}
            className={`${styles.nextButton} ${
              (options.length === 1 && !inputValue.trim()) ||
              (isMultiSelect && selectedOptions.length === 0)
                ? styles.disabledButton
                : ''
            }`}
            disabled={
              (options.length === 1 && !inputValue.trim()) ||
              (isMultiSelect && selectedOptions.length === 0)
            }
          >
            Next
          </button>
        )}
      </div>
    </div>
  );
};



const QuoteQuiz = () => {
  const [currentStep, setCurrentStep] = useState<number>(1);
  const [answers, setAnswers] = useState<QuizAnswersData>({});
  const [dynamicSteps, setDynamicSteps] = useState<any[]>([]);
  const [error, setError] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [submitSuccess, setSubmitSuccess] = useState<boolean>(false);
  const ref = useRef(null);
  const isInView = useInView(ref, { once: false, amount: 0.3 });

  // Define question bank item interface
  interface QuestionBankItem {
    question: string;
    options: string[];
    isMultiSelect?: boolean;
    key: string;
  }

  interface QuestionBank {
    [key: string]: QuestionBankItem;
  }

  // Define all possible questions
  const questionBank = useMemo<QuestionBank>(() => ({
    services: {
      question: 'Which services are you interested in?',
      options: [
        'Web Development',
        'App Development',
        'Graphic Design',
        'Automation',
        'Marketing',
        'Email Services'
      ],
      isMultiSelect: true,
      key: 'services'
    },
    websiteType: {
      question: 'What type of website do you need?',
      options: ['E-commerce (Sell products online)', 'Business/Portfolio website'],
      isMultiSelect: false,
      key: 'websiteType'
    },
    appType: {
      question: 'What type of app do you need?',
      options: ['iOS App', 'Android App', 'Cross-platform App', 'Web App'],
      isMultiSelect: false,
      key: 'appType'
    },
    designType: {
      question: 'What design services do you need?',
      options: ['Logo Design', 'Brand Identity', 'Marketing Materials', 'UI/UX Design', 'Other'],
      isMultiSelect: false,
      key: 'designType'
    },
    automationType: {
      question: 'What type of automation do you need?',
      options: ['Workflow Automation', 'Data Integration', 'Process Automation', 'Custom Solutions'],
      isMultiSelect: false,
      key: 'automationType'
    },
    marketingType: {
      question: 'What marketing services do you need?',
      options: ['SEO', 'Social Media Marketing', 'Content Marketing', 'PPC Advertising', 'Email Marketing'],
      isMultiSelect: false,
      key: 'marketingType'
    },
    emailType: {
      question: 'What email services do you need?',
      options: ['Email Campaign Design', 'Email Automation', 'Newsletter Setup', 'Email Template Design'],
      isMultiSelect: false,
      key: 'emailType'
    },
    requirements: {
      question: 'Please describe your project requirements in detail',
      options: ['Description box'],
      isMultiSelect: false,
      key: 'requirements'
    },
    businessType: {
      question: 'What type of business is this for?',
      options: [
        'Sole trader / self-employed',
        'Small business (1-10 employees)',
        'Medium business (10-50 employees)',
        'Large business (50+ employees)',
      ],
      isMultiSelect: false,
      key: 'businessType'
    },
    existingWebsite: {
      question: 'Do you have an existing website?',
      options: ['No', 'Yes'],
      isMultiSelect: false,
      key: 'existingWebsite'
    },
    timeline: {
      question: 'When do you need the project completed?',
      options: ['As soon as possible', 'Within a month', 'Within a few months'],
      isMultiSelect: false,
      key: 'timeline'
    },
    budget: {
      question: 'What is your estimated budget?',
      options: ['£500 - £2,999', '£3,000 - £5,999', '£6,000 - £9,999', '£10,000+'],
      isMultiSelect: false,
      key: 'budget'
    },
    email: {
      question: 'What email would you like the quote sent to?',
      options: ['Text box'],
      isMultiSelect: false,
      key: 'email'
    },
    contactDetails: {
      question: 'Your contact details',
      options: ['Form'],
      isMultiSelect: false,
      key: 'contactDetails'
    }
  }), []);

  // Function to build dynamic steps based on selected services
  const buildDynamicSteps = useCallback((selectedServices: string[] | undefined) => {
    const steps = [questionBank.services];

    if (selectedServices && selectedServices.length > 0) {
      // If multiple services are selected, go directly to requirements
      if (selectedServices.length > 1) {
        steps.push(questionBank.requirements);
      } else {
        // If only one service is selected, show specific question for that service
        if (selectedServices.includes('Web Development')) {
          steps.push(questionBank.websiteType);
        }
        if (selectedServices.includes('App Development')) {
          steps.push(questionBank.appType);
        }
        if (selectedServices.includes('Graphic Design')) {
          steps.push(questionBank.designType);
        }
        if (selectedServices.includes('Automation')) {
          steps.push(questionBank.automationType);
        }
        if (selectedServices.includes('Marketing')) {
          steps.push(questionBank.marketingType);
        }
        if (selectedServices.includes('Email Services')) {
          steps.push(questionBank.emailType);
        }

        // Then add requirements after the specific question
        steps.push(questionBank.requirements);
      }
    } else {
      // If no services selected yet, still add requirements as fallback
      steps.push(questionBank.requirements);
    }

    // Add common questions after requirements
    steps.push(
      questionBank.businessType,
      questionBank.existingWebsite,
      questionBank.timeline,
      questionBank.budget,
      questionBank.email,
      questionBank.contactDetails
    );

    return steps;
  }, [questionBank]);

  // Initialize with default steps
  useEffect(() => {
    setDynamicSteps(buildDynamicSteps([]));
  }, [buildDynamicSteps]);

  // Update steps when services are selected
  useEffect(() => {
    if (answers.services) {
      const newSteps = buildDynamicSteps(answers.services);
      setDynamicSteps(newSteps);
    }
  }, [answers.services, buildDynamicSteps]);

  const getCurrentStepData = () => {
    return dynamicSteps[currentStep - 1] || {};
  };

  const validateStep = (_: string, value: any): boolean => {
    if (!value || value === '') {
      return false;
    }

    if (Array.isArray(value) && value.length === 0) {
      return false;
    }

    return true;
  };

  const nextStep = () => {
    const currentStepData = getCurrentStepData();
    const currentAnswer = answers[currentStepData.key];

    // Log for debugging
    console.log('Current step key:', currentStepData.key);
    console.log('Current answer:', currentAnswer);
    console.log('All answers:', answers);

    if (!validateStep(currentStepData.key, currentAnswer)) {
      setError(currentStepData.isMultiSelect ? 'Please select at least one option.' : 'Please complete this step before proceeding.');
      return;
    }

    // Email validation
    if (currentStepData.key === 'email') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(currentAnswer)) {
        setError('Please enter a valid email address.');
        return;
      }
    }

    // Contact details validation
    if (currentStepData.key === 'contactDetails') {
      if (!currentAnswer || !currentAnswer.includes('|')) {
        setError('Please provide both your name and phone number.');
        return;
      }

      const [name, phone] = currentAnswer.split('|');
      if (!name.trim() || !phone.trim()) {
        setError('Please provide both your name and phone number.');
        return;
      }
    }

    setError('');
    setCurrentStep((prev) => prev + 1);
  };

  const prevStep = () => {
    setError('');
    setCurrentStep((prev) => prev - 1);
  };

  const saveAnswer = (key: string, answer: any, shouldAdvance = false) => {
    console.log('Saving answer:', key, answer);
    setAnswers((prev: QuizAnswersData) => ({
      ...prev,
      [key]: answer
    }));

    // If shouldAdvance is true and it's not multi-select, advance after state update
    if (shouldAdvance) {
      // Using Promise to ensure state is updated
      Promise.resolve().then(() => {
        if (validateStep(key, answer)) {
          setError('');
          setCurrentStep((prev) => prev + 1);
        }
      });
    }
  };

  const submitQuiz = async () => {
    setIsSubmitting(true);
    setError('');

    console.log('Final answers before submission:', answers);
    const crmQuoteData = createQuotePayload(answers);

    try {
      const apiUrl = `${CRM_CONFIG.API_URL}${CRM_CONFIG.QUOTES_ENDPOINT}`;
      console.log("Submitting to CRM:", apiUrl);
      console.log("Quote data:", crmQuoteData);

      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${CRM_CONFIG.API_TOKEN}`
        },
        body: JSON.stringify(crmQuoteData),
      });

      if (response.ok) {
        const responseData = await response.json();
        console.log("Submission successful!", responseData);
        setSubmitSuccess(true);
      } else {
        console.error("Submission failed:", response.status);
        const errorText = await response.text();
        console.error("Error response:", errorText);
        setError("Failed to submit your request. Please try again later or contact us directly.");
      }
    } catch (error) {
      console.error('Failed to submit:', error);
      setError(`Failed to submit your request. Please try again later or contact us <NAME_EMAIL>.`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const currentStepData = getCurrentStepData();

  return (
    <motion.div
      ref={ref}
      className={styles.container}
      initial={{ opacity: 0 }}
      animate={{ opacity: isInView ? 1 : 0 }}
      transition={{ duration: 0.3 }}
    >
      <h2 className={styles.quizTitle}>
        GET A CUSTOM QUOTE
      </h2>

      {currentStep <= dynamicSteps.length ? (
        <QuoteQuizStep
          key={`step-${currentStep}-${currentStepData.key}`}
          step={currentStep}
          totalSteps={dynamicSteps.length}
          question={currentStepData.question}
          options={currentStepData.options}
          selectedAnswer={answers[currentStepData.key]}
          saveAnswer={saveAnswer}
          nextStep={nextStep}
          prevStep={currentStep > 1 ? prevStep : null}
          isMultiSelect={currentStepData.isMultiSelect || false}
          stepKey={currentStepData.key}
        />
      ) : (
        <div className={styles.thankYouContainer}>
          {submitSuccess ? (
            <>
              <h2 className={styles.thankYouTitle}>
                Thank you! Your quote request has been submitted.
              </h2>
              <p className={styles.thankYouMessage}>
                We&apos;ll send a detailed quote to {answers.email} shortly. Your request has been added to our CRM system for tracking.
              </p>
            </>
          ) : (
            <>
              <h2 className={styles.thankYouTitle}>
                Thank you! Please submit your information to receive a quote.
              </h2>
              <p className={styles.summaryText}>
                We&apos;ll create a custom quote based on your requirements:
              </p>
              <div className={styles.summaryContainer}>
                <div className={styles.summaryItem}>
                  <span className={styles.summaryLabel}>Services:</span>
                  <span className={styles.summaryValue}>
                    {Array.isArray(answers.services) ? answers.services.join(', ') : ''}
                  </span>
                </div>
                {answers.websiteType && (
                  <div className={styles.summaryItem}>
                    <span className={styles.summaryLabel}>Website Type:</span>
                    <span className={styles.summaryValue}>{answers.websiteType}</span>
                  </div>
                )}
                {answers.appType && (
                  <div className={styles.summaryItem}>
                    <span className={styles.summaryLabel}>App Type:</span>
                    <span className={styles.summaryValue}>{answers.appType}</span>
                  </div>
                )}
                <div className={styles.summaryItem}>
                  <span className={styles.summaryLabel}>Business Type:</span>
                  <span className={styles.summaryValue}>{answers.businessType}</span>
                </div>
                <div className={styles.summaryItem}>
                  <span className={styles.summaryLabel}>Budget Range:</span>
                  <span className={styles.summaryValue}>{answers.budget}</span>
                </div>
                <div className={styles.summaryItem}>
                  <span className={styles.summaryLabel}>Email:</span>
                  <span className={styles.summaryValue}>{answers.email}</span>
                </div>
              </div>
              <button
                className={`${styles.submitButton} ${isSubmitting ? styles.submitting : ''}`}
                onClick={submitQuiz}
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Submitting...' : 'Submit Request'}
              </button>
            </>
          )}
        </div>
      )}

      {error && (
        <div className={styles.errorMessage}>
          {error}
        </div>
      )}
    </motion.div>
  );
};

export default QuoteQuiz;

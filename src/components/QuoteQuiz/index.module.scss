.container {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  min-height: 600px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-bottom: 200px;
}

.quizTitle {
  font-size: 4rem; /* text-6xl */
  font-weight: bold;
  text-align: center;
  color: #ffffff;
  margin-bottom: 200px;
}

.quizStepContainer {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem;
  background: rgb(26 38 38 / var(--tw-bg-opacity));
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 0.5px solid #23452e;
}

.progressContainer {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 2rem;
}

.stepIndicator {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  transition: all 0.3s ease;
  
  &.activeStep {
    background-color: #4ecdc4;
    color: white;
    transform: scale(1.1);
  }
  
  &.completedStep {
    background-color: #44a19f;
    color: white;
  }
  
  &.inactiveStep {
    background-color: rgba(255, 255, 255, 0.1);
    color: #666;
  }
}

.stepNumber {
  font-size: 1rem;
}

.question {
  font-size: 1.75rem;
  font-weight: 600;
  text-align: center;
  margin-bottom: 2rem;
  color: white;
  line-height: 1.4;
}

.subtext {
  display: block;
  font-size: 1rem;
  color: #aaa;
  margin-top: 0.5rem;
  font-weight: normal;
}

.optionsContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 2rem;
  width: 100%;
}

.optionButton {
  width: 100%;
  padding: 1.2rem 1.5rem;
  font-size: 1.1rem;
  font-weight: 500;
  text-align: left;
  background: rgba(255, 255, 255, 0.05);
  border: 0.5px solid #23452e;
  border-radius: 10px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
  }
  
  &.selectedOption {
    background: rgba(78, 205, 196, 0.2);
    border-color: #4ecdc4;
    color: white;
    
    &:hover {
      background: rgba(78, 205, 196, 0.3);
    }
  }
}

.checkmark {
  font-size: 1.2rem;
  color: #4ecdc4;
  font-weight: bold;
  margin-left: 1rem;
}

.formContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  width: 100%;
}

.inputField, .textareaField {
  width: 100%;
  padding: 1rem 1.5rem;
  font-size: 1.1rem;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: white;
  transition: all 0.3s ease;
  
  &::placeholder {
    color: #888;
  }
  
  &:focus {
    outline: none;
    border-color: #4ecdc4;
    background: rgba(255, 255, 255, 0.1);
  }
}

.textareaField {
  resize: vertical;
  min-height: 150px;
  font-family: inherit;
}

.navButtons {
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  margin-top: 2rem;
}

.backButton, .nextButton {
  padding: 0.8rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.backButton {
  background: transparent;
  border: 2px solid rgba(255, 255, 255, 0.2);
  color: white;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.3);
  }
}

.nextButton {
  background: #4ecdc4;
  color: white;
  flex: 1;
  max-width: 200px;
  margin-left: auto;
  
  &:hover:not(:disabled) {
    background: #44a19f;
    transform: translateY(-2px);
  }
  
  &.disabledButton {
    opacity: 0.5;
    cursor: not-allowed;
    
    &:hover {
      transform: none;
      background: #4ecdc4;
    }
  }
}

.submitButton {
  padding: 1rem 3rem;
  font-size: 1.2rem;
  font-weight: 600;
  background: #4ecdc4;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 2rem;
  
  &:hover:not(:disabled) {
    background: #44a19f;
    transform: translateY(-2px);
  }
  
  &:disabled, &.submitting {
    opacity: 0.5;
    cursor: not-allowed;
    
    &:hover {
      transform: none;
      background: #4ecdc4;
    }
  }
}

.thankYouContainer {
  text-align: center;
  padding: 2rem;
  max-width: 600px;
}

.thankYouTitle {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
  color: white;
}

.thankYouMessage, .summaryText {
  font-size: 1.2rem;
  color: #ccc;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.summaryContainer {
  background: rgba(255, 255, 255, 0.05);
  padding: 2rem;
  border-radius: 15px;
  margin-bottom: 2rem;
  text-align: left;
}

.summaryItem {
  display: flex;
  justify-content: space-between;
  padding: 1rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  
  &:last-child {
    border-bottom: none;
  }
}

.summaryLabel {
  font-weight: 600;
  color: #4ecdc4;
}

.summaryValue {
  color: white;
  text-align: right;
  max-width: 60%;
}

.errorMessage {
  background: rgba(255, 59, 48, 0.1);
  border: 1px solid rgba(255, 59, 48, 0.3);
  color: #ff6b6b;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
  text-align: center;
  font-size: 0.9rem;
}

// Responsive styles
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .quizTitle {
    font-size: 2rem;
  }
  
  .question {
    font-size: 1.5rem;
  }
  
  .optionButton {
    padding: 1rem;
    font-size: 1rem;
  }
  
  .navButtons {
    flex-direction: column;
    
    .backButton, .nextButton {
      width: 100%;
      max-width: none;
    }
  }
  
  .summaryItem {
    flex-direction: column;
    gap: 0.5rem;
    
    .summaryValue {
      text-align: left;
      max-width: 100%;
    }
  }
}
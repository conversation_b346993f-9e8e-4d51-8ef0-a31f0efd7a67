
// src/components/BentoGrid/index.tsx
import { ReactNode } from "react";
import { cn } from "../../../lib/utils";
import styles from "./index.module.scss";

interface BentoGridProps {
  className?: string;
  children: ReactNode;
}

export const BentoGrid = ({ className, children }: BentoGridProps): JSX.Element => {
  return (
    <div className={cn(styles.grid, className)}>
      {children}
    </div>
  );
};

interface BentoGridItemProps {
  className?: string;
  title: ReactNode;
  description: ReactNode;
  header?: ReactNode;
  icon?: ReactNode;
}

export const BentoGridItem = ({
  className,
  title,
  description,
  header,
  icon,
}: BentoGridItemProps): JSX.Element => {
  return (
    <div className={cn(styles.item, className)}>
      <div className={styles.content}>
        {header && <div className={styles.header}>{header}</div>}
        {/* Icon Section */}
        {icon && <div className={styles.icon}>{icon}</div>}
        {/* Title Section */}
        <div className={styles.title}>{title}</div>
        {/* Description Section */}
        <div className={styles.description}>{description}</div>
      </div>
    </div>
  );
};

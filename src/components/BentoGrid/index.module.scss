/* src/components/BentoGrid/index.module.scss */
.grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 48px;
    max-width: 7xl;
    margin: 0 auto;
    grid-auto-rows: 22rem; /* Standardized height */

    @media (min-width: 768px) {
      grid-template-columns: repeat(3, 1fr);
      gap: 6px;
    }
  }

  .item {
    border: 0.5px solid var(--lightGreen30);
    border-radius: 1rem;
    transition: all 0.2s ease-in-out;
    box-shadow: var(--shadow-input);
    padding: 0.5rem;
    background-color: var(--darkGreen);
    border: 0.5px solid #23452e;
    display: flex;
    flex-direction: column;
    space-y: 1rem;
    height: 100%; /* Ensure full height of grid cell */

    &:hover {
      box-shadow: var(--shadow-xl);
    }

    @media (min-width: 768px) {
      padding: 1rem;
    }

    &.dark {
      background-color: black;
      box-shadow: none;
    }
  }

  .content {
    flex-grow: 1;
  }

  .header {
    position: relative;
    width: 100%;
    height: 180px; /* Fixed height for consistency */
    margin-bottom: 1rem;
    border-radius: 1rem;
    overflow: hidden;

    @media (min-width: 768px) {
      height: 180px; /* Keep consistent across breakpoints */
    }
  }

  .title {
    font-family: sans-serif;
    font-weight: bold;
    color: white;
    margin-bottom: 0.5rem;
    margin-top: 0.5rem;
    font-size: 1.125rem;

    @media (min-width: 768px) {
      font-size: 1.25rem;
    }
  }

  .description {
    font-family: sans-serif;
    font-weight: normal;
    color: var(--neutral-600);
    font-size: 0.75rem;
    display: -webkit-box;
    -webkit-line-clamp: 3; /* Limit to 3 lines */
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: 4.5rem; /* Approximately 3 lines of text */

    @media (min-width: 768px) {
      font-size: 0.875rem;
    }

    &.dark {
      color: var(--neutral-300);
    }
  }
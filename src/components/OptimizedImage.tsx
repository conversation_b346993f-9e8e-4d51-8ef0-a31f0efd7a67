import React from 'react';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  loading?: 'lazy' | 'eager';
}

/**
 * OptimizedImage component that automatically uses WebP versions if available
 * 
 * This component checks if a WebP version of the image exists at the same path
 * and uses it for browsers that support WebP format.
 * 
 * Usage:
 * import { OptimizedImage } from '@components/OptimizedImage';
 * import backgroundImage from '@assets/images/background.jpg';
 * 
 * <OptimizedImage 
 *   src={backgroundImage} 
 *   alt="Background" 
 *   width={1200} 
 *   height={800} 
 * />
 */
export const OptimizedImage: React.FC<OptimizedImageProps> = ({
  src, 
  alt, 
  width, 
  height, 
  className = '', 
  loading = 'lazy'
}) => {
  // Only try to use WebP for JPG, JPEG, and PNG images
  const isConvertibleToWebP = src.endsWith('.jpg') || src.endsWith('.jpeg') || src.endsWith('.png');
  
  // Create the WebP path by replacing the extension with .webp
  const webpSrc = isConvertibleToWebP 
    ? src.substring(0, src.lastIndexOf('.')) + '.webp' 
    : null;
  
  // If we have a potential WebP version, use <picture> element
  if (webpSrc) {
    return (
      <picture>
        <source srcSet={webpSrc} type="image/webp" />
        <img 
          src={src} 
          alt={alt} 
          width={width} 
          height={height} 
          loading={loading} 
          className={className}
        />
      </picture>
    );
  }
  
  // Otherwise just use a regular img tag
  return (
    <img 
      src={src} 
      alt={alt} 
      width={width} 
      height={height} 
      loading={loading} 
      className={className}
    />
  );
};

export default OptimizedImage;

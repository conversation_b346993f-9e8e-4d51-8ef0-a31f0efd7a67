
import { useState } from "react";
import PropTypes from "prop-types";
import { motion, AnimatePresence } from "framer-motion";
import { IconX } from "@tabler/icons-react";
import styles from "./AnimatedCardsMobile.module.scss";

// Import cards data from separate file
import { cards } from "./cards-data";

/* ------------------------- */
/* Mobile Card Component     */
/* ------------------------- */
const Card = ({ card, isSelected, onClick, onViewMore }) => (
  <motion.div
    className={`${styles.cardContainer} ${isSelected ? styles.selected : ""}`}
    onClick={onClick}
    whileHover={{ scale: 1.03 }}
    transition={{ type: "spring", stiffness: 300 }}
  >
    <div
      className={styles.cardBackground}
      style={{ backgroundImage: `url(${card.backgroundImage})` }}
    />
    <div className={styles.cardContent}>
      <h4 className={styles.cardTitle}>{card.title}</h4>
      <p className={styles.cardDescription}>{card.description}</p>
      <button
        onClick={(e) => {
          e.stopPropagation();
          onViewMore(card);
        }}
        className={styles.viewMoreButton}
        aria-label={`View more about ${card.title}`}
      >
        View More
      </button>
    </div>
  </motion.div>
);

// Add PropTypes validation for Card component
Card.propTypes = {
  card: PropTypes.shape({
    id: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    description: PropTypes.string.isRequired,
    backgroundImage: PropTypes.string.isRequired
  }).isRequired,
  isSelected: PropTypes.bool.isRequired,
  onClick: PropTypes.func.isRequired,
  onViewMore: PropTypes.func.isRequired
};

/* ------------------------- */
/* Mobile PopOutBox Component */
/* ------------------------- */
const PopOutBox = ({ activeCard, setActiveCard }): JSX.Element => {
  if (!activeCard) return <></>;
  return (
    <AnimatePresence>
      <motion.div
        className={styles.popOutBox}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        aria-modal="true"
        role="dialog"
        aria-label={`More details about ${activeCard.title}`}
      >
        <motion.div className={styles.popOutContent}>
          <button
            onClick={() => setActiveCard(null)}
            className={styles.closeButton}
            aria-label="Close details"
          >
            <IconX className={styles.iconX} />
          </button>
          <img
            src={activeCard.backgroundImage}
            alt={`${activeCard.title} background`}
            className={styles.popOutImage}
          />
          <h3 className={styles.popOutTitle}>{activeCard.title}</h3>
          <p className={styles.popOutDescription}>{activeCard.description}</p>
          <p className={styles.popOutExtra}>Additional details about {activeCard.title}...</p>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

// Add PropTypes validation for PopOutBox component
PopOutBox.propTypes = {
  activeCard: PropTypes.shape({
    title: PropTypes.string.isRequired,
    description: PropTypes.string.isRequired,
    backgroundImage: PropTypes.string.isRequired
  }),
  setActiveCard: PropTypes.func.isRequired
};

/* ------------------------- */
/* Main AnimatedCardsMobile  */
/* ------------------------- */
const AnimatedCardsMobile = () => {
  const [selectedCard, setSelectedCard] = useState(null);
  const [activeCard, setActiveCard] = useState(null);

  return (
    <div className={styles.animatedCardsContainer}>
      <h2 className={styles.title}>RECENT PROJECTS</h2>
      <div className={styles.cardContainerWrapper}>
        {cards.map((card) => (
          <Card
            key={card.id}
            card={card}
            isSelected={selectedCard === card.id}
            onClick={() => setSelectedCard(card.id)}
            onViewMore={(card) => setActiveCard(card)}
          />
        ))}
      </div>
      <PopOutBox activeCard={activeCard} setActiveCard={setActiveCard} />
    </div>
  );
};

export default AnimatedCardsMobile;

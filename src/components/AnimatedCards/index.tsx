
import { useState, useRef } from "react";
import PropTypes from "prop-types";
import { motion, useScroll, useTransform, AnimatePresence } from "framer-motion";
import { Link } from "react-router-dom";
import { IconX } from "@tabler/icons-react";
import styles from "./index.module.scss";

// Import cards data from separate file
import { cards } from "./cards-data";

// ------------------------------------
// Card Component
// ------------------------------------
const Card = ({ card, isSelected, onClick, onViewMore }) => (
  <motion.div
    className={`${styles.cardContainer} ${
      isSelected
        ? "w-[75vw] md:w-[1000px] h-[30vh] md:h-[65vh]"
        : "w-[40vw] md:w-[100px] h-[30vh] md:h-[65vh]"
    } relative cursor-pointer flex-shrink-0 rounded-3xl overflow-hidden shadow-lg`}
    onClick={onClick}
    whileHover={{ scale: 1.05 }}
    transition={{ type: "spring", stiffness: 300 }}
  >
    <div
      className="absolute inset-0 bg-cover bg-center backdrop-blur-sm"
      style={{ backgroundImage: `url(${card.backgroundImage})` }}
    ></div>
    <div className="absolute z-20 top-4 left-4 w-10 h-10 bg-gray-800 text-white rounded-full flex items-center justify-center">
      {card.id.charAt(1)}
    </div>
    <div
      className={`absolute bottom-0 z-10 w-full p-4 bg-black bg-opacity-70 backdrop-blur-md transition-opacity duration-300 ${
        isSelected ? "opacity-100" : "opacity-0"
      }`}
    >
      <div className="text-white flex justify-between items-center">
        <div>
          <h4 className="uppercase font-bold">{card.title}</h4>
          <p className="text-gray-300">{card.description}</p>
        </div>
        <button
          onClick={(e) => {
            e.stopPropagation();
            onViewMore(card);
          }}
          className="ml-4 px-4 py-2 bg-[#34be77] text-white rounded-lg"
          aria-label={`View more about ${card.title}`}
        >
          View More
        </button>
      </div>
    </div>
  </motion.div>
);

// Add PropTypes validation for Card component
Card.propTypes = {
  card: PropTypes.shape({
    id: PropTypes.string.isRequired,
    title: PropTypes.string.isRequired,
    description: PropTypes.string.isRequired,
    backgroundImage: PropTypes.string.isRequired,
    popoutText: PropTypes.string.isRequired,
    projectUrl: PropTypes.string.isRequired
  }).isRequired,
  isSelected: PropTypes.bool.isRequired,
  onClick: PropTypes.func.isRequired,
  onViewMore: PropTypes.func.isRequired
};

// ------------------------------------
// PopOutBox Component
// ------------------------------------
const PopOutBox = ({ activeCard, setActiveCard }): JSX.Element => {
  if (!activeCard) return <></>;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className={styles.popOutBox}
        aria-modal="true"
        role="dialog"
        aria-label={`More details about ${activeCard.title}`}
      >
        <motion.div className="bg-white p-6 rounded-2xl shadow-lg relative max-w-lg w-full">
          <button
            onClick={() => setActiveCard(null)}
            className="absolute top-4 right-4 p-2 rounded-full bg-gray-200"
            aria-label="Close details"
          >
            <IconX className="h-6 w-6 text-gray-600" />
          </button>
          <img
            src={activeCard.backgroundImage}
            alt={`${activeCard.title} background`}
            className="rounded-t-lg w-full"
          />
          <h3 className="text-xl font-bold mt-4">{activeCard.title}</h3>
          <p className="text-gray-700 mt-2">{activeCard.description}</p>
          <p className="text-gray-500 mt-4">{activeCard.popoutText}</p>
          <Link
            to={activeCard.projectUrl}
            className="mt-4 inline-block px-4 py-2 bg-blue-600 text-white rounded-lg"
          >
            View Project
          </Link>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
};

// Add PropTypes validation for PopOutBox component
PopOutBox.propTypes = {
  activeCard: PropTypes.shape({
    title: PropTypes.string.isRequired,
    description: PropTypes.string.isRequired,
    backgroundImage: PropTypes.string.isRequired,
    popoutText: PropTypes.string.isRequired,
    projectUrl: PropTypes.string.isRequired
  }),
  setActiveCard: PropTypes.func.isRequired
};

// ------------------------------------
// AnimatedCards Component
// ------------------------------------
const AnimatedCards = () => {
  const [selectedCard, setSelectedCard] = useState("c1");
  const [activeCard, setActiveCard] = useState(null);

  // Create a ref for the container to track its scroll progress.
  const containerRef = useRef(null);

  // Use scroll hooks to derive progress relative to the container.
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start center", "end start"],
  });

  // For the title:
  const titleX = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [-100, 0, 0, 100]);
  const titleOpacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0]);

  // For the cards area:
  const cardsX = useTransform(scrollYProgress, [0.2, 0.4, 0.4, 1], [100, 0, 0, -300]);
  const cardsOpacity = useTransform(scrollYProgress, [0.1, 0.4, 0.5, 1], [0, 1, 1, 0]);

  return (
    <div ref={containerRef} className={styles.animatedCardsContainer}>
      <motion.h2 className={styles.title} style={{ x: titleX, opacity: titleOpacity }}>
        RECENT PROJECTS
      </motion.h2>

      <motion.div className={styles.cardsWrapper} style={{ x: cardsX, opacity: cardsOpacity }}>
        <div className={styles.cardContainerWrapper}>
          {cards.map((card) => (
            <Card
              key={card.id}
              card={card}
              isSelected={selectedCard === card.id}
              onClick={() => setSelectedCard(card.id)}
              onViewMore={(card) => setActiveCard(card)}
            />
          ))}
        </div>
      </motion.div>

      <PopOutBox activeCard={activeCard} setActiveCard={setActiveCard} />

      {/* Explore More Portfolio Button */}
      <div className={styles.exploreMoreContainer}>
        <Link to="/portfolio/web-development" className={styles.exploreMoreButton}>
          Explore More Portfolio
        </Link>
      </div>
    </div>
  );
};

export default AnimatedCards;

/* Container for the overall animated cards section */
.animatedCardsContainer {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding-bottom: 200px;
}

/* Title styling */
.title {
  font-size: 4rem; /* text-6xl */
  font-weight: bold;
  text-align: center;
  color: #ffffff;
  margin-bottom: 120px;
}

/* Wrapper for the cards area */
.cardsWrapper {
  width: 100%;
  display: flex;
  justify-content: center;
  overflow-x: auto;
  padding-top: 100px;
  padding-bottom: 100px;
}

/* Container for the card items (inside the wrapper) */
.cardContainerWrapper {
  display: flex;
  gap: 2rem;
  padding: 1rem;
}

/* Card container base class.
   (Additional sizing is handled via Tailwind classes in the JSX markup) */
.cardContainer {
  position: relative;
  border: 0.5px solid #23452e;
}

/* Pop-out box overlay styling */
.popOutBox {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

/* Explore More Portfolio Button Container */
.exploreMoreContainer {
  margin-top: 2rem;
  display: flex;
  justify-content: center;
}

/* Explore More Portfolio Button */
.exploreMoreButton {
  padding: 1rem 2rem;
  background-color: #34be77;
  color: #ffffff;
  font-size: 1.25rem;
  font-weight: bold;
  border-radius: 0.5rem;
  text-decoration: none;
  transition: background-color 0.2s ease-in-out;
}

.exploreMoreButton:hover {
  background-color: #2ca46a;
}
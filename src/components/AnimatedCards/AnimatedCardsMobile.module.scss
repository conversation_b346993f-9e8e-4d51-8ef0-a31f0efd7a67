/* Container for the overall animated cards section (mobile version) */
.animatedCardsContainer {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  background-color: #121b1b; /* Adjust background as needed */
  margin-bottom: 200px;
}

/* Title styling */
.title {
  font-size: 2.5rem; /* Smaller title size for mobile */
  font-weight: bold;
  text-align: center;
  color: #ffffff;
  margin-bottom: 2rem;
}

/* Wrapper for the card items (vertical stacking) */
.cardContainerWrapper {
  display: flex;
  flex-direction: column;
  gap: 20px;
  width: 90%;
  padding: 0 1rem;
}

/* Card container base styling */
.cardContainer {
  position: relative;
  border: 0.5px solid #23452e;
  border-radius: 1rem;
  overflow: hidden;
  background: #000;
}

/* Optionally style a "selected" state if needed */
.selected {
  /* For example, you might add a border or shadow */
  box-shadow: 0 0 10px rgba(63, 169, 142, 0.7);
}

/* Card background image styling */
.cardBackground {
  width: 100%;
  height: 300px; /* Fixed height for mobile cards */
  background-size: cover;
  background-position: center;
  filter: brightness(0.7);
}

/* Card content overlay */
.cardContent {
  padding: 1rem;
  background: #121b1b;
  color: #ffffff;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

/* Card title and description */
.cardTitle {
  font-size: 1.5rem;
  margin: 0;
}

.cardDescription {
  font-size: 1rem;
  margin: 0;
}

/* "View More" button styling */
.viewMoreButton {
  margin-top: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #34be77;
  color: #fff;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.viewMoreButton:hover {
  background-color: #2aa765;
}

/* ------------------------- */
/* Pop-out Box Styling       */
/* ------------------------- */
.popOutBox {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.popOutContent {
  background: #fff;
  padding: 1rem;
  border-radius: 1rem;
  max-width: 90%;
  width: 300px;
  position: relative;
  text-align: center;
}

.closeButton {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: #eee;
  border: none;
  border-radius: 50%;
  padding: 0.5rem;
  cursor: pointer;
}

.iconX {
  width: 1.5rem;
  height: 1.5rem;
  color: #333;
}

.popOutImage {
  width: 100%;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.popOutTitle {
  font-size: 1.5rem;
  margin: 0.5rem 0;
}

.popOutDescription {
  font-size: 1rem;
  margin: 0.5rem 0;
}

.popOutExtra {
  font-size: 0.9rem;
  color: #666;
  margin-top: 1rem;
}
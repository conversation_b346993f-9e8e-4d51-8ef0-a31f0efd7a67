
import { motion } from "framer-motion";
import { <PERSON> } from "react-router-dom";
import { BentoGrid, BentoGridItem } from "../BentoGrid/index";
import styles from "./index.module.scss";
import { ReactNode } from "react";

// Define interface for blog items
interface BlogItem {
  title: string;
  description: string;
  header: ReactNode;
  icon?: ReactNode;
  className: string;
}

// Data for the grid items
const items: BlogItem[] = [
  {
    title: "Web Design Trends 2024",
    description: "Stay ahead of the curve with the latest web design trends.",
    header: (
      <img
        src="https://newworlddigitalmedia.co.uk/wp-content/uploads/shutterstock_1409688650.jpg"
        alt="Web Design Trends 2024"
        className={styles.image}
      />
    ),
    className: styles.spanTwo,
  },
  {
    title: "Boosting Your Online Presence",
    description: "Effective strategies for enhancing your brand's digital footprint.",
    header: (
      <img
        src="https://media.istockphoto.com/id/1352650187/photo/human-resource-manager-checks-the-cv-online-to-choose-the-perfect-employee-for-his-business.jpg?s=612x612&w=0&k=20&c=eyBny8f78fbh-3ihs4M-8sxCDUR8KKpBNDbnWUDkDE0="
        alt="Boosting Your Online Presence"
        className={styles.image}
      />
    ),
    className: styles.spanOne,
  },
  {
    title: "SEO Best Practices",
    description: "Master SEO techniques to improve your website's visibility.",
    header: (
      <img
        src="https://www.innermedia.co.uk/wp-content/uploads/2023/11/shutterstock_184302617-520x520.jpg"
        alt="SEO Best Practices"
        className={styles.image}
      />
    ),
    className: styles.spanOne,
  },
  {
    title: "Creating Engaging Content",
    description: "Tips for crafting content that captivates and converts.",
    header: (
      <img
        src="https://miro.medium.com/v2/resize:fit:612/1*Ylz1U0jJ2IIlWpcd3ECFQw.jpeg"
        alt="Creating Engaging Content"
        className={styles.image}
      />
    ),
    className: styles.spanTwo,
  },
  {
    title: "The Power of Branding",
    description: "How to create a brand that resonates with your audience.",
    header: (
      <img
        src="https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTnHqtSrlBGIeIzvERUzWzFOPS5Za0PD45Jmw&s"
        alt="The Power of Branding"
        className={styles.image}
      />
    ),
    className: styles.spanTwo,
  },
  {
    title: "Harnessing Social Media",
    description: "Strategies for leveraging social media for business growth.",
    header: (
      <img
        src="https://www.digibubble.co.uk/wp-content/uploads/2022/08/900x600-social-media-cubes.jpg"
        alt="Harnessing Social Media"
        className={styles.image}
      />
    ),
    className: styles.spanOne,
  },
];

// Animation Variants
const gridVariants = {
  hidden: { opacity: 0, y: 50, scale: 0.9 },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      type: "spring",
      bounce: 0.4,
      duration: 0.8,
      staggerChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 50, scale: 0.9 },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: { duration: 0.5 },
  },
};

const titleVariants = {
  hidden: { opacity: 0, y: 100 },
  visible: {
    opacity: 1,
    y: 0,
    transition: { duration: 0.8, ease: "easeOut" },
  },
};

export function HomeBlog() {
  return (
    <div className={styles.container}>
      <motion.div initial="hidden" whileInView="visible" viewport={{ once: false, amount: 0.4 }} variants={gridVariants}>
        {/* Blog Title */}
        <motion.h1 className={styles.title} variants={titleVariants}>
          BLOG
        </motion.h1>

        {/* Animated Bento Grid */}
        <BentoGrid className={styles.grid}>
          {items.map((item, i) => (
            <motion.div key={i} variants={itemVariants} className={item.className}>
              <BentoGridItem
                title={item.title}
                description={item.description}
                header={item.header}
                icon={item.icon}
              />
            </motion.div>
          ))}

          {/* View More Button at the End */}
          <motion.div className={styles.viewMoreContainer} variants={itemVariants}>
            <Link to="/blog" className={styles.viewMoreButton}>
              View More
            </Link>
          </motion.div>
        </BentoGrid>
      </motion.div>
    </div>
  );
}
/* src/components/BentoGridSecondDemo/index.module.scss */

.container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  /*
    If you want the entire content (title, grid, and button)
    to be vertically centered on the page, also add:
    align-items: center;
    justify-content: center;
  */
}

.title {
  font-size: 3rem;
  text-align: center;
  color: white;
  margin-bottom: 5rem;
}

.grid {
  width: 100%;
  max-width: 1250px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  grid-auto-rows: 22rem; /* Standardized height */

  @media (min-width: 768px) {
    grid-template-columns: repeat(3, 1fr);
    gap: 6px; /* Consistent gap with main BentoGrid */
  }
}

.image {
  width: 100%;
  height: 180px;
  object-fit: cover;
  border-radius: 10px;
}

.icon {
  width: 1rem;
  height: 1rem;
  color: var(--neutral-500);
}

.spanOne {
  grid-column: span 1;
}

.spanTwo {
  grid-column: span 2;
}

/* Center the button container */
.viewMoreContainer {
  /* Force this item to span all columns */
  grid-column: 1 / -1;
  /* Now center the button */
  display: flex;
  justify-content: center;
  align-items: center;
}

/* Button styling */
.viewMoreButton {
  display: inline-block;
  padding: 12px 24px;
  font-size: 1.2rem;
  font-weight: bold;
  text-transform: uppercase;
  text-decoration: none;
  color: white;
  background: linear-gradient(90deg, #06beb6 0%, #48b1bf 100%);
  border-radius: 8px;
  transition: all 0.3s ease-in-out;
  text-align: center;

  &:hover {
    transform: scale(1.05);
    background: linear-gradient(90deg, #48b1bf 0%, #06beb6 100%);
  }
}
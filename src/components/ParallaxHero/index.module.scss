.parallaxHero {
  position: relative;
  height: 100vh;
  overflow: hidden;
}

.background,
.foreground {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  background-position: center;
  background-size: cover;
  transition: transform 0.1s ease-out;
}

.background {
  z-index: 1;
}

.foreground {
  z-index: 2;
}

.heroContent {
  position: absolute;
  inset: 0;
  z-index: 20;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-align: left;
  padding: 0 2.5rem; /* px-10 */
  transition: transform 0.1s ease-out;
}

.textContainer {
  display: flex;
  flex: 1;
  justify-content: space-between;
  align-items: center;
  max-width: 80rem; /* max-w-7xl */
  width: 100%;
}

.title {
  font-size: 9rem; /* text-9xl */
  font-weight: bold;
  color: white !important;
  text-shadow: 0 20px 30px rgba(0, 0, 0, 0.1),
    0 4px 6px rgba(0, 0, 0, 0.1);
  word-spacing: -0.3rem; /* Adjust for closer words */
  letter-spacing: -0.05em; /* Tighten letters */
  line-height: 8rem; /* Reduce vertical space */
}

.button {
  margin-top: 2rem;
  padding: 1rem 2.5rem;
  background-color: #3fa98e;
  border-radius: 9999px; /* rounded-full */
  font-size: 1.125rem; /* text-lg */
  font-weight: 500;
  box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1),
    0 4px 6px rgba(0, 0, 0, 0.1);
  transition: background-color 0.2s ease-in-out;
  color: white !important;

  &:hover {
    background-color: #34a084;
  }
}

/* Mobile Adjustments */
@media (max-width: 768px) {
  .heroContent {
    padding: 0 1rem;
  }

  .textContainer {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .title {
    font-size: 4rem; /* Smaller title for mobile */
    line-height: 1.2; /* More appropriate line height */
    word-spacing: normal;
    letter-spacing: normal;
  }

  .button {
    margin-top: 1.5rem;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }
}
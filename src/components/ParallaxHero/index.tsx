
import { useEffect, useState } from "react";
import { LinearGradient } from "react-text-gradients";
import { HashLink } from 'react-router-hash-link';
import background from "@assets/Background.png"; // Import background image
import foreground from "@assets/Foreground.png"; // Import foreground image
import styles from "./index.module.scss";

const ParallaxHero = () => {
  const [scrollY, setScrollY] = useState(0);

  useEffect(() => {
    const handleScroll = () => setScrollY(window.scrollY);
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const backgroundZoomFactor = Math.max(1.5 - scrollY / 700, 1);
  const foregroundZoomFactor = Math.max(1.2 - scrollY / 1000, 1);

  return (
    <div className={styles.parallaxHero}>
      {/* Background Image */}
      <div
        className={styles.background}
        style={{
          backgroundImage: `url(${background})`,
          transform: `scale(${backgroundZoomFactor})`,
        }}
      />

      {/* Foreground Image */}
      <div
        className={styles.foreground}
        style={{
          backgroundImage: `url(${foreground})`,
          transform: `scale(${foregroundZoomFactor})`,
        }}
      />

      {/* Hero Content */}
      <div
        className={styles.heroContent}
        style={{ transform: `translateY(-${scrollY / 5}px)` }}
      >
        <div className={styles.textContainer}>
          {/* Main Title */}
          <div>
            <h1 className={styles.title}>
              DESIGN <br /> INNOVATE <br />
              <LinearGradient gradient={["to right", "#3fa98e, #D7FDF3"]}>
                ELEVATE
              </LinearGradient>
            </h1>

            {/* Call to Action Button */}


            <HashLink smooth to="/#quote" className={styles.link}>
              <button className={styles.button}>GET A QUOTE</button>
            </HashLink>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ParallaxHero;
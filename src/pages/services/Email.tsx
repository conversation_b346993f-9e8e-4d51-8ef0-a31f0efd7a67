import React, { useRef, useState } from "react";
import { motion, useScroll } from "framer-motion";
import styles from "./AppDevelopment.module.scss";

const Email = () => {
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const containerRef = useRef<HTMLDivElement | null>(null);

  useScroll({
    target: containerRef,
    offset: ["start start", "end end"]
  });

  const handleScroll = () => {
    if (!containerRef.current) return;

    const cards = containerRef.current.querySelectorAll(`.${styles.contentBlock}`);
    const containerTop = containerRef.current.offsetTop;
    const scrollPosition = window.scrollY + window.innerHeight / 2;

    cards.forEach((card, index) => {
      // Type assertion to tell TypeScript this is an HTMLElement
      const cardElement = card as HTMLElement;
      const cardTop = cardElement.offsetTop + containerTop;
      const cardBottom = cardTop + cardElement.offsetHeight;

      if (scrollPosition >= cardTop && scrollPosition <= cardBottom) {
        setActiveIndex(index);
      }
    });
  };

  React.useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const heroImage = "https://images.unsplash.com/photo-1633265486064-086b219458ec?q=80&w=2070&auto=format&fit=crop";

  return (
    <div className={styles.webDevelopment}>
      <motion.header
        className={styles.heroSection}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        <div className={styles.heroInner}>
          <motion.div
            className={styles.heroText}
            initial={{ opacity: 0, x: -80 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <h1>EMAIL MARKETING SERVICES</h1>
            <p>
              Helping businesses connect with their audience and drive conversions through personalized, data-driven email campaigns.
            </p>
          </motion.div>

          <motion.div
            className={styles.heroImageWrapper}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <img
              src={heroImage}
              alt="Email Marketing Hero"
              className={styles.heroImage}
            />
          </motion.div>
          <motion.div
            className={styles.heroNav}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <nav className={styles.navMenu}>
              <ul>
                <li><a href="/services/web-development">Web Development</a></li>
                <li><a href="/services/app-development">App Development</a></li>
                <li><a href="/services/graphic-design">Graphic Design</a></li>
                <li><a href="/services/automation">Automation</a></li>
                <li><a href="/services/marketing">Marketing</a></li>
                <li><a href="/services/email" className={styles.active}>Email</a></li>
              </ul>
            </nav>
          </motion.div>
        </div>
      </motion.header>

      <div className={styles.contentSection} ref={containerRef}>
        {[
          {
            title: "Email Marketing that Connects and Converts",
            description: "At Veltrix, we design and deliver email marketing campaigns that help you build meaningful relationships with your customers. Our personalized and data-driven approach ensures that every email resonates with your audience, drives engagement, and boosts conversions.",
            imageUrl: "https://images.unsplash.com/photo-1526628953301-3e589a6a8b74?q=80&w=2006&auto=format&fit=crop"
          },
          {
            title: "Personalized Email Campaigns",
            description: "We create tailored email campaigns that speak directly to your customers' needs and interests. From welcome emails and promotional offers to newsletters and product announcements, we ensure your messages are relevant, engaging, and effective.",
            imageUrl: "https://images.unsplash.com/photo-1517697471339-4aa32003c11a?q=80&w=2076&auto=format&fit=crop"
          },
          {
            title: "Email Automation for Seamless Communication",
            description: "Save time and increase efficiency with automated email sequences that nurture leads and keep customers engaged. Our email automation services include onboarding sequences, follow-ups, abandoned cart reminders, and more.",
            imageUrl: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?q=80&w=2070&auto=format&fit=crop"
          },
          {
            title: "Lead Nurturing and Customer Retention",
            description: "Build long-lasting relationships with your audience through strategic lead nurturing. We design email sequences that guide prospects through the buyer journey, from awareness to purchase, while keeping existing customers engaged and loyal.",
            imageUrl: "https://images.unsplash.com/photo-1512428559087-560fa5ceab42?q=80&w=2070&auto=format&fit=crop"
          },
          {
            title: "Responsive Email Design for Every Device",
            description: "Our email templates are designed to look great and function flawlessly on any device, whether it's a desktop, tablet, or smartphone. We ensure your emails are visually appealing and easy to read, no matter where they're opened.",
            imageUrl: "https://images.unsplash.com/photo-1553484771-371a605b060b?q=80&w=2070&auto=format&fit=crop"
          },
          {
            title: "Email List Management and Segmentation",
            description: "Maximize the impact of your campaigns by sending the right messages to the right people. We help you segment your email list based on customer behavior, demographics, and preferences, ensuring targeted communication that drives results.",
            imageUrl: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?q=80&w=2070&auto=format&fit=crop"
          },
          {
            title: "A/B Testing and Performance Optimization",
            description: "Continuously improve your email campaigns with A/B testing and performance analysis. We test different subject lines, content, and layouts to identify what resonates best with your audience and optimize for higher open and click-through rates.",
            imageUrl: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=2070&auto=format&fit=crop"
          },
          {
            title: "Email Deliverability and Compliance",
            description: "Ensure your emails reach your subscribers' inboxes with our email deliverability services. We follow best practices to maintain high deliverability rates and ensure compliance with email regulations like GDPR and CAN-SPAM.",
            imageUrl: "https://images.unsplash.com/photo-1504868584819-f8e8b4b6d7e3?q=80&w=2070&auto=format&fit=crop"
          },
          {
            title: "Performance Analytics and Reporting",
            description: "Track the success of your email campaigns with detailed analytics and performance reports. We provide insights into key metrics like open rates, click-through rates, conversions, and ROI, helping you measure results and optimize future campaigns.",
            imageUrl: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=2070&auto=format&fit=crop"
          }
        ].map((content, index) => (
          <motion.div
            key={index}
            className={styles.contentBlock}
            initial={{ opacity: 0.2 }}
            animate={{
              opacity: activeIndex === index ? 1 : 0.1,
              scale: activeIndex === index ? 1 : 0.98
            }}
            transition={{ duration: 0.5 }}
          >
            <div className={styles.textContent}>
              <h2>{content.title}</h2>
              <p>{content.description}</p>
            </div>
            <div className={styles.imageContent}>
              <img src={content.imageUrl} alt={content.title} />
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default Email;

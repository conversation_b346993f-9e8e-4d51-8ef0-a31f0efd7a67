import React, { useRef, useState } from "react";
import { motion, useScroll } from "framer-motion";
import styles from "./AppDevelopment.module.scss";

const Marketing = () => {
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const containerRef = useRef<HTMLDivElement | null>(null);

  useScroll({
    target: containerRef,
    offset: ["start start", "end end"]
  });

  const handleScroll = () => {
    if (!containerRef.current) return;

    const cards = containerRef.current.querySelectorAll(`.${styles.contentBlock}`);
    const containerTop = containerRef.current.offsetTop;
    const scrollPosition = window.scrollY + window.innerHeight / 2;

    cards.forEach((card, index) => {
      // Type assertion to tell TypeScript this is an HTMLElement
      const cardElement = card as HTMLElement;
      const cardTop = cardElement.offsetTop + containerTop;
      const cardBottom = cardTop + cardElement.offsetHeight;

      if (scrollPosition >= cardTop && scrollPosition <= cardBottom) {
        setActiveIndex(index);
      }
    });
  };

  React.useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const heroImage = "https://images.unsplash.com/photo-1557838923-2985c318be48?q=80&w=2069&auto=format&fit=crop";

  return (
    <div className={styles.webDevelopment}>
      <motion.header
        className={styles.heroSection}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        <div className={styles.heroInner}>
          <motion.div
            className={styles.heroText}
            initial={{ opacity: 0, x: -80 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <h1>MARKETING SERVICES</h1>
            <p>
              Helping businesses grow through data-driven marketing strategies that deliver measurable results.
            </p>
          </motion.div>

          <motion.div
            className={styles.heroImageWrapper}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <img
              src={heroImage}
              alt="Marketing Hero"
              className={styles.heroImage}
            />
          </motion.div>
          <motion.div
            className={styles.heroNav}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <nav className={styles.navMenu}>
              <ul>
                <li><a href="/services/web-development">Web Development</a></li>
                <li><a href="/services/app-development">App Development</a></li>
                <li><a href="/services/graphic-design">Graphic Design</a></li>
                <li><a href="/services/automation">Automation</a></li>
                <li><a href="/services/marketing" className={styles.active}>Marketing</a></li>
                <li><a href="/services/email">Email</a></li>
              </ul>
            </nav>
          </motion.div>
        </div>
      </motion.header>

      <div className={styles.contentSection} ref={containerRef}>
        {[
          {
            title: "Strategic Marketing to Accelerate Your Business Growth",
            description: "At Veltrix, we create data-driven marketing strategies that elevate your brand and drive measurable results. From digital advertising and content marketing to SEO and social media campaigns, our solutions are designed to boost visibility, generate leads, and increase conversions. Let us help you turn clicks into customers and build lasting relationships with your audience.",
            imageUrl: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?q=80&w=2070&auto=format&fit=crop"
          },
          {
            title: "Digital Advertising Campaigns",
            description: "Reach your target audience with highly targeted digital ads. We design and manage PPC, display, and social media campaigns that deliver maximum ROI, helping you increase brand awareness and drive sales.",
            imageUrl: "https://images.unsplash.com/photo-1432888498266-38ffec3eaf0a?q=80&w=2074&auto=format&fit=crop"
          },
          {
            title: "Content Marketing & SEO",
            description: "Engage your audience with high-quality content that informs, inspires, and converts. Our SEO-optimized blog posts, articles, and videos help you rank higher on search engines, driving organic traffic and increasing brand authority.",
            imageUrl: "https://images.unsplash.com/photo-1542393545-10f5cde2c810?q=80&w=2066&auto=format&fit=crop"
          },
          {
            title: "Social Media Marketing",
            description: "Build a loyal community and boost engagement with our social media marketing services. We create compelling content and manage your social media presence across platforms like Facebook, Instagram, LinkedIn, and Twitter.",
            imageUrl: "https://images.unsplash.com/photo-1562577309-4932fdd64cd1?q=80&w=2074&auto=format&fit=crop"
          },
          {
            title: "Email Marketing Campaigns",
            description: "Stay connected with your customers and nurture leads with personalized email campaigns. We design engaging email sequences that drive engagement, promote offers, and boost customer retention.",
            imageUrl: "https://images.unsplash.com/photo-1633265486064-086b219458ec?q=80&w=2070&auto=format&fit=crop"
          },
          {
            title: "SEO & Link-Building",
            description: "Improve your search engine rankings and attract more organic traffic with our comprehensive SEO services. We optimize your website, build high-quality backlinks, and ensure your content meets the latest SEO best practices.",
            imageUrl: "https://images.unsplash.com/photo-1571786256017-aee7a0c009b6?q=80&w=1780&auto=format&fit=crop"
          },
          {
            title: "Conversion Rate Optimization (CRO)",
            description: "Turn website visitors into paying customers with our conversion rate optimization services. We analyze user behavior, test different elements, and optimize your website to increase leads and sales.",
            imageUrl: "https://images.unsplash.com/photo-1533750349088-cd871a92f312?q=80&w=2070&auto=format&fit=crop"
          },
          {
            title: "Brand Development & Positioning",
            description: "Define your brand identity and stand out from the competition with our branding services. We help you create a compelling brand story, design eye-catching visuals, and craft messaging that resonates with your audience.",
            imageUrl: "https://images.unsplash.com/photo-1434626881859-194d67b2b86f?q=80&w=2074&auto=format&fit=crop"
          },
          {
            title: "Performance Analytics & Reporting",
            description: "Track the success of your marketing campaigns with our detailed analytics and reporting services. We provide actionable insights that help you measure performance, optimize campaigns, and maximize ROI.",
            imageUrl: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=2070&auto=format&fit=crop"
          }
        ].map((content, index) => (
          <motion.div
            key={index}
            className={styles.contentBlock}
            initial={{ opacity: 0.2 }}
            animate={{
              opacity: activeIndex === index ? 1 : 0.1,
              scale: activeIndex === index ? 1 : 0.98
            }}
            transition={{ duration: 0.5 }}
          >
            <div className={styles.textContent}>
              <h2>{content.title}</h2>
              <p>{content.description}</p>
            </div>
            <div className={styles.imageContent}>
              <img src={content.imageUrl} alt={content.title} />
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default Marketing;

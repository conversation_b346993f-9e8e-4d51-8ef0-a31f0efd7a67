
import { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { BentoGrid, BentoGridItem } from "../../components/BentoGrid/index";
import styles from "./Services.module.scss";

// Define interfaces for the service data structure
interface ServiceContent {
  children?: Array<{
    text?: string;
  }>;
}

interface ServiceImage {
  url?: string;
}

interface Service {
  id: string;
  Title?: string;
  Slug?: string;
  Content?: string | ServiceContent[];
  CoverImage?: ServiceImage;
}

export default function Services() {
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  useEffect(() => {
    const fetchServices = async () => {
      try {
        const response = await fetch("http://localhost:1337/api/services?populate=*");
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        console.log("Fetched services:", data); // Debugging API response

        if (data && Array.isArray(data.data)) {
          setServices(data.data);
        } else {
          setServices([]);
        }
      } catch (err) {
        console.error("Error fetching services:", err);
        setError("Failed to load services. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, []);

  if (loading) return <p className={styles.loading}>Loading services...</p>;
  if (error) return <p className={styles.error}>{error}</p>;
  if (services.length === 0) return <p className={styles.noServices}>No services available.</p>;

  return (
    <div className={styles.container}>
      <h1 className={styles.title}>Our Services</h1>
      <BentoGrid className={styles.grid}>
        {services.map((service, index) => {
          const { id, Title, Slug, Content, CoverImage } = service;

          console.log("Service Data:", service); // Debugging Content

          // Extract first sentence and limit length
          let previewContent = "No description available.";
          if (typeof Content === "string") {
            previewContent = Content.split(". ").slice(0, 1).join(". ") + ".";
          } else if (Array.isArray(Content) && Content.length > 0) {
            const contentItem = Content[0] as ServiceContent;
            previewContent = contentItem?.children?.[0]?.text || "No description available.";
          }
          if (previewContent.length > 80) {
            previewContent = previewContent.substring(0, 80) + "...";
          }

          // Get cover image URL safely
          const imageUrl = CoverImage?.url
            ? `http://localhost:1337${CoverImage.url}`
            : null;

          // Determine span class (example: every third item spans two columns)å
          const spanClass = index % 3 === 0 ? styles.spanTwo : styles.spanOne;

          return (
            <div key={id} className={spanClass}>
              <Link to={`/services/${Slug}`} className={styles.link}>
                <BentoGridItem
                  className={styles.gridItem}
                  title={Title || "Untitled Service"}
                  description={previewContent}
                  header={
                    imageUrl ? (
                      <img src={imageUrl} alt={Title || "Service"} className={styles.image} />
                    ) : (
                      <div className={styles.placeholder}>No Image Available</div>
                    )
                  }
                />
              </Link>
            </div>
          );
        })}
      </BentoGrid>
    </div>
  );
}
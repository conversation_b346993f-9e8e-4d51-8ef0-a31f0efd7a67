import React, { useRef, useState, useEffect } from "react";
import { motion, useScroll } from "framer-motion";
import { Link } from "react-router-dom";
import styles from "./AppDevelopment.module.scss";
import img4 from "../../assets/Automation.jpg";

const WebDevelopment = () => {
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const containerRef = useRef<HTMLDivElement | null>(null);

  // Force scroll to top immediately when component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      window.scrollTo({
        top: 0,
        left: 0,
        behavior: 'auto'
      });
    }, 0);
    
    return () => clearTimeout(timer);
  }, []);

  // Track scroll progress to determine which card is active
  useScroll({
      target: containerRef,
      offset: ["start start", "end end"]
    });

  // Update active card based on scroll position
  const handleScroll = () => {
    if (!containerRef.current) return;

    const cards = containerRef.current.querySelectorAll(`.${styles.contentBlock}`);
    const containerTop = containerRef.current.offsetTop;
    const scrollPosition = window.scrollY + window.innerHeight / 2;

    cards.forEach((card, index) => {
      // Type assertion to tell TypeScript this is an HTMLElement
      const cardElement = card as HTMLElement;
      const cardTop = cardElement.offsetTop + containerTop;
      const cardBottom = cardTop + cardElement.offsetHeight;

      if (scrollPosition >= cardTop && scrollPosition <= cardBottom) {
        setActiveIndex(index);
      }
    });
  };

  React.useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const contentData = [
    {
      title: "Web Development agency to help your business grow",
      description: "At Veltrix, we specialize in creating custom websites that elevate your business and drive growth. Our web development services are designed to deliver fast, secure, and user-friendly websites that leave a lasting impression. Whether you need a sleek portfolio, a dynamic e-commerce platform, or a fully customized web solution, our team combines creativity with cutting-edge technology to bring your vision to life. We focus on responsive design, seamless functionality, and optimized performance to ensure your website not only looks great but also delivers results. Partner with Veltrixand watch your online presence thrive.",
      imageUrl: "https://images.unsplash.com/photo-1733503711060-1df31554390f?q=80&w=1170&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D"
    },
    {
      title: "Concepts and Wireframes",
      description: "We use wireframes as the blueprint of your website, helping us map out the structure, layout, and functionality. With strategic design and compelling copy, we ensure that your website captures the attention of visitors and drives conversions.",
      imageUrl: "https://images.unsplash.com/photo-1581291518633-83b4ebd1d83e?q=80&w=1170&auto=format&fit=crop"
    },
    {
      title: "Video and Animation",
      description: "Our team creates engaging video content and animations that bring your website to life, making your brand stand out and effectively communicate your message.",
      imageUrl: "https://images.unsplash.com/photo-1535016120720-40c646be5580?q=80&w=1170&auto=format&fit=crop"
    },
    {
      title: "UX/UI",
      description: "We prioritize user experience and interface design to create websites that are both visually appealing and easy to navigate, ensuring a seamless experience for your customers.",
      imageUrl: "https://images.unsplash.com/photo-1586717791821-3f44a563fa4c?q=80&w=1170&auto=format&fit=crop"
    },
    {
      title: "Responsive Design",
      description: "Our websites are built to be fully responsive, ensuring they look and function perfectly on all devices, from desktops to smartphones. We implement mobile-first design principles that prioritize the user experience across all screen sizes. Our responsive approach includes flexible grid layouts, adaptable images, and optimized navigation systems that transform seamlessly between devices. We conduct thorough testing on multiple devices and browsers to guarantee consistent performance and appearance. This approach not only improves user satisfaction but also boosts your SEO rankings, as search engines prioritize mobile-friendly websites. With our responsive design expertise, your website will deliver an exceptional experience to every visitor, regardless of how they access it.",
      imageUrl: "https://images.unsplash.com/photo-1558655146-9f40138edfeb?q=80&w=1170&auto=format&fit=crop"
    },
    {
      title: "Hosting",
      description: "We provide reliable and secure hosting solutions, ensuring your website is always online and performs at its best. Our hosting services include high-performance cloud infrastructure with 99.9% uptime guarantees and essential security measures like SSL certificates and automated backups. We optimize server configurations for maximum speed using content delivery networks (CDNs), and our solutions are scalable to accommodate traffic spikes and business growth. Whether you need shared hosting for a small business or dedicated servers for high-traffic applications, we tailor our hosting solutions to match your specific requirements.",
      imageUrl: "https://images.unsplash.com/photo-1639322537228-f710d846310a?q=80&w=1170&auto=format&fit=crop"
    },
    {
      title: "Website Maintenance",
      description: "Our ongoing maintenance services keep your website up to date, secure, and running smoothly, so you can focus on your business. We provide regular updates to your CMS and plugins, security monitoring, performance optimization, and content updates. Our maintenance plans include regular backups, uptime monitoring, and technical support to quickly resolve any issues that arise. We also conduct periodic site audits to identify and fix potential problems before they impact your visitors.",
      imageUrl: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?q=80&w=1169&auto=format&fit=crop"
    },
    {
      title: "eCommerce Websites",
      description: "We design and build powerful eCommerce websites that make it easy to sell your products and grow your online business. Our solutions include secure payment gateways, inventory management systems, and user-friendly product catalogs. We optimize the checkout process to reduce cart abandonment and implement mobile-friendly designs for shoppers on any device. Our eCommerce sites include analytics tools to track sales performance and customer behavior, helping you make data-driven decisions to increase conversions.",
      imageUrl: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?q=80&w=1170&auto=format&fit=crop"
    },
    {
      title: "CMS Web Design",
      description: "Our CMS solutions give you full control over your website content, making it easy to update and manage without technical expertise. We build custom websites on popular platforms like WordPress, Shopify, and Webflow, tailored to your specific needs. Our CMS implementations feature intuitive dashboards, custom content types, and role-based permissions to streamline your workflow. We provide training and documentation to ensure your team can confidently manage your site, while also offering ongoing support when needed.",
      imageUrl: "https://images.unsplash.com/photo-1499951360447-b19be8fe80f5?q=80&w=1170&auto=format&fit=crop"
    },
    {
      title: "Off-Page SEO and Link-Building",
      description: "We optimize your website for search engines and build high-quality backlinks to improve your rankings and increase organic traffic. Our comprehensive SEO strategy includes competitor analysis, keyword research, and content optimization to target your ideal audience. We develop authoritative backlink profiles through guest posting, industry partnerships, and content marketing. Our approach focuses on sustainable, white-hat techniques that build your site's authority naturally over time, avoiding risky tactics that could lead to penalties. We provide regular reporting on your SEO performance with actionable insights.",
      imageUrl: "https://images.unsplash.com/photo-1562577309-4932fdd64cd1?q=80&w=1170&auto=format&fit=crop"
    },
    {
      title: "User Interface (UI) Design",
      description: "Our UI design services focus on creating visually appealing and intuitive interfaces that enhance user engagement and satisfaction. We craft clean, modern designs that reflect your brand identity while prioritizing usability and accessibility. Our designers work with the latest design tools and follow current trends while ensuring timeless appeal. We create consistent visual elements across your website, from typography and color schemes to buttons and navigation menus. Each interface element is thoughtfully designed to guide users toward their goals and create memorable experiences that keep them coming back.",
      imageUrl: "https://images.unsplash.com/photo-1545235617-7a424c1a60cc?q=80&w=1170&auto=format&fit=crop"
    },
    {
      title: "Client Testimonial",
      description: "It's been a pleasure working with Influx on our new website for our business, Vantage Spaces. From start to finish, they worked hard to match our original brief, and were incredibly responsive, proactive, and professional throughout the project. We could not have asked for a better team to build our website and the final result is awesome, despite the tight timescales! We won't hesitate to recommend. Thank you Influx!",
      imageUrl: "https://images.unsplash.com/photo-1559136555-9303baea8ebd?q=80&w=1170&auto=format&fit=crop"
    }
  ];

  return (
    <div className={styles.webDevelopment}>
      {/* HERO SECTION */}
      <motion.header
        className={styles.heroSection}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        <div className={styles.heroInner}>
          <motion.div
            className={styles.heroText}
            initial={{ opacity: 0, x: -80 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <h1>WEB DEVELOPMENT SERVICES</h1>
            <p>
              Crafting fast, secure, and visually stunning websites that drive
              business growth.
            </p>
          </motion.div>

          <motion.div
            className={styles.heroImageWrapper}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <img
              src={img4}
              alt="Small Hero"
              className={styles.heroImage}
            />
          </motion.div>
          <motion.div
            className={styles.heroNav}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <nav className={styles.navMenu}>
              <ul>
                <li><Link to="/services/web-development" className={styles.active}>Web Development</Link></li>
                <li><Link to="/services/app-development">App Development</Link></li>
                <li><Link to="/services/graphic-design">Graphic Design</Link></li>
                <li><Link to="/services/automation">Automation</Link></li>
                <li><Link to="/services/marketing">Marketing</Link></li>
                <li><Link to="/services/email">Email</Link></li>
              </ul>
            </nav>
          </motion.div>
        </div>
      </motion.header>

      {/* Content Section */}
      <div className={styles.contentSection} ref={containerRef}>
        {contentData.map((content, index) => (
          <motion.div
            key={index}
            className={styles.contentBlock}
            initial={{ opacity: 0.2 }}
            animate={{
              opacity: activeIndex === index ? 1 : 0.1,
              scale: activeIndex === index ? 1 : 0.98
            }}
            transition={{ duration: 0.5 }}
          >
            <div className={styles.textContent}>
              <h2>{content.title}</h2>
              <p>{content.description}</p>
            </div>
            <div className={styles.imageContent}>
              <img src={content.imageUrl} alt={content.title} />
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default WebDevelopment;

import React, { useRef, useState } from "react";
import { motion, useScroll } from "framer-motion";
import styles from "./AppDevelopment.module.scss";

const Automation = () => {
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const containerRef = useRef<HTMLDivElement | null>(null);

  useScroll({
    target: containerRef,
    offset: ["start start", "end end"]
  });

  const handleScroll = () => {
    if (!containerRef.current) return;

    const cards = containerRef.current.querySelectorAll(`.${styles.contentBlock}`);
    const containerTop = containerRef.current.offsetTop;
    const scrollPosition = window.scrollY + window.innerHeight / 2;

    cards.forEach((card, index) => {
      // Type assertion to tell TypeScript this is an HTMLElement
      const cardElement = card as HTMLElement;
      const cardTop = cardElement.offsetTop + containerTop;
      const cardBottom = cardTop + cardElement.offsetHeight;

      if (scrollPosition >= cardTop && scrollPosition <= cardBottom) {
        setActiveIndex(index);
      }
    });
  };

  React.useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const heroImage = "https://images.unsplash.com/photo-1635070041078-e363dbe005cb?q=80&w=2070&auto=format&fit=crop";

  return (
    <div className={styles.webDevelopment}>
      <motion.header
        className={styles.heroSection}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        <div className={styles.heroInner}>
          <motion.div
            className={styles.heroText}
            initial={{ opacity: 0, x: -80 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <h1>AUTOMATION SERVICES</h1>
            <p>
              Streamline your operations and boost efficiency with intelligent automation solutions that transform your business processes.
            </p>
          </motion.div>

          <motion.div
            className={styles.heroImageWrapper}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <img
              src={heroImage}
              alt="Automation Hero"
              className={styles.heroImage}
            />
          </motion.div>
          <motion.div
            className={styles.heroNav}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <nav className={styles.navMenu}>
              <ul>
                <li><a href="/services/web-development">Web Development</a></li>
                <li><a href="/services/app-development">App Development</a></li>
                <li><a href="/services/graphic-design">Graphic Design</a></li>
                <li><a href="/services/automation" className={styles.active}>Automation</a></li>
                <li><a href="/services/marketing">Marketing</a></li>
                <li><a href="/services/email">Email</a></li>
              </ul>
            </nav>
          </motion.div>
        </div>
      </motion.header>

      <div className={styles.contentSection} ref={containerRef}>
        {[
          {
            title: "Business Process Automation Solutions",
            description: "At Veltrix, we specialize in transforming manual, time-consuming processes into streamlined automated workflows. Our automation services help businesses reduce operational costs, minimize errors, and increase productivity. Whether you need to automate data entry, document processing, or complex business workflows, our team delivers customized solutions that drive efficiency and growth.",
            imageUrl: "https://images.unsplash.com/photo-1518432031352-d6fc5c10da5a?q=80&w=2070&auto=format&fit=crop"
          },
          {
            title: "Workflow Automation",
            description: "Streamline your business processes with intelligent workflow automation. We design and implement automated workflows that eliminate manual tasks, ensure consistency, and improve operational efficiency across your organization.",
            imageUrl: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=2070&auto=format&fit=crop"
          },
          {
            title: "RPA (Robotic Process Automation)",
            description: "Our RPA solutions automate repetitive tasks and processes using software robots. These digital workers can handle data entry, form filling, report generation, and other routine tasks with precision and speed.",
            imageUrl: "https://images.unsplash.com/photo-1485827404703-89b55fcc595e?q=80&w=2070&auto=format&fit=crop"
          },
          {
            title: "API Integration & Development",
            description: "Connect your systems and applications seamlessly with our API integration services. We create custom APIs and integrate existing ones to ensure smooth data flow and process automation across your technology stack.",
            imageUrl: "https://images.unsplash.com/photo-1451187580459-43490279c0fa?q=80&w=2070&auto=format&fit=crop"
          },
          {
            title: "Data Processing Automation",
            description: "Transform your data management with automated processing solutions. From data extraction and validation to transformation and reporting, we help you handle data more efficiently and accurately.",
            imageUrl: "https://images.unsplash.com/photo-1504868584819-f8e8b4b6d7e3?q=80&w=2070&auto=format&fit=crop"
          },
          {
            title: "Cloud Automation",
            description: "Leverage the power of cloud computing with our cloud automation services. We help you automate cloud resource management, scaling, and deployment processes for optimal performance and cost efficiency.",
            imageUrl: "https://images.unsplash.com/photo-1451187580459-43490279c0fa?q=80&w=2070&auto=format&fit=crop"
          },
          {
            title: "Test Automation",
            description: "Ensure quality and reliability with automated testing solutions. Our test automation frameworks help you catch issues early, reduce manual testing effort, and accelerate your development cycle.",
            imageUrl: "https://images.unsplash.com/photo-1461749280684-dccba630e2f6?q=80&w=2070&auto=format&fit=crop"
          },
          {
            title: "Marketing Automation",
            description: "Enhance your marketing efforts with automated campaigns, lead nurturing, and customer engagement processes. Our solutions help you deliver personalized experiences while saving time and resources.",
            imageUrl: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?q=80&w=2070&auto=format&fit=crop"
          },
          {
            title: "Custom Automation Solutions",
            description: "Every business is unique, and so are their automation needs. We develop tailored automation solutions that address your specific challenges and help you achieve your business objectives.",
            imageUrl: "https://images.unsplash.com/photo-1563986768609-322da13575f3?q=80&w=2070&auto=format&fit=crop"
          }
        ].map((content, index) => (
          <motion.div
            key={index}
            className={styles.contentBlock}
            initial={{ opacity: 0.2 }}
            animate={{
              opacity: activeIndex === index ? 1 : 0.1,
              scale: activeIndex === index ? 1 : 0.98
            }}
            transition={{ duration: 0.5 }}
          >
            <div className={styles.textContent}>
              <h2>{content.title}</h2>
              <p>{content.description}</p>
            </div>
            <div className={styles.imageContent}>
              <img src={content.imageUrl} alt={content.title} />
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default Automation;

.webDevelopment {
  background-color: #182b2a;
  min-height: 100vh;
}

/* HERO (solid black) */
.heroSection {
  padding: 4rem 2rem;
  background-color: #121b1b; // Example background
  display: flex;
  justify-content: center;
  align-items: center;

  .heroInner {
    padding-top: 120px;
    max-width: 1400px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
    margin: 0 auto;
    flex-wrap: wrap; // Ensures columns stack on smaller screens
  }

  .heroText {
    flex: 1 1 500px;
    h1 {
      font-size: 2.5rem;
      margin-bottom: 1rem;
      color: #ffffff;
      font-weight: 600;
    }
    p {
      font-size: 1.2rem;
      margin-bottom: 2rem;
      max-width: 500px;
      color: #ffffff;
      line-height: 1.5;
    }
  }

  .heroImageWrapper {
    flex: 0 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;

    .heroImage {
      max-width: 400px;
      width: 100%;
      height: auto;
      border-radius: 20px; // Example styling
    }
  }
}

/* NAV MENU (pill style) */
.navMenu {
  margin-top: 2rem;

  ul {
      display: flex;
      flex-wrap: wrap;
      gap: 0.75rem;
      list-style: none;
      padding: 0;
      margin: 0;
  }

  li {
    margin: 0;

    a {
      display: inline-block;
      padding: 0.5rem 1.5rem;
      border: 0.5px solid #23452e;
      border-radius: 9999px;
      background-color: #182b2a;
      color: #fff;
      font-size: 0.9rem;
      font-weight: 400;
      text-decoration: none;
      transition:
        color 0.3s ease,
        background-color 0.3s ease,
        border-color 0.3s ease;
    }

    a:hover {
      border-color: #2fc39f;
    }

    a.active {
      border-color: #2fc39f;
      border-width: 2px;
      background-color: rgba(47, 195, 159, 0.1);
      color: #2fc39f;
      font-weight: 500;
    }
  }
}

/* IMAGE SECTION (starry skyline, fixed height, full width) */
.imageSection {
  width: 100%;
  background-color: #182b2a;
  padding-bottom: 20px;

  .imageWrapper {
    width: 100%;
    height: 700px; /* ADJUST THIS HEIGHT AS NEEDED */
    overflow: hidden;

    .starryImage {
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: center;
      display: block;
    }
  }
}

/* INFO SECTION (green background) */
.infoSection {
  background-color: #121b1b;
  padding: 8rem 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10rem;
}

/* INFO CARDS */
.infoCard {
  display: flex;
  gap: 2rem;
  width: 100%;
  max-width: 1500px;

  /* On smaller screens, stack them */
  @media (max-width: 768px) {
    flex-direction: column;
  }
}

/* The text container with a white background */
.cardContent {
  border: 0.5px solid #23452e;
  flex: 1;
  background: #1a2626; /* #1a2626 */
  border-radius: 8px;
  padding: 2rem;
  color: #ffffff;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

  h2 {
    margin-top: 0;
    color: #ffffff;
    font-size: 2rem;
    font-weight: 550;
    margin-bottom: 2rem;
  }

  p {
    line-height: 1.6;
    margin-bottom: 1rem;
  }

  ul {
    list-style: disc;
    padding-left: 1.5rem;

    li {
      margin-bottom: 0.5rem;
    }
  }
}

/* The image container with its own border-radius */
.cardImageContainer {
  flex: 0 0 40%;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 0.5px solid #23452e;

  /* On smaller screens, full width */
  @media (max-width: 768px) {
    width: 100%;
    flex: unset;
  }
}

/* The image itself just fills the container */
.cardImage {
  width: 100%;
  height: auto;
  display: block;
  object-fit: inherit;
}

/* DROPDOWN CONTAINER */
.dropdownContainer {
  width: 100%;
  max-width: 1500px;

  h2 {
    font-size: 2.5rem;
    color: #ffffff;
    margin-bottom: 2rem;
    text-align: center;
  }

  .dropdownItem {
    border-bottom: 1px solid #23452e;
    padding: 1.5rem 0;
    color: #ffffff;

    &:first-child {
      border-top: 1px solid #23452e;
    }

    .dropdownHeader {
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;
      padding: 0.75rem 1.5rem;
      background: #182b2a;
      border-radius: 8px;
      transition: background 0.3s ease;

      h3 {
        font-size: 1.75rem;
        margin: 0;
        font-weight: 500;
      }

      span {
        font-size: 2rem;
        font-weight: 600;
        color: #2fc39f;
      }

      &:hover {
        background: #23452e;
      }
    }

    .dropdownContent {
      padding: 1.5rem;
      background: #1a2626;
      border-radius: 0 0 8px 8px;
      border: 0.5px solid #23452e;
      border-top: none;

      p {
        font-size: 1.25rem;
        line-height: 1.6;
        color: #ffffff;
      }
    }
  }
}
/* Scrollbar for WebKit browsers */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #1a262600;
  border-radius: 10px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #121b1b;
  border-radius: 10px;
  border: 0.5px solid #23452e;
  transition: background 0.3s ease;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #121b1b;
}

.contentSection {
  max-width: 1500px;
  margin: 0 auto;
  padding: 4rem 2rem;
  display: flex;
  flex-direction: column;
  gap: 6rem; // Increased from 8rem to create more vertical space between blocks
  scroll-behavior: smooth;
  position: relative;
}

.contentBlock {
  min-height: 20vh;
  display: flex;
  align-items: center;
  justify-content: space-between; // Changed from center to space-between
  padding: 2rem;
  position: relative;
  gap: 4rem; // Added significant horizontal gap between text and image

  &:nth-child(even) {
    flex-direction: row-reverse;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 4rem; // Reduced gap for mobile but still maintaining good spacing
    min-height: auto;
    padding: 1rem;
  }
}

.textContent {
  flex: 0 1 45%; // Changed from flex: 1 to give specific width
  transition: opacity 0.3s ease;
  max-width: 600px; // Added max-width to prevent text from stretching too wide

  h2 {
    font-size: 2.5rem;
    color: #f1f5f9;
    margin-bottom: 2rem; // Increased from 1.5rem
    font-weight: 700;
  }

  p {
    font-size: 1.1rem;
    color: #94a3b8;
    line-height: 1.8; // Increased from 1.6
  }
}

.imageContent {
  flex: 0 1 47%; // Changed from flex: 1 to give specific width
  display: flex;
  justify-content: center;
  align-items: center;
  transition: transform 0.3s ease;

  img {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
    border: 0.5px solid #23452e;
  }
}

import React, { useRef, useState } from "react";
import { motion, useScroll } from "framer-motion";
import styles from "./AppDevelopment.module.scss";

const GraphicDesign = () => {
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const containerRef = useRef<HTMLDivElement | null>(null);

  useScroll({
    target: containerRef,
    offset: ["start start", "end end"]
  });

  const handleScroll = () => {
    if (!containerRef.current) return;

    const cards = containerRef.current.querySelectorAll(`.${styles.contentBlock}`);
    const containerTop = containerRef.current.offsetTop;
    const scrollPosition = window.scrollY + window.innerHeight / 2;

    cards.forEach((card, index) => {
      // Type assertion to tell TypeScript this is an HTMLElement
      const cardElement = card as HTMLElement;
      const cardTop = cardElement.offsetTop + containerTop;
      const cardBottom = cardTop + cardElement.offsetHeight;

      if (scrollPosition >= cardTop && scrollPosition <= cardBottom) {
        setActiveIndex(index);
      }
    });
  };

  React.useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const heroImage = "https://images.unsplash.com/photo-1626785774573-4b799315345d?q=80&w=2071&auto=format&fit=crop";

  return (
    <div className={styles.webDevelopment}>
      {/* HERO SECTION */}
      <motion.header
        className={styles.heroSection}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        <div className={styles.heroInner}>
          <motion.div
            className={styles.heroText}
            initial={{ opacity: 0, x: -80 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <h1>GRAPHIC DESIGN SERVICES</h1>
            <p>
              Creating visually compelling designs that capture attention, communicate messages, and elevate your brand.
            </p>
          </motion.div>

          <motion.div
            className={styles.heroImageWrapper}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <img
              src={heroImage}
              alt="Graphic Design Hero"
              className={styles.heroImage}
            />
          </motion.div>
          <motion.div
            className={styles.heroNav}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <nav className={styles.navMenu}>
              <ul>
                <li><a href="/services/web-development">Web Development</a></li>
                <li><a href="/services/app-development">App Development</a></li>
                <li><a href="/services/graphic-design" className={styles.active}>Graphic Design</a></li>
                <li><a href="/services/automation">Automation</a></li>
                <li><a href="/services/marketing">Marketing</a></li>
                <li><a href="/services/email">Email</a></li>
              </ul>
            </nav>
          </motion.div>
        </div>
      </motion.header>

      {/* Content Section */}
      <div className={styles.contentSection} ref={containerRef}>
        {[
          {
            title: "Graphic Design that Captivates and Communicates",
            description: "At Veltrix, we transform ideas into visually stunning designs that resonate with your audience. Our graphic design services encompass everything from branding and visual identity to digital graphics and print materials. With a focus on creativity, clarity, and consistency, we help you create designs that leave a lasting impression.",
            imageUrl: "https://images.unsplash.com/photo-1561070791-2526d30994b5?q=80&w=2070&auto=format&fit=crop"
          },
          {
            title: "Brand Identity and Logo Design",
            description: "Your brand identity is the foundation of your business. We create memorable logos and cohesive brand elements that reflect your values, resonate with your audience, and set you apart from the competition.",
            imageUrl: "https://images.unsplash.com/photo-1524758631624-e2822e304c36?q=80&w=2070&auto=format&fit=crop"
          },
          {
            title: "Visual Storytelling with Infographics",
            description: "Simplify complex information with visually engaging infographics. Our designers combine compelling visuals with clear messaging to help you convey your ideas quickly and effectively.",
            imageUrl: "https://images.unsplash.com/photo-1542744094-3a31f272c490?q=80&w=2070&auto=format&fit=crop"
          },
          {
            title: "Print Design that Leaves a Lasting Impression",
            description: "From business cards and brochures to posters and packaging, our print designs are crafted to make an impact. We ensure that every print material is visually appealing, professionally designed, and aligned with your brand identity.",
            imageUrl: "https://images.unsplash.com/photo-1544819679-57b273c767dc?q=80&w=2070&auto=format&fit=crop"
          },
          {
            title: "Digital Graphics for Online Success",
            description: "Capture your audience's attention with eye-catching digital graphics. Whether it's social media posts, website banners, or email graphics, we create visuals that are optimized for digital platforms and designed to engage.",
            imageUrl: "https://images.unsplash.com/photo-1572044162444-ad60f128bdea?q=80&w=2070&auto=format&fit=crop"
          },
          {
            title: "User Interface (UI) Design",
            description: "Our UI design services focus on creating visually appealing and intuitive interfaces that enhance user engagement and satisfaction. We design user-friendly layouts that align with your brand and deliver seamless experiences.",
            imageUrl: "https://images.unsplash.com/photo-1581291518857-4e27b48ff24e?q=80&w=2070&auto=format&fit=crop"
          },
          {
            title: "Packaging Design that Stands Out",
            description: "Your packaging is often the first point of contact with your customers. We design packaging that captures attention, communicates your brand message, and enhances the customer experience.",
            imageUrl: "https://images.unsplash.com/photo-1636955779321-819753cd1741?q=80&w=2070&auto=format&fit=crop"
          },
          {
            title: "Illustration and Custom Artwork",
            description: "Bring your ideas to life with custom illustrations and artwork. Our talented illustrators create unique visuals that add personality and creativity to your brand, products, and marketing materials.",
            imageUrl: "https://images.unsplash.com/photo-1618005182384-a83a8bd57fbe?q=80&w=2070&auto=format&fit=crop"
          },
          {
            title: "Motion Graphics and Animation",
            description: "Add movement and energy to your visual content with motion graphics and animations. Whether it's for social media, presentations, or video content, our animations capture attention and convey your message effectively.",
            imageUrl: "https://images.unsplash.com/photo-1551269901-5c5e14c25df7?q=80&w=2070&auto=format&fit=crop"
          }
        ].map((content, index) => (
          <motion.div
            key={index}
            className={styles.contentBlock}
            initial={{ opacity: 0.2 }}
            animate={{
              opacity: activeIndex === index ? 1 : 0.1,
              scale: activeIndex === index ? 1 : 0.98
            }}
            transition={{ duration: 0.5 }}
          >
            <div className={styles.textContent}>
              <h2>{content.title}</h2>
              <p>{content.description}</p>
            </div>
            <div className={styles.imageContent}>
              <img src={content.imageUrl} alt={content.title} />
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default GraphicDesign;

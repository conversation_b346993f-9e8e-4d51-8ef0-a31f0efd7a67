import React, { useRef, useState, useEffect } from "react";
import { motion, useScroll } from "framer-motion";
import { Link } from "react-router-dom";
import styles from "./AppDevelopment.module.scss";

const AppDevelopment = () => {
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const containerRef = useRef<HTMLDivElement | null>(null);

  // Force scroll to top immediately when component mounts with a slight delay
  useEffect(() => {
    const timer = setTimeout(() => {
      window.scrollTo({
        top: 0,
        left: 0,
        behavior: 'auto'
      });
    }, 0);
    
    return () => clearTimeout(timer);
  }, []);

  useScroll({
    target: containerRef,
    offset: ["start start", "end end"]
  });

  const handleScroll = () => {
    if (!containerRef.current) return;

    const cards = containerRef.current.querySelectorAll(`.${styles.contentBlock}`);
    const containerTop = containerRef.current.offsetTop;
    const scrollPosition = window.scrollY + window.innerHeight / 2;

    cards.forEach((card, index) => {
      // Type assertion to tell TypeScript this is an HTMLElement
      const cardElement = card as HTMLElement;
      const cardTop = cardElement.offsetTop + containerTop;
      const cardBottom = cardTop + cardElement.offsetHeight;

      if (scrollPosition >= cardTop && scrollPosition <= cardBottom) {
        setActiveIndex(index);
      }
    });
  };

  React.useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const heroImage = "https://images.unsplash.com/photo-1551650975-87deedd944c3?q=80&w=1974&auto=format&fit=crop";

  return (
    <div className={styles.webDevelopment}>
      {/* HERO SECTION */}
      <motion.header
        className={styles.heroSection}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        <div className={styles.heroInner}>
          <motion.div
            className={styles.heroText}
            initial={{ opacity: 0, x: -80 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <h1>APP DEVELOPMENT SERVICES</h1>
            <p>
              Transform your business with custom mobile and web applications that deliver seamless performance and exceptional user experiences.
            </p>
          </motion.div>

          <motion.div
            className={styles.heroImageWrapper}
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <img
              src={heroImage}
              alt="App Development Hero"
              className={styles.heroImage}
            />
          </motion.div>
          <motion.div
            className={styles.heroNav}
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <nav className={styles.navMenu}>
              <ul>
                <li><Link to="/services/web-development">Web Development</Link></li>
                <li><Link to="/services/app-development" className={styles.active}>App Development</Link></li>
                <li><Link to="/services/graphic-design">Graphic Design</Link></li>
                <li><Link to="/services/automation">Automation</Link></li>
                <li><Link to="/services/marketing">Marketing</Link></li>
                <li><Link to="/services/email">Email</Link></li>
              </ul>
            </nav>
          </motion.div>
        </div>
      </motion.header>

      {/* Content Section */}
      <div className={styles.contentSection} ref={containerRef}>
        {[
          {
            title: "Custom App Development to Elevate Your Business",
            description: "At Veltrix, we specialize in developing innovative and user-friendly mobile and web applications tailored to your business needs. Our team combines cutting-edge technology with intuitive design to create apps that deliver seamless performance and engaging user experiences.",
            imageUrl: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?q=80&w=1170&auto=format&fit=crop"
          },
          {
            title: "Mobile App Development",
            description: "We design and develop mobile applications that deliver exceptional performance and usability. Whether it's iOS, Android, or cross-platform development, we ensure your app runs smoothly and engages users on every device.",
            imageUrl: "https://images.unsplash.com/photo-1526498460520-4c246339dccb?q=80&w=1170&auto=format&fit=crop"
          },
          {
            title: "Cross-Platform Development",
            description: "Reach a broader audience with apps that work seamlessly across multiple platforms. Using frameworks like React Native and Flutter, we create apps that deliver native-like performance with a single codebase.",
            imageUrl: "https://images.unsplash.com/photo-1551650992-ee4fd47df41f?q=80&w=1974&auto=format&fit=crop"
          },
          {
            title: "User Experience (UX) and Interface Design (UI)",
            description: "Our UX/UI design services focus on creating visually appealing and intuitive interfaces that enhance user engagement. We ensure that every interaction is smooth, responsive, and aligned with your brand.",
            imageUrl: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?q=80&w=1170&auto=format&fit=crop"
          },
          {
            title: "Web Application Development",
            description: "From dynamic web applications to progressive web apps (PWAs), we build web solutions that are fast, responsive, and scalable. Our web applications are designed to deliver a seamless experience across all devices and browsers.",
            imageUrl: "https://images.unsplash.com/photo-1555774698-0b77e0d5fac6?q=80&w=1170&auto=format&fit=crop"
          },
          {
            title: "API Development and Integration",
            description: "Enhance the functionality of your app with custom APIs and third-party integrations. We create robust APIs that enable seamless communication between your app and other platforms, ensuring smooth data exchange and enhanced functionality.",
            imageUrl: "https://images.unsplash.com/photo-1571786256017-aee7a0c009b6?q=80&w=1170&auto=format&fit=crop"
          },
          {
            title: "Scalable Cloud Solutions",
            description: "Ensure your app can grow with your business by leveraging cloud platforms like AWS, Google Cloud, and Azure. We design scalable and secure cloud solutions that support your app's performance and reliability.",
            imageUrl: "https://images.unsplash.com/photo-1451187580459-43490279c0fa?q=80&w=1172&auto=format&fit=crop"
          },
          {
            title: "App Maintenance and Support",
            description: "Keep your app running smoothly with our ongoing maintenance and support services. We provide regular updates, bug fixes, and performance optimizations to ensure your app remains reliable and up to date.",
            imageUrl: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?q=80&w=1115&auto=format&fit=crop"
          },
          {
            title: "App Store Optimization (ASO)",
            description: "Increase your app's visibility and downloads with our app store optimization services. We optimize your app's metadata, keywords, and visuals to ensure it ranks higher in app store search results.",
            imageUrl: "https://images.unsplash.com/photo-1516321318423-f06f85e504b3?q=80&w=1170&auto=format&fit=crop"
          },
          {
            title: "Security and Data Privacy",
            description: "Protect your app and user data with our advanced security measures. We implement encryption, secure authentication, and data privacy protocols to ensure your app is safe and compliant with industry regulations.",
            imageUrl: "https://images.unsplash.com/photo-1563986768609-322da13575f3?q=80&w=1170&auto=format&fit=crop"
          }

        ].map((content, index) => (
          <motion.div
            key={index}
            className={styles.contentBlock}
            initial={{ opacity: 0.2 }}
            animate={{
              opacity: activeIndex === index ? 1 : 0.1,
              scale: activeIndex === index ? 1 : 0.98
            }}
            transition={{ duration: 0.5 }}
          >
            <div className={styles.textContent}>
              <h2>{content.title}</h2>
              <p>{content.description}</p>
            </div>
            <div className={styles.imageContent}>
              <img src={content.imageUrl} alt={content.title} />
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default AppDevelopment;


import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { motion } from "framer-motion";
import styles from "./slug.module.scss";
import { FaArrowRight } from "react-icons/fa";

// Define interfaces for the blog post data structure
interface ContentBlock {
  type?: string;
  level?: number;
  children?: Array<{
    text?: string;
  }>;
}

interface ImageData {
  data?: {
    attributes?: {
      url?: string;
    };
  };
  url?: string;
}

interface BlogAttributes {
  Title?: string;
  Slug?: string;
  Content?: ContentBlock[];
  DatePublished?: string;
  HeroImage?: ImageData | number;
  CoverImage?: ImageData | number;
}

interface BlogPost {
  id: string;
  attributes: BlogAttributes;
}

export default function BlogPost() {
  const { slug } = useParams<{ slug: string }>();
  const [post, setPost] = useState<BlogPost | null>(null);
  const [relatedPosts, setRelatedPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPost = async () => {
      try {
        const response = await fetch(
          `http://localhost:1337/api/blogs?filters[Slug][$eq]=${slug}&populate=*`
        );
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const data = await response.json();

        // Log the full API response to see the structure
        console.log('API Response:', data);

        if (data?.data?.length > 0) {
          setPost(data.data[0]);

          // Fetch related posts (excluding current post)
          fetchRelatedPosts(data.data[0].id);
        } else {
          setError("Blog post not found");
          setLoading(false);
        }
      } catch (err) {
        console.error("Error fetching blog post:", err);
        setError("Failed to load blog post");
        setLoading(false);
      }
    };

    const fetchRelatedPosts = async (currentPostId: string) => {
      try {
        // Fetch up to 3 other blog posts, excluding the current one
        const response = await fetch(
          `http://localhost:1337/api/blogs?filters[id][$ne]=${currentPostId}&populate=*&pagination[limit]=3`
        );

        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        console.log('Related Posts:', data);

        if (data?.data) {
          setRelatedPosts(data.data);
        }
      } catch (err) {
        console.error("Error fetching related posts:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchPost();
  }, [slug]);

  if (loading) return <div className={styles.container}><p className={styles.loading}>Loading blog post...</p></div>;
  if (error) return <div className={styles.container}><p className={styles.error}>{error}</p></div>;
  if (!post) return <div className={styles.container}><p className={styles.error}>Blog post not found</p></div>;

  const { attributes } = post;
  const { Title, Content, DatePublished, HeroImage, CoverImage } = attributes;

  // Log the image data to see the structure
  console.log('Hero Image Data:', HeroImage);
  console.log('Cover Image Data:', CoverImage);

  // Get hero image URL - handle different possible structures
  let heroImageUrl: string | null = null;

  // Try different possible structures for the hero image data
  if (HeroImage && typeof HeroImage !== 'number' && HeroImage.data?.attributes?.url) {
    // Standard Strapi v4 structure
    const url = HeroImage.data.attributes.url;
    heroImageUrl = url.startsWith('http')
      ? url
      : `http://localhost:1337${url}`;
  } else if (HeroImage && typeof HeroImage !== 'number' && HeroImage.url) {
    // Alternative structure
    const url = HeroImage.url;
    heroImageUrl = url.startsWith('http')
      ? url
      : `http://localhost:1337${url}`;
  } else if (typeof HeroImage === 'number') {
    // If HeroImage is just an ID, construct a URL to fetch the image
    heroImageUrl = `http://localhost:1337/api/upload/files/${HeroImage}`;
  }

  // Get cover image URL - handle different possible structures
  let coverImageUrl: string | null = null;

  // Try different possible structures for the cover image data
  if (CoverImage && typeof CoverImage !== 'number' && CoverImage.data?.attributes?.url) {
    // Standard Strapi v4 structure
    const url = CoverImage.data.attributes.url;
    coverImageUrl = url.startsWith('http')
      ? url
      : `http://localhost:1337${url}`;
  } else if (CoverImage && typeof CoverImage !== 'number' && CoverImage.url) {
    // Alternative structure
    const url = CoverImage.url;
    coverImageUrl = url.startsWith('http')
      ? url
      : `http://localhost:1337${url}`;
  } else if (typeof CoverImage === 'number') {
    // If CoverImage is just an ID, construct a URL to fetch the image
    coverImageUrl = `http://localhost:1337/api/upload/files/${CoverImage}`;
  }

  console.log('Hero Image URL:', heroImageUrl);
  console.log('Cover Image URL:', coverImageUrl);

  return (
    <div className={styles.container}>
      {/* Hero Section */}
      <motion.header
        className={styles.heroSection}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
        style={heroImageUrl ? {
          backgroundImage: `url(${heroImageUrl})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center'
        } : {}}
      >
        <div className={styles.heroInner}>
          <motion.div
            className={styles.heroText}
            initial={{ opacity: 0, x: -80 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <h1>{Title}</h1>
            {DatePublished && (
              <p className={styles.date}>
                {new Date(DatePublished).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </p>
            )}
          </motion.div>
        </div>
      </motion.header>

      {/* Content Section */}
      <motion.section
        className={styles.contentSection}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, delay: 0.4 }}
      >
        <div className={styles.contentWrapper}>
          <div className={styles.mainContent}>
            <article className={styles.blogPost}>
              {coverImageUrl && (
                <div className={styles.coverImageContainer}>
                  <img src={coverImageUrl} alt={Title} className={styles.coverImage} />
                </div>
              )}
              <div className={styles.content}>
                {Content && Array.isArray(Content) && Content.map((block: ContentBlock, index: number) => {
                  // Handle different block types
                  if (block.type === 'paragraph') {
                    return (
                      <p key={index}>
                        {block.children?.map((child, childIndex: number) => (
                          <span key={childIndex}>{child.text}</span>
                        ))}
                      </p>
                    );
                  } else if (block.type === 'heading' && block.level === 2) {
                    return (
                      <h2 key={index}>
                        {block.children?.map((child, childIndex: number) => (
                          <span key={childIndex}>{child.text}</span>
                        ))}
                      </h2>
                    );
                  } else if (block.type === 'heading' && block.level === 3) {
                    return (
                      <h3 key={index}>
                        {block.children?.map((child, childIndex: number) => (
                          <span key={childIndex}>{child.text}</span>
                        ))}
                      </h3>
                    );
                  } else {
                    // Default rendering for other block types
                    return (
                      <div key={index}>
                        {block.children?.map((child, childIndex: number) => (
                          <p key={childIndex}>{child.text}</p>
                        ))}
                      </div>
                    );
                  }
                })}
              </div>
            </article>
          </div>

          {/* Sidebar with Related Posts */}
          <div className={styles.sidebar}>
            <div className={styles.relatedPosts}>
              <h3>Related Articles</h3>
              {relatedPosts.length > 0 ? (
                relatedPosts.map((relatedPost) => {
                  const { id, attributes } = relatedPost;
                  const { Title, Slug, CoverImage } = attributes;

                  // Get cover image URL
                  let relatedImageUrl: string | null = null;
                  if (CoverImage && typeof CoverImage !== 'number' && CoverImage.data?.attributes?.url) {
                    const url = CoverImage.data.attributes.url;
                    relatedImageUrl = url.startsWith('http')
                      ? url
                      : `http://localhost:1337${url}`;
                  } else if (CoverImage && typeof CoverImage !== 'number' && CoverImage.url) {
                    const url = CoverImage.url;
                    relatedImageUrl = url.startsWith('http')
                      ? url
                      : `http://localhost:1337${url}`;
                  } else if (typeof CoverImage === 'number') {
                    relatedImageUrl = `http://localhost:1337/api/upload/files/${CoverImage}`;
                  }

                  return (
                    <div key={id} className={styles.relatedPostItem}>
                      <Link to={`/blog/${Slug}`}>
                        {relatedImageUrl && (
                          <img
                            src={relatedImageUrl}
                            alt={Title}
                            className={styles.relatedPostImage}
                          />
                        )}
                        <h4 className={styles.relatedPostTitle}>{Title}</h4>
                        <div className={styles.readMore}>
                          Read blog <FaArrowRight />
                        </div>
                      </Link>
                    </div>
                  );
                })
              ) : (
                <p>No related articles found</p>
              )}
            </div>
          </div>
        </div>
      </motion.section>
    </div>
  );
}

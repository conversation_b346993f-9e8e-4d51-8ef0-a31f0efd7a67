/* Main container for the entire blog page */
.container {
  min-height: 100vh;
  max-width: 100vw;
  background-color: #182b2a;
}

/* Hero section with darker background */
.heroSection {
  padding: 4rem 2rem;
  background-color: #121b1b;
  display: flex;
  justify-content: center;
  align-items: center;
}

.heroInner {
  padding-top: 120px;
  max-width: 1400px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
}

.heroText {
  text-align: center;

  h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #ffffff;
    font-weight: 600;
  }
}

/* Grid container with lighter background */
.gridContainer {
  padding: 4rem 2rem;
  background-color: #182b2a;
  display: flex;
  justify-content: center;
}

.gridWrapper {
  width: 90%;
  max-width: 1000px; /* Reduced from 1250px */
}

  /* Container styling consolidated into .container */

.grid {
    width: 100%;
    margin: 0 auto;
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    grid-auto-rows: 22rem; /* Standardized height */

    @media (min-width: 768px) {
      grid-template-columns: repeat(3, 1fr);
      gap: 6px; /* Consistent gap with main BentoGrid */
    }
  }

  .image {
    width: 100%;
    height: 180px;
    object-fit: cover;
    border-radius: 10px;
  }

  /* Span classes for grid layout */
  .spanOne {
    grid-column: span 1;
  }

  .spanTwo {
    grid-column: span 2;
  }

  .placeholder {
    width: 100%;
    height: 250px;
    background-color: #2d2d2d;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    border-radius: 8px;
  }

  .loading,
  .error,
  .noBlogs {
    text-align: center;
    font-size: 1.2rem;
    color: white;
  }

  .link {
    text-decoration: none;
    color: inherit;
    display: block;
    height: 100%;
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-5px);
    }
  }
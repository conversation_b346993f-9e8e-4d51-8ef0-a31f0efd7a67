.container {
  min-height: 100vh;
  background-color: #182b2a;
  color: #ffffff;
}

/* Hero Section */
.heroSection {
  padding: 8rem 2rem;
  background-color: #121b1b;
  display: flex;
  justify-content: center;
  align-items: center;
  background-size: cover;
  background-position: center;
  position: relative;
  min-height: 500px;
  background-attachment: fixed;
  transition: all 0.3s ease;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(18, 27, 27, 0.7), rgba(24, 43, 42, 0.9));
    z-index: 1;
  }
}

.heroInner {
  padding-top: 120px;
  max-width: 1400px;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.heroText {
  text-align: center;
  background-color: rgba(18, 27, 27, 0.6);
  padding: 2rem;
  border-radius: 8px;
  backdrop-filter: blur(5px);
  border: 0.5px solid rgba(63, 169, 142, 0.3);
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);

  h1 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #ffffff;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  }

  .date {
    font-size: 1.1rem;
    color: #3fa98e;
    margin-bottom: 1rem;
    font-weight: 500;
  }
}

/* Content Section */
.contentSection {
  padding: 4rem 2rem;
  background-color: #182b2a;
  display: flex;
  justify-content: center;
}

.contentWrapper {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: row;
  gap: 2rem;

  @media (max-width: 992px) {
    flex-direction: column;
  }
}

.mainContent {
  flex: 1;
}

.sidebar {
  width: 320px;

  @media (max-width: 992px) {
    width: 100%;
  }
}

.relatedPosts {
  background-color: #1a2626;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  border: 0.5px solid #23452e;

  h3 {
    font-size: 1.4rem;
    color: #3fa98e;
    margin-bottom: 1.5rem;
    padding-bottom: 0.8rem;
    border-bottom: 1px solid rgba(63, 169, 142, 0.2);
  }
}

.relatedPostItem {
  margin-bottom: 1.5rem;

  &:last-child {
    margin-bottom: 0;
  }

  a {
    display: flex;
    flex-direction: column;
    text-decoration: none;
    color: inherit;

    &:hover {
      .relatedPostTitle {
        color: #3fa98e;
      }
    }
  }

  .relatedPostImage {
    width: 100%;
    height: 160px;
    object-fit: cover;
    border-radius: 6px;
    margin-bottom: 0.8rem;
  }

  .relatedPostTitle {
    font-size: 1rem;
    font-weight: 500;
    color: #f0f0f0;
    margin-bottom: 0.5rem;
    transition: color 0.2s ease;
  }

  .readMore {
    font-size: 0.9rem;
    color: #3fa98e;
    display: flex;
    align-items: center;

    svg {
      margin-left: 0.3rem;
      font-size: 0.8rem;
    }
  }
}

.blogPost {
  background-color: #1a2626;
  border-radius: 8px;
  padding: 2.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  border: 0.5px solid #23452e;
}

.coverImageContainer {
  margin: -2.5rem -2.5rem 2rem -2.5rem;
  overflow: hidden;
  border-radius: 8px 8px 0 0;
  max-height: 400px;
}

.coverImage {
  width: 100%;
  height: 400px;
  object-fit: cover;
  display: block;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.02);
  }
}

.content {
  line-height: 1.8;

  p {
    margin-bottom: 1.8rem;
    color: #f0f0f0;
    font-size: 1.15rem;
    letter-spacing: 0.01em;
  }

  h2 {
    font-size: 2.2rem;
    font-weight: 600;
    color: #3fa98e;
    margin-top: 2.5rem;
    margin-bottom: 1.5rem;
  }

  h3 {
    font-size: 1.8rem;
    font-weight: 500;
    color: #3fa98e;
    margin-top: 2rem;
    margin-bottom: 1.2rem;
  }
}

.loading, .error {
  text-align: center;
  padding: 2rem;
  font-size: 1.2rem;
  color: #ffffff;
}

.error {
  color: #ff6b6b;
}

/* Responsive Design */
@media (max-width: 768px) {
  .heroSection {
    padding: 2rem 1rem;
  }

  .heroInner {
    padding-top: 80px;
  }

  .heroText {
    h1 {
      font-size: 2rem;
    }
  }

  .contentSection {
    padding: 2rem 1rem;
  }

  .blogPost {
    padding: 1.5rem;
  }

  .content {
    h2 {
      font-size: 1.8rem;
    }

    h3 {
      font-size: 1.5rem;
    }

    p {
      font-size: 1rem;
    }
  }

  .sidebar {
    margin-top: 2rem;
  }

  .relatedPosts {
    padding: 1.2rem;
  }

  .relatedPostItem {
    margin-bottom: 1.2rem;

    .relatedPostImage {
      height: 140px;
    }
  }
}

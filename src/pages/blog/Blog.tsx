
import { useEffect, useState } from "react";
import { <PERSON> } from "react-router-dom";
import { motion } from "framer-motion";
import { BentoGrid, BentoGridItem } from "../../components/BentoGrid/index";
import styles from "./Blog.module.scss";

// Define interfaces for the blog data structure
interface BlogContent {
  children?: Array<{
    text?: string;
  }>;
}

interface BlogImage {
  url?: string;
}

interface BlogAttributes {
  Title?: string;
  Slug?: string;
  Content?: string | BlogContent[];
  CoverImage?: BlogImage;
  DatePublished?: string;
}

interface BlogItem {
  id: string;
  attributes: BlogAttributes;
}

// Animation Variants
const gridVariants = {
  hidden: { opacity: 0, y: 50, scale: 0.9 },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      type: "spring",
      bounce: 0.4,
      duration: 0.8,
      staggerChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 50, scale: 0.9 },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: { duration: 0.5 },
  },
};



export default function Blog() {
  const [blogs, setBlogs] = useState<BlogItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBlogs = async () => {
      try {
        const response = await fetch("http://localhost:1337/api/blogs?populate=*");
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        console.log("Fetched blogs:", data);

        if (data && data.data) {
          setBlogs(data.data);
        } else {
          setBlogs([]);
        }
      } catch (err) {
        console.error("Error fetching blogs:", err);
        setError("Failed to load blog posts. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchBlogs();
  }, []);

  if (loading) {
    return <p className={styles.loading}>Loading blog posts...</p>;
  }

  if (error) {
    return <p className={styles.error}>{error}</p>;
  }

  if (blogs.length === 0) {
    return <p className={styles.noBlogs}>No blog posts available.</p>;
  }

  return (
    <div className={styles.container}>
      {/* Hero Section with Title */}
      <motion.header
        className={styles.heroSection}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        <div className={styles.heroInner}>
          <motion.div
            className={styles.heroText}
            initial={{ opacity: 0, y: -30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <h1>LATEST BLOG POSTS</h1>
          </motion.div>
        </div>
      </motion.header>

      {/* Grid Container with Blog Posts */}
      <section className={styles.gridContainer}>
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: false, amount: 0.4 }}
          variants={gridVariants}
          className={styles.gridWrapper}
        >
          <BentoGrid className={styles.grid}>
            {blogs.map((blog, index) => {
              const { id, attributes } = blog || {};
              const { Title, Slug, Content, CoverImage, DatePublished } = attributes || {};

              // Ensure Content is extracted correctly and limited
              let previewContent = "No content available.";
              if (typeof Content === "string") {
                previewContent = Content.split("\n").slice(0, 1).join(" ");
                if (previewContent.length > 80) {
                  previewContent = previewContent.substring(0, 80) + "...";
                }
              } else if (Array.isArray(Content)) {
                previewContent = Content.length > 0 ? Content[0]?.children?.[0]?.text || "No content available." : "No content available.";
                if (previewContent.length > 80) {
                  previewContent = previewContent.substring(0, 80) + "...";
                }
              }

              // Format date
              const formattedDate = DatePublished ? new Date(DatePublished).toLocaleDateString() : "Unknown date";

              // Get cover image URL
              const imageUrl = CoverImage?.url
                ? `http://localhost:1337${CoverImage.url}`
                : null;

              // Determine span class (every third item spans two columns)
              const spanClass = index % 3 === 0 ? styles.spanTwo : styles.spanOne;

              return (
                <motion.div key={id} variants={itemVariants} className={spanClass}>
                  <Link to={`/blog/${Slug}`} className={styles.link}>
                    <BentoGridItem
                      title={Title || "Untitled Post"}
                      description={`${formattedDate} - ${previewContent}`}
                      header={
                        imageUrl ? (
                          <img src={imageUrl} alt={Title || "Blog post"} className={styles.image} />
                        ) : (
                          <div className={styles.placeholder}>No Image Available</div>
                        )
                      }
                    />
                  </Link>
                </motion.div>
              );
            })}
          </BentoGrid>
        </motion.div>
      </section>
    </div>
  );
}
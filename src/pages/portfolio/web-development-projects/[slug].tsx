
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { motion } from "framer-motion";
import styles from "./index.module.scss";

// Define interfaces for the project data structure
interface ImageData {
  data?: {
    attributes?: {
      url?: string;
    };
  };
  url?: string;
  attributes?: {
    url?: string;
  };
}

interface ProjectDescription {
  children?: Array<{
    text?: string;
  }>;
}

interface WebDevelopmentProject {
  id?: string;
  Title?: string;
  Slug?: string;
  Title1?: string;
  Title2?: string;
  Title3?: string;
  Title4?: string;
  Description?: ProjectDescription[];
  Description2?: ProjectDescription[];
  Description3?: ProjectDescription[];
  Description4?: ProjectDescription[];
  Thumbnail?: ImageData;
  Image2?: ImageData;
  Image3?: ImageData;
  Image4?: ImageData;
}

// Helper function to get the image URL
const getImageUrl = (image: ImageData | undefined): string | null => {
  if (!image) return null;
  if (image.data && image.data.attributes && image.data.attributes.url) {
    return `http://localhost:1337${image.data.attributes.url}`;
  }
  if (image.url) {
    return `http://localhost:1337${image.url}`;
  }
  if (image.attributes && image.attributes.url) {
    return `http://localhost:1337${image.attributes.url}`;
  }
  return null;
};

export default function PortfolioProjectWeb() {
  const { slug } = useParams<{ slug: string }>();
  const [project, setProject] = useState<WebDevelopmentProject | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchProject = async () => {
      try {
        const response = await fetch(
          `http://localhost:1337/api/web-development-projects?filters[Slug][$eq]=${slug}&populate=*`
        );
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const data = await response.json();
        console.log("Fetched project:", data);
        if (data?.data?.length > 0) {
          setProject(data.data[0]); // Use the first (and only) project
        } else {
          setProject(null);
        }
      } catch (err) {
        console.error("Error fetching project:", err);
        setError("Project not found.");
      } finally {
        setLoading(false);
      }
    };

    fetchProject();
  }, [slug]);

  if (loading) return <p className={styles.loading}>Loading project...</p>;
  if (error || !project) return <p className={styles.error}>Project not found.</p>;

  // Since fields are directly on the project object:
  const item: WebDevelopmentProject = project;

  return (
    <motion.div
      className={styles.container}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 1 }}
    >

        <div className={styles.section1}>
          <h1>{item.Title || "Untitled Project"}</h1>
        </div>

      <div className={styles.content}>
        {/* Introduction Section */}
        <section className={styles.section}>
          <div className={styles.text}>
            <h2>{item.Title1 || "Introduction"}</h2>
            <p>
              {item.Description?.[0]?.children?.[0]?.text || "No description available."}
            </p>
          </div>
          {getImageUrl(item.Thumbnail) ? (
            <motion.img
              src={getImageUrl(item.Thumbnail) || ""}
              alt={item.Title1 || "Introduction"}
              className={styles.image}
              whileHover={{ scale: 1.05 }}
            />
          ) : (
            <div className={styles.placeholder}>No Image Available</div>
          )}
        </section>

        {/* The Brief Section */}
        <section className={styles.section}>
          <div className={styles.text}>
            <h2>{item.Title2 || "The Brief"}</h2>
            <p>
              {item.Description2?.[0]?.children?.[0]?.text || "No brief available."}
            </p>
          </div>
          {getImageUrl(item.Image2) ? (
            <motion.img
              src={getImageUrl(item.Image2) || ""}
              alt={item.Title2 || "The Brief"}
              className={styles.image}
              whileHover={{ scale: 1.05 }}
            />
          ) : (
            <div className={styles.placeholder}>No Image Available</div>
          )}
        </section>

        {/* The Result Section */}
        <section className={styles.section}>
          <div className={styles.text}>
            <h2>{item.Title3 || "The Result"}</h2>
            <p>
              {item.Description3?.[0]?.children?.[0]?.text || "No results available."}
            </p>
          </div>
          {getImageUrl(item.Image3) ? (
            <motion.img
              src={getImageUrl(item.Image3) || ""}
              alt={item.Title3 || "The Result"}
              className={styles.image}
              whileHover={{ scale: 1.05 }}
            />
          ) : (
            <div className={styles.placeholder}>No Image Available</div>
          )}
        </section>

        {/* Testimonial Section */}
        <section className={styles.section}>
          <div className={styles.text}>
            <h2>{item.Title4 || "Testimonial"}</h2>
            <p>
              {item.Description4?.[0]?.children?.[0]?.text || "No testimonial available."}
            </p>
          </div>
          {getImageUrl(item.Image4) ? (
            <motion.img
              src={getImageUrl(item.Image4) || ""}
              alt={item.Title4 || "Testimonial"}
              className={styles.image}
              whileHover={{ scale: 1.05 }}
            />
          ) : (
            <div className={styles.placeholder}>No Image Available</div>
          )}
        </section>
      </div>
    </motion.div>
  );
}
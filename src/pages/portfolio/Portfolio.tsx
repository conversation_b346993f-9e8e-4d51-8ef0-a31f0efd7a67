import React, { useRef, useState, useEffect } from "react";
import { motion, useScroll } from "framer-motion";
import { Link } from "react-router-dom";
import styles from "./Portfolio.module.scss";

const Portfolio = () => {
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const containerRef = useRef<HTMLDivElement | null>(null);

  // Force scroll to top immediately when component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      window.scrollTo({
        top: 0,
        left: 0,
        behavior: 'auto'
      });
    }, 0);

    return () => clearTimeout(timer);
  }, []);

  // Track scroll progress to determine which card is active
  useScroll({
    target: containerRef,
    offset: ["start start", "end end"]
  });

  // Update active index based on scroll position
  useEffect(() => {
    const handleScroll = () => {
      if (!containerRef.current) return;

      const children = containerRef.current.children;
      const scrollPosition = window.scrollY + window.innerHeight / 2;

      for (let i = 0; i < children.length; i++) {
        const element = children[i] as HTMLElement;
        const topPosition = element.offsetTop;
        const bottomPosition = topPosition + element.offsetHeight;

        if (scrollPosition >= topPosition && scrollPosition < bottomPosition) {
          setActiveIndex(i);
          break;
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const heroImage = "https://images.unsplash.com/photo-1522542550221-31fd19575a2d?q=80&w=2070&auto=format&fit=crop";

  return (
    <div className={styles.webDevelopment}>
      {/* HERO SECTION */}
      <motion.header
        className={styles.heroSection}
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8 }}
      >
        <div className={styles.heroInner}>
          <motion.div
            className={styles.heroText}
            initial={{ opacity: 0, x: -80 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <h1>OUR PORTFOLIO</h1>
            <p>
              Explore our diverse collection of projects showcasing our expertise in web development, app development, and graphic design.
            </p>

            {/* Navigation Menu */}
            <nav className={styles.navMenu}>
              <ul>
                <li><Link to="/portfolio/web-development">Web Development</Link></li>
                <li><Link to="/portfolio/app-development">App Development</Link></li>
                <li><Link to="/portfolio/graphic-design">Graphic Design</Link></li>
              </ul>
            </nav>
          </motion.div>

          <motion.div
            className={styles.heroImageWrapper}
            initial={{ opacity: 0, x: 80 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <img
              src={heroImage}
              alt="Portfolio Hero"
              className={styles.heroImage}
            />
          </motion.div>
        </div>
      </motion.header>

      {/* Content Section */}
      <div className={styles.contentSection} ref={containerRef}>
        {[
          {
            title: "Innovative Solutions for Modern Businesses",
            description: "At Veltrix, we pride ourselves on delivering cutting-edge digital solutions that help businesses thrive in today's competitive landscape. Our portfolio showcases a diverse range of projects across various industries, demonstrating our commitment to excellence and innovation.",
            imageUrl: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?q=80&w=2015&auto=format&fit=crop"
          },
          {
            title: "Web Development Excellence",
            description: "Our web development projects demonstrate our expertise in creating responsive, user-friendly websites that deliver exceptional user experiences. From e-commerce platforms to corporate websites, we build solutions that drive results and help businesses achieve their goals.",
            imageUrl: "https://images.unsplash.com/photo-1547658719-da2b51169166?q=80&w=2064&auto=format&fit=crop"
          },
          {
            title: "Mobile App Innovation",
            description: "Our mobile applications showcase our ability to create intuitive, feature-rich apps that engage users and solve real-world problems. We develop for both iOS and Android platforms, ensuring your app reaches the widest possible audience.",
            imageUrl: "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?q=80&w=1170&auto=format&fit=crop"
          },
          {
            title: "Creative Graphic Design",
            description: "Our graphic design portfolio highlights our creative approach to visual communication. From brand identity to marketing materials, we create designs that capture attention, communicate messages effectively, and strengthen brand recognition.",
            imageUrl: "https://images.unsplash.com/photo-1626785774573-4b799315345d?q=80&w=2071&auto=format&fit=crop"
          }
        ].map((content, index) => (
          <motion.div
            key={index}
            className={styles.contentBlock}
            initial={{ opacity: 0.2 }}
            animate={{
              opacity: activeIndex === index ? 1 : 0.1,
              scale: activeIndex === index ? 1 : 0.98
            }}
            transition={{ duration: 0.5 }}
          >
            <div className={styles.textContent}>
              <h2>{content.title}</h2>
              <p>{content.description}</p>
            </div>
            <div className={styles.imageContent}>
              <img src={content.imageUrl} alt={content.title} />
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default Portfolio;
.container {
  padding-top: 100px;
  width: 100vw; /* Full viewport width */
  background-color: #0d1f1f;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

/* HERO SECTION */
.hero {
  padding-top: 200px;
  width: 100%;
  max-width: 100vw;
  background-color: #121b1b;
  color: white;
  padding: 80px 5vw; /* Responsive padding */
}

.loading,
.error,
.noProjects {
  text-align: center;
  font-size: 1.2rem;
  color: white;
}

.title {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.description {
  font-size: 1.1rem;
  max-width: 800px;
  margin: 0 auto;
  padding-bottom: 32px;
}

/* Category Buttons */
.categories {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 10px;
}

.categories button {
  background-color: #182b2a;
  border: 0.5px solid #23452e;
  padding: 0.75rem 1.5rem;
  color: white;
  font-weight: 600;
  cursor: pointer;
  font-size: 0.9rem;
  border-radius: 9999px;
}

.activeCategory {
  background: white;
  color: black;
}

/* CARDS SECTION */
.cardsSection {
  width: 100%;
  background-color: #182b2a;
  padding: 80px 20vw;
  display: flex;
  justify-content: center;
}

.cardsGrid {
  display: grid;
  /* Standardized grid settings for consistency */
  grid-template-columns: 1fr;
  gap: 6px;
  grid-auto-rows: 22rem; /* Standardized height */
  width: 100%;

  @media (min-width: 768px) {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Card Styles */
.card {
  background-color: #1A2626;
  border: 0.5px solid #23452e;
  border-radius: 15px;
  overflow: hidden;
  transition: transform 0.2s ease-in-out;
  cursor: pointer;
  text-align: left;
  padding: 20px;

  &:hover {
    transform: translateY(-5px);
  }
}

.cardImage {
  width: 100%;
  height: 400px; /* Adjust as needed */
  object-fit: cover;
  display: block;
  border-radius: 10px;
}

.placeholder {
  width: 100%;
  height: 200px;
  background-color: #444;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #bbb;
}

.cardTitle {
  font-size: 1.2rem;
  margin: 1rem;
  color: #fff;
}

.cardDescription {
  margin: 0 1rem 1rem;
  font-size: 0.95rem;
  color: #ccc;
}

.link {
  text-decoration: none;
}

/* Optional: replicate the “spanOne” / “spanTwo” logic */
.spanOne {
  grid-column: span 1;
}

.spanTwo {
  grid-column: span 2;
}

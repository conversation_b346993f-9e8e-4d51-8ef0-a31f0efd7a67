
import { useEffect, useState } from "react";
import { useN<PERSON><PERSON>, Link } from "react-router-dom";
import { BentoGrid, BentoGridItem } from "../../components/BentoGrid/index";
import styles from "./GraphicDesign.module.scss";

// Define interfaces for the project data structure
interface AppProject {
  id: string;
  Title?: string;
  Slug?: string;
  Description?: string;
  Thumbnail?: {
    url?: string;
  };
}

export default function AppDevelopment() {
  const [projects, setProjects] = useState<AppProject[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // We need useNavigate to programmatically navigate on button click
  const navigate = useNavigate();

  useEffect(() => {
    const fetchProjects = async () => {
      try {
        const response = await fetch("http://localhost:1337/api/app-development-projects?populate=*");
        if (!response.ok) {
          throw new Error(`HTTP error! Status: ${response.status}`);
        }
        const data = await response.json();
        console.log("Fetched projects:", data);

        if (data && Array.isArray(data.data)) {
          setProjects(data.data);
        } else {
          setProjects([]);
        }
      } catch (err) {
        console.error("Error fetching projects:", err);
        setError("Failed to load projects. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, []);

  if (loading) return <p className={styles.loading}>Loading projects...</p>;
  if (error) return <p className={styles.error}>{error}</p>;
  if (projects.length === 0) return <p className={styles.noProjects}>No projects available.</p>;

  return (
    <div className={styles.container}>
      {/* Hero Section */}
      <section className={styles.hero}>
        <h1 className={styles.title}>APP DEVELOPMENT</h1>
        <p className={styles.description}>
          We pride ourselves on transforming your ideas into impactful digital narratives.
          Our work is our best advocate, from crafting dynamic branding to compelling marketing
          strategies. Discover our client work to see how we’ve helped businesses across the UK.
        </p>

        {/* Categories */}
        <div className={styles.categories}>
          <button onClick={() => navigate("/portfolio/web-development")}>
            Web Development
          </button>
          <button onClick={() => navigate("/portfolio/graphic-design")}>
            Graphic Design
          </button>
          <button
            className={styles.activeCategory}
            onClick={() => navigate("/portfolio/app-development")}
          >
            App Development
          </button>
        </div>
      </section>

      {/* Bento Grid */}
      <section className={styles.gridSection}>
        <BentoGrid className={styles.grid}>
          {projects.map((project, index) => {
            const { id, Title, Slug, Description, Thumbnail } = project || {};

            // Extract first sentence for preview and limit length
            let previewContent = "No description available.";
            if (typeof Description === "string") {
              previewContent = Description.split(". ").slice(0, 1).join(". ") + ".";
            }
            if (previewContent.length > 80) {
              previewContent = previewContent.substring(0, 80) + "...";
            }

            // Get cover image URL safely
            const imageUrl = Thumbnail?.url
              ? `http://localhost:1337${Thumbnail.url}`
              : null;

            // Example: alternate between .spanTwo and .spanOne
            const spanClass = index % 2 === 0 ? styles.spanTwo : styles.spanOne;

            return (
              <div key={id} className={spanClass}>
                <Link to={`/portfolio/app-development-projects/${Slug}`} className={styles.link}>
                  <BentoGridItem
                    className={styles.gridItem}
                    title={Title || "Untitled Project"}
                    description=""
                    header={
                      imageUrl ? (
                        <img src={imageUrl} alt={Title || "Project"} className={styles.image} />
                      ) : (
                        <div className={styles.placeholder}>No Image Available</div>
                      )
                    }
                  />
                </Link>
              </div>
            );
          })}
        </BentoGrid>
      </section>
    </div>
  );
}
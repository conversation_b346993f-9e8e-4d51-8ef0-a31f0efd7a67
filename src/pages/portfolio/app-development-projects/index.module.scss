.container {
  font-family: "Arial", sans-serif;
  font-size: 2rem;
  color: #fff;
  background-color: #182b2a;
  padding-bottom: 200px;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 100px;
  padding: 20px 10%;
}

.section1 {
  width: 100%;
  max-width: 100vw;
  background-color: #121b1b;
  color: white;
  padding: 200px 5vw; /* Responsive padding */
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 3rem;
}

.section {
  padding-top: 100px;
  display: flex;
  align-items: center;
  gap: 40px;
  max-width: 1000px;
  width: 100%;
  
  /* Odd sections: normal order, Even sections: reversed */
  &:nth-child(odd) {
    flex-direction: row;
  }
  &:nth-child(even) {
    flex-direction: row-reverse;
  }

  .text {
    flex: 1;
    h2 {
      font-size: 1.8rem;
      color: #b2e8c4;
      margin-bottom: 10px;
    }
    p {
      font-size: 1rem;
      color: #ddd;
      line-height: 1.6;
    }
  }

  /* Placeholder for missing images */
  .imagePlaceholder {
    flex: 1;
    height: 200px;
    background-color: #2c4037;
    border-radius: 8px;
  }

  /* Actual images */
  img {
    flex: 1;
    max-width: 400px;     /* Adjust as needed */
    width: 100%;
    border-radius: 8px;
    object-fit: cover;
  }
}
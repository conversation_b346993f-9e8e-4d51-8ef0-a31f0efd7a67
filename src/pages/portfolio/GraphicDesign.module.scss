/* src/pages/WebDevelopment.module.scss */

.container {
  padding-top: 100px;
  width: 100vw; /* Full viewport width */
  background-color: #0d1f1f;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

/* HERO SECTION */
.hero {
  padding-top: 200px;
  width: 100%;  /* Full width */
  max-width: 100vw;
  background-color: #121b1b;
  color: white;
  padding: 80px 5vw; /* Responsive padding */
}

/* Bento Grid Section */
.gridSection {
  width: 100%;
  background-color: #182b2a;
  padding: 80px 20vw;
  display: flex;
  justify-content: center;
}

/* Add grid styling to ensure consistent height */
.grid {
  width: 100%;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr;
  gap: 6px;
  grid-auto-rows: 22rem; /* Standardized height */

  @media (min-width: 768px) {
    grid-template-columns: repeat(3, 1fr);
  }
}
.loading,
.error,
.noServices {
  text-align: center;
  font-size: 1.2rem;
  color: white;
}

/* ✅ FIX FOR LARGER BENTO ITEMS */
.spanOne {
  grid-column: span 1;
}



/* Fix Category Buttons */
.categories {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 10px;
}

.categories button {
  background-color: #182b2a;
  border: 0.5px solid #23452e;
  padding: 0.75rem 1.5rem;
  color: white;
  font-weight: 600;
  cursor: pointer;
  font-size: 0.9rem;
  border-radius: 9999px;
}

.activeCategory {
  background: white;
  color: black;
}

.title {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.description {
  font-size: 1.1rem;
  max-width: 800px;
  margin: 0 auto;
  padding-bottom: 32px;
}

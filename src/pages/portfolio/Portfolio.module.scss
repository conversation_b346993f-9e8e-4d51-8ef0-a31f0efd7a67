.webDevelopment {
  background-color: #182b2a;
  min-height: 100vh;
}

/* HERO (solid black) */
.heroSection {
  padding: 4rem 2rem;
  background-color: #121b1b; // Example background
  display: flex;
  justify-content: center;
  align-items: center;

  .heroInner {
    padding-top: 120px;
    max-width: 1400px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
    margin: 0 auto;
    flex-wrap: wrap; // Ensures columns stack on smaller screens
  }

  .heroText {
    flex: 1 1 500px; 
    h1 {
      font-size: 2.5rem;
      margin-bottom: 1rem;
      color: #ffffff;
      font-weight: 600;
    }
    p {
      font-size: 1.2rem;
      margin-bottom: 2rem;
      max-width: 500px;
      color: #ffffff;
      line-height: 1.5;
    }
  }

  .heroImageWrapper {
    flex: 0 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;

    .heroImage {
      max-width: 400px;
      width: 100%;
      height: auto;
      border-radius: 20px; // Example styling
    }
  }
}

/* NAV MENU (pill style) */
.navMenu {
  margin-top: 2rem;

  ul {
      display: flex;
      flex-wrap: wrap;
      gap: 0.75rem;
      list-style: none;
      padding: 0;
      margin: 0;
  }

  li {
    margin: 0;

    a {
      display: inline-block;
      padding: 0.5rem 1.5rem;
      border: 0.5px solid #23452e;
      border-radius: 9999px;
      background-color: #182b2a;
      color: #fff;
      font-size: 0.9rem;
      font-weight: 400;
      text-decoration: none;
      transition:
        color 0.3s ease,
        background-color 0.3s ease,
        border-color 0.3s ease;
    }

    a:hover {
      border-color: #2fc39f;
    }

    a.active {
      border-color: #2fc39f;
      border-width: 2px;
      background-color: rgba(47, 195, 159, 0.1);
      color: #2fc39f;
      font-weight: 500;
    }
  }
}

/* Scrollbar for WebKit browsers */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #1a262600;
  border-radius: 10px;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #121b1b;
  border-radius: 10px;
  border: 0.5px solid #23452e;
  transition: background 0.3s ease;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #121b1b;
}

.contentSection {
  max-width: 1500px;
  margin: 0 auto;
  padding: 4rem 2rem;
  display: flex;
  flex-direction: column;
  gap: 6rem; // Increased from 8rem to create more vertical space between blocks
  scroll-behavior: smooth;
  position: relative;
}

.contentBlock {
  display: flex;
  gap: 5rem;
  margin: 2rem 0;
  align-items: center;

  &:nth-child(even) {
    flex-direction: row-reverse;
  }

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 2rem;
    margin: 4rem 0;
  }
}

.textContent {
  flex: 0 1 45%; // Changed from flex: 1 to give specific width
  transition: opacity 0.3s ease;
  max-width: 600px; // Added max-width to prevent text from stretching too wide

  h2 {
    font-size: 2.5rem;
    color: #f1f5f9;
    margin-bottom: 2rem; // Increased from 1.5rem
    font-weight: 700;
  }

  p {
    font-size: 1.1rem;
    color: #94a3b8;
    line-height: 1.8; // Increased from 1.6
  }
}

.imageContent {
  flex: 0 1 47%; // Changed from flex: 1 to give specific width
  display: flex;
  justify-content: center;
  align-items: center;
  transition: transform 0.3s ease;

  img {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
    border: 0.5px solid #23452e;
  }
}

/* Grid section for portfolio items */
.gridSection {
  width: 100%;
  background-color: #182b2a;
  padding: 80px 20px;
  display: flex;
  justify-content: center;
}

.grid {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.gridItem {
  border-radius: 10px;
  overflow: hidden;
  border: 0.5px solid #23452e;
  transition: transform 0.3s ease;
  
  &:hover {
    transform: scale(1.03);
  }
}

.image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.placeholder {
  width: 100%;
  height: 200px;
  background-color: #1a2626;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #94a3b8;
}

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Email Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      max-width: 600px;
      margin: 0 auto;
      padding: 20px;
      line-height: 1.6;
    }
    h1 {
      color: #333;
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input[type="email"] {
      width: 100%;
      padding: 8px;
      box-sizing: border-box;
      border: 1px solid #ddd;
      border-radius: 4px;
    }
    button {
      background-color: #3fa98e;
      color: white;
      border: none;
      padding: 10px 15px;
      border-radius: 4px;
      cursor: pointer;
    }
    button:hover {
      background-color: #2d8a7f;
    }
    .result {
      margin-top: 20px;
      padding: 15px;
      border-radius: 4px;
    }
    .success {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    .error {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    .hidden {
      display: none;
    }
  </style>
</head>
<body>
  <h1>Veltrix Email Test</h1>
  <p>Use this form to test email delivery using your Zoho Mail SMTP settings.</p>
  
  <div class="form-group">
    <label for="email">Email Address:</label>
    <input type="email" id="email" placeholder="Enter email address">
  </div>
  
  <button id="sendButton">Send Test Email</button>
  
  <div id="result" class="result hidden"></div>
  
  <script>
    document.getElementById('sendButton').addEventListener('click', async function() {
      const email = document.getElementById('email').value.trim();
      const resultEl = document.getElementById('result');
      
      if (!email) {
        resultEl.className = 'result error';
        resultEl.textContent = 'Please enter an email address';
        resultEl.classList.remove('hidden');
        return;
      }
      
      // Show loading state
      this.textContent = 'Sending...';
      this.disabled = true;
      resultEl.classList.add('hidden');
      
      try {
        const response = await fetch('/api/email-test', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ email })
        });
        
        const data = await response.json();
        
        if (response.ok) {
          resultEl.className = 'result success';
          resultEl.textContent = 'Email sent successfully! Check your inbox.';
        } else {
          resultEl.className = 'result error';
          resultEl.textContent = data.error || 'Failed to send email. Please try again.';
        }
      } catch (error) {
        resultEl.className = 'result error';
        resultEl.textContent = 'An error occurred: ' + error.message;
      } finally {
        this.textContent = 'Send Test Email';
        this.disabled = false;
        resultEl.classList.remove('hidden');
      }
    });
  </script>
</body>
</html>
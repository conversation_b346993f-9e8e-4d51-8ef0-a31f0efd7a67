{"kind": "collectionType", "collectionName": "projects", "info": {"singularName": "project", "pluralName": "projects", "displayName": "Project"}, "options": {"draftAndPublish": true}, "attributes": {"Title": {"type": "string"}, "slug": {"type": "uid"}, "Description": {"type": "richtext"}, "Thumbnail": {"allowedTypes": ["images", "files", "videos", "audios"], "type": "media", "multiple": false}, "Images": {"allowedTypes": ["images", "files", "videos", "audios"], "type": "media", "multiple": true}}}
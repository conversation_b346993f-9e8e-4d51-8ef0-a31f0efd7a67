{"kind": "collectionType", "collectionName": "blogs", "info": {"singularName": "blog", "pluralName": "blogs", "displayName": "Blog", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {"i18n": {"localized": true}}, "attributes": {"Title": {"type": "string"}, "Slug": {"type": "uid", "targetField": "Title"}, "Content": {"type": "blocks"}, "CoverImage": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "DatePublished": {"type": "datetime"}, "HeroImage": {"allowedTypes": ["images", "files", "videos", "audios"], "type": "media", "multiple": false}}}
import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::blog.blog', ({ strapi }) => ({
  async find(ctx) {
    const { data, meta } = await super.find(ctx); // Call the default Strapi find method

    // ✅ Wrap each blog post inside an `attributes` field
    const modifiedData = data.map(post => ({
      id: post.id,
      attributes: { ...post }
    }));

    return { data: modifiedData, meta };
  }
}));
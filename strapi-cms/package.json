{"name": "strapi-cms", "version": "0.1.0", "private": true, "description": "A Strapi application", "scripts": {"build": "strapi build", "deploy": "strapi deploy", "develop": "strapi develop", "start": "strapi start", "strapi": "strapi"}, "dependencies": {"@strapi/plugin-cloud": "5.10.0", "@strapi/plugin-users-permissions": "5.10.0", "@strapi/provider-email-nodemailer": "^5.13.0", "@strapi/strapi": "5.10.0", "better-sqlite3": "11.3.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^6.0.0", "styled-components": "^6.0.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "typescript": "^5"}, "engines": {"node": ">=18.0.0 <=22.x.x", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************"}}
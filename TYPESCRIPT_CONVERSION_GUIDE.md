# TypeScript Conversion Guide for Auctus Project

## Step 1: Initial Setup (Completed)

You have now set up the TypeScript environment for your project with:

- TypeScript compiler and necessary type definitions installed
- TypeScript configuration files (`tsconfig.json` and `tsconfig.node.json`)
- Basic type definitions in `/src/types/index.ts`
- Vite configuration updated to support TypeScript

## Step 2: Convert Files from JSX to TSX

You have two options for this:

### Option 1: Automatic Conversion (Faster but requires manual fixing)

Run the conversion script:

```bash
node scripts/convertJsxToTsx.js
```

This will create `.tsx` versions of all your `.jsx` files. The script:
- Creates a copy of each `.jsx` file as `.tsx`
- Adds React import if missing
- Does NOT delete the original `.jsx` files (for safety)

### Option 2: Manual Conversion (Recommended for better type safety)

Manually convert files one by one, starting with:

1. Shared components (Footer, Header, etc.)
2. Common utilities and hooks
3. Page components
4. Main App component

## Step 3: Add Type Annotations

For each converted file, you'll need to add proper type annotations:

### For Props

```typescript
// Before
function Button({ text, onClick, variant = 'primary' }) {
  // ...
}

// After
interface ButtonProps {
  text: string;
  onClick: () => void;
  variant?: 'primary' | 'secondary';
}

function Button({ text, onClick, variant = 'primary' }: ButtonProps): JSX.Element {
  // ...
}
```

### For State

```typescript
// Before
const [users, setUsers] = useState([]);

// After
const [users, setUsers] = useState<User[]>([]);
```

### For Event Handlers

```typescript
// Before
function handleClick(e) {
  // ...
}

// After
function handleClick(e: React.MouseEvent<HTMLButtonElement>): void {
  // ...
}
```

## Step 4: Test & Fix Type Errors

Run the TypeScript compiler to check for errors:

```bash
npm run typecheck
```

Fix errors one by one, starting with:

1. Missing type annotations
2. Incompatible types
3. Incorrect prop types
4. Null/undefined handling

## Step 5: Clean Up

Once everything is working:

1. Update imports to use `.tsx` files
2. Remove original `.jsx` files
3. Update any build scripts or configurations

## Tips for Common TypeScript Issues

### Handling Dynamic Content

```typescript
// If you're not sure about a type
const dynamicContent: any = fetchUnknownData();

// Better approach with type assertion
const dynamicContent = fetchUnknownData() as SomeType;
```

### External Libraries Without Types

If a library doesn't have TypeScript definitions, create them in a `src/types/declarations.d.ts` file:

```typescript
declare module 'untyped-library-name' {
  export function someFunction(param: string): number;
  export const someValue: string;
  // ...
}
```

### Strict Null Checks

When using optional properties:

```typescript
// Using optional chaining
const username = user?.profile?.username;

// Type guards
if (user && user.profile) {
  // Now TypeScript knows these aren't null
  const username = user.profile.username;
}
```

## Next Steps

After completing the TypeScript conversion:

1. Enable stricter type checking by setting `"noImplicitAny": true` in `tsconfig.json`
2. Consider adding automated tests to ensure type safety
3. Document your types for other developers

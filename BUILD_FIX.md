# Build Error Fix: Handling External Scripts

## The Problem

We encountered a build error with the aat.js script:

```
error during build:
[vite]: Rollup failed to resolve import "/aat/dist/aat.js" from "/Users/<USER>/Development/Claude/Auctus/Auctus-Site/index.html".
```

This happens because Vite tries to bundle the aat.js script, but can't find it in the project's source files since it's likely an external script or in a location not accessible to Vite's bundling process.

## The Solution

We've implemented two solutions:

### Solution 1: Mark the Script as External

In `vite.config.ts`, we've added the script to the `external` option in the Rollup configuration:

```typescript
build: {
  rollupOptions: {
    external: ['/aat/dist/aat.js'],
    // rest of the config...
  }
}
```

This tells Vite not to try to bundle this script and treat it as an external resource.

### Solution 2: Change Script Loading Method

In `index.html`, we've changed the script tag from:

```html
<script type="module" src="/aat/dist/aat.js"></script>
```

To:

```html
<script defer src="/aat/dist/aat.js"></script>
```

This tells the browser to load the script as a regular script (not an ES module), which prevents Vite from trying to process it during the build.

### Solution 3: Copy Assets Script

We've also created a utility script to copy the aat.js file to the output directory during the build process:

```bash
npm run build:full
```

This command:
1. Runs the TypeScript compiler
2. Builds the project with Vite
3. Copies any necessary assets from the public directory to the dist directory

## How to Build Your Project

Use one of these commands:

1. **Regular build (Solution 1 & 2):**
   ```bash
   npm run build
   ```

2. **Full build with asset copying (Solution 3):**
   ```bash
   npm run build:full
   ```

## Verifying the Build

After building, check that:

1. The build completes without errors
2. The aat.js script is correctly referenced in the output HTML
3. The website functions as expected when viewed with npm run preview

## Preventing Similar Issues

When adding external scripts:

1. Use `defer` or `async` attributes rather than `type="module"` if they're not ES modules
2. Add them to the `external` list in Vite config
3. Ensure they're properly copied to the output directory during build

## Additional Recommendations

For third-party scripts like analytics and tracking:

1. Load them after the main application code
2. Consider using dynamic loading for non-critical scripts
3. Add appropriate `preconnect` and `dns-prefetch` tags for domains hosting these scripts

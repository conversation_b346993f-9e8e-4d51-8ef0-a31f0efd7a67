# Auctus Performance Optimization Guide

This guide explains the performance optimizations we've implemented for your Auctus site and provides instructions on how to maintain and further improve performance.

## Implemented Optimizations

### 1. Text Compression

We've added Vite's compression plugin to automatically generate compressed (gzip) versions of your assets. This addresses the "Enable text compression" warning which had a potential savings of 7,502 KiB.

### 2. JavaScript Optimization

We've optimized JavaScript in several ways:

- **Code Splitting**: The build configuration now intelligently splits your code into vendor and application chunks.
- **Minification**: Terser is used for better minification of JavaScript code.
- **Tree Shaking**: Unused code is eliminated during the build process.
- **Console Log Removal**: Console logs are removed in production builds.

### 3. HTML Optimization

We've updated your `index.html` to include:

- **Preconnect Hints**: For third-party domains like Google Fonts.
- **Preload Critical Resources**: Main scripts and stylesheets are preloaded.
- **Inline Critical CSS**: Basic styling is inlined to avoid render-blocking.
- **Deferred Scripts**: Non-critical scripts use the `defer` attribute.

### 4. Image Optimization

We've created a script to optimize your images (`scripts/optimize-images.js`), which:

- Creates properly sized responsive versions of images
- Compresses images without significant quality loss
- Generates WebP versions for better compression
- Creates documentation of image dimensions to help with proper HTML implementation

## How to Use These Optimizations

### Building for Production

```bash
# Regular production build
npm run build

# Build with bundle analysis
npm run build:analyze
```

### Optimizing Images

```bash
# Optimize all images in src/assets
npm run optimize-images

# Optimize images in a specific directory
npm run optimize-images src/assets/images
```

### Testing Performance

```bash
# Run a production preview server
npm run preview:prod

# Run Lighthouse tests (requires Lighthouse CLI)
npm run lighthouse
```

### Bundle Analysis

```bash
# Analyze bundle size
npm run analyze
```

## Guidelines for Maintaining Performance

### Images

1. **Always specify width and height**: This prevents layout shifts during page load
   ```jsx
   <img src="/path/to/image.jpg" width="800" height="600" alt="Description" />
   ```

2. **Use responsive images**: For large hero or feature images
   ```jsx
   <img 
     src="/images/hero-small.jpg"
     srcSet="/images/hero-small.jpg 640w, /images/hero-medium.jpg 1024w, /images/hero-large.jpg 1600w"
     sizes="(max-width: 640px) 640px, (max-width: 1024px) 1024px, 1600px"
     width="1600"
     height="900"
     alt="Hero image"
   />
   ```

3. **Lazy load non-critical images**: Use the loading attribute
   ```jsx
   <img src="/path/to/image.jpg" loading="lazy" alt="Description" />
   ```

### JavaScript

1. **Use code splitting with React.lazy for large components**
   ```jsx
   import React, { lazy, Suspense } from 'react';
   
   // Instead of: import HeavyComponent from './HeavyComponent';
   const HeavyComponent = lazy(() => import('./HeavyComponent'));
   
   function MyComponent() {
     return (
       <Suspense fallback={<div>Loading...</div>}>
         <HeavyComponent />
       </Suspense>
     );
   }
   ```

2. **Avoid large third-party libraries** when smaller alternatives exist

3. **Implement proper memoization** for expensive calculations
   ```jsx
   import { useMemo } from 'react';
   
   function MyComponent({ data }) {
     const processedData = useMemo(() => {
       // Expensive calculation here
       return data.map(item => expensiveOperation(item));
     }, [data]);
     
     return <div>{processedData}</div>;
   }
   ```

### CSS

1. **Prioritize critical CSS**: Put essential styles inline in the head

2. **Avoid large CSS frameworks**: Only import what you need
   ```javascript
   // Instead of importing the entire library
   import 'huge-library/dist/huge-library.css';
   
   // Import only what you need
   import 'huge-library/dist/components/button.css';
   import 'huge-library/dist/components/card.css';
   ```

3. **Use CSS-in-JS with caution**: It can increase bundle size and runtime cost

### Third-party Resources

1. **Limit third-party scripts**: Each external script adds loading and execution time

2. **Load third-party scripts with defer or async when possible**
   ```html
   <script src="https://third-party.com/script.js" defer></script>
   ```

3. **Self-host critical third-party resources** when possible instead of loading from external CDNs

## Advanced Performance Techniques

For further performance improvements, consider:

1. **Implementing a Service Worker**: For caching assets and offline support

2. **Using Intersection Observer API**: For better lazy loading
   ```jsx
   import { useInView } from 'react-intersection-observer';
   
   function LazyComponent() {
     const { ref, inView } = useInView({
       triggerOnce: true,
       threshold: 0.1,
     });
     
     return (
       <div ref={ref}>
         {inView ? <ExpensiveComponent /> : <Placeholder />}
       </div>
     );
   }
   ```

3. **Implementing proper cache headers**: Work with your hosting provider to set up optimal cache headers

4. **Using a Content Delivery Network (CDN)**: For faster global asset delivery

## Monitoring Performance

It's important to regularly check your site's performance:

1. Run Lighthouse tests periodically
2. Monitor Core Web Vitals in Google Search Console
3. Use real user monitoring (RUM) tools

## Need More Help?

If you need further assistance with performance optimization:
- Check Vite's performance documentation: https://vitejs.dev/guide/performance.html
- Review React's performance tips: https://react.dev/learn/render-and-commit
- Consider tools like New Relic or Datadog for ongoing performance monitoring

# TypeScript Cleanup Guide

Now that you've successfully converted your project to TypeScript, follow these steps to clean up the remaining JSX files and complete the migration.

## Step 1: Find JSX Files to Remove

Run the cleanup script to identify JSX files that have corresponding TSX versions:

```bash
npm run cleanup
```

This will:
- Scan your project for JSX files
- Identify which ones have corresponding TSX files
- Generate a shell script to remove the redundant JSX files
- List any JSX files that don't have corresponding TSX files (which might still need conversion)

## Step 2: Remove Redundant JSX Files

After running the cleanup script, you'll see a new file: `scripts/remove-jsx-files.sh`

Run this script to remove all the identified JSX files:

```bash
./scripts/remove-jsx-files.sh
```

## Step 3: Check for Lingering JSX Imports

Sometimes, even after converting all files, you might still have imports that reference `.jsx` files. Run:

```bash
node scripts/check-jsx-imports.js
```

This will identify any TypeScript files that still import JSX files. For each issue found:

1. Update the import to remove the file extension:

```typescript
// Change this:
import Component from './Component.jsx';

// To this:
import Component from './Component';
```

## Step 4: Convert Remaining JavaScript Utilities

If you have utility files (.js) that haven't been converted to TypeScript:

1. Create a .ts version of each file
2. Add appropriate type annotations
3. Update imports to reference the .ts file
4. Delete the original .js file once confirmed working

## Step 5: Update Project Configuration

1. Update your `tsconfig.json` to enable stricter type checking if desired:

```json
{
  "compilerOptions": {
    "noImplicitAny": true,
    "strictNullChecks": true
  }
}
```

2. Make sure your `index.html` doesn't reference any old JavaScript files directly

## Step 6: Update Import Aliases

If you're still encountering path resolution issues:

1. Make sure your path aliases in `tsconfig.json` match those in `vite.config.ts`
2. Consider organizing your imports consistently

Example alias organization:
```typescript
// External libraries first
import React from 'react';
import { useMediaQuery } from 'react-responsive';

// Aliased imports
import Button from '@components/Button';
import Footer from '@components/Footer';

// Relative imports
import { validateEmail } from '../../utils/validation';
```

## Step 7: Clean up PropTypes

Now that you're using TypeScript interfaces, you can remove PropTypes declarations:

```typescript
// Remove this:
Component.propTypes = {
  name: PropTypes.string.isRequired,
  count: PropTypes.number
};

// Keep your TypeScript interface:
interface ComponentProps {
  name: string;
  count?: number;
}
```

## Step 8: Final Verification

Run these commands to make sure everything is working:

```bash
# Type checking
npm run typecheck

# Build verification
npm run build

# Development server
npm run dev
```

## Congratulations!

Your project is now fully converted to TypeScript! This provides several benefits:

- Better editor support with autocompletion
- Catch errors during development rather than runtime
- Easier refactoring and maintenance
- Better documentation through type definitions
- Improved developer experience

## Post-Migration Tips

1. **Gradually enhance types**: Start with more permissive types (`any`, optional properties) and gradually make them stricter as you refine the codebase.

2. **Add JSDoc comments**: Enhance your interfaces and types with JSDoc comments for even better IDE hints:

```typescript
/**
 * Configuration options for the application
 */
interface AppConfig {
  /**
   * The API base URL
   * @example "https://api.example.com/v1"
   */
  apiUrl: string;
  
  /**
   * Maximum number of retries for failed requests
   * @default 3
   */
  maxRetries?: number;
}
```

3. **Use type guards**: For complex type scenarios, add type guards to make TypeScript smarter:

```typescript
function isApiError(error: unknown): error is ApiError {
  return (
    typeof error === 'object' && 
    error !== null && 
    'statusCode' in error && 
    'message' in error
  );
}

try {
  // Some API call
} catch (error) {
  if (isApiError(error)) {
    // TypeScript now knows error has statusCode and message properties
    console.error(`API Error ${error.statusCode}: ${error.message}`);
  } else {
    console.error('Unknown error:', error);
  }
}
```

4. **Consider adding tests**: TypeScript migration is a great time to also add unit tests if you don't have them already.

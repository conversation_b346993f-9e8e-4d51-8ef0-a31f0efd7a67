import { useState, useRef } from "react";
import { motion, useScroll, useTransform } from "framer-motion";
import img1 from './assets/img1.png';
import img2 from './assets/img2.png';
import img3 from './assets/img3.png';
import img4 from './assets/img4.png';

const cards = [
  { id: "c1", title: "Winter", description: "Winter has so much to offer", backgroundImage: img1 },
  { id: "c2", title: "Digital Technology", description: "Gets better every day", backgroundImage: img2 },
  { id: "c3", title: "Globalization", description: "Help people all over the world", backgroundImage: img3 },
  { id: "c4", title: "New Technologies", description: "Space engineering advances", backgroundImage: img4 },
];

const AnimatedCards = () => {
  const [selectedCard, setSelectedCard] = useState("c1");
  const ref = useRef(null);
  
  const { scrollYProgress } = useScroll({ target: ref });
  
  const titleX = useTransform(scrollYProgress, [0, 0.3, 0.7, 1], [-400, 0, 0, 400]);
  const titleOpacity = useTransform(scrollYProgress, [0, 0.3, 0.7, 1], [0, 1, 1, 0]);
  
  const cardsX = useTransform(scrollYProgress, [0.2, 0.5, 0.8, 1], [400, 0, 0, -400]);
  const cardsOpacity = useTransform(scrollYProgress, [0.2, 0.5, 0.8, 1], [0, 1, 1, 0]);

  return (
    <div ref={ref} className="bg-[#121B1B] min-h-[200vh] flex flex-col items-center justify-center">
      <motion.h2
        className="text-white text-7xl my-60"
        style={{ x: titleX, opacity: titleOpacity }}
      >
        RECENT PROJECTS
      </motion.h2>

      <motion.div
        className="flex items-center justify-center min-h-[80vh] md:min-h-[60vh] bg-[#121B1B] overflow-x-auto"
        style={{ x: cardsX, opacity: cardsOpacity }}
      >
        <div className="flex space-x-4 md:space-x-6 px-4 flex-nowrap">
          {cards.map((card) => (
            <motion.div
              key={card.id}
              className={`relative cursor-pointer flex-shrink-0 transition-all duration-500 ease-in-out 
                ${selectedCard === card.id ? "w-[75vw] md:w-[900px] h-[30vh] md:h-[60vh]" : "w-[40vw] md:w-[100px] h-[40vh] md:h-[60vh]"} rounded-3xl overflow-hidden shadow-lg`}
              onClick={() => setSelectedCard(card.id)}
            >
              <div
                className={`absolute inset-0 bg-cover bg-center transition-all duration-500 ease-in-out
                  ${selectedCard !== card.id ? "filter blur-sm" : ""}`}
                style={{ backgroundImage: `url(${card.backgroundImage})` }}
              ></div>
              <div className="absolute z-20 top-4 left-4 w-10 h-10 bg-gray-800 text-white rounded-full flex items-center justify-center">
                {card.id.charAt(1)}
              </div>
              <div className={`absolute bottom-0 z-10 w-full p-4 bg-black bg-opacity-70 backdrop-blur-md transition-opacity duration-300
                ${selectedCard === card.id ? "opacity-100" : "opacity-0"}`}>
                <div className="text-white">
                  <h4 className="uppercase font-bold">{card.title}</h4>
                  <p className="text-gray-300">{card.description}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default AnimatedCards;
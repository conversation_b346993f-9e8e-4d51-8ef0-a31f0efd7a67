# CRM Integration for Quote Quiz

## Overview

The Quote Quiz component has been updated to integrate with your CRM system. Instead of sending emails, it now creates quote records directly in your CRM.

## Setup

1. Copy `.env.example` to `.env`
2. Update the environment variables with your CRM credentials
3. Restart your development server

## Environment Variables

```
VITE_CRM_API_URL=http://192.168.0.11:3000/rest
VITE_CRM_API_TOKEN=your-api-token-here
```

**Note**: In Vite, environment variables must be prefixed with `VITE_` to be accessible in the client-side code.

## How It Works

1. User completes the 8-step quote quiz
2. Data is formatted to match CRM requirements
3. Quote is created via POST request to CRM API
4. Success/error message is displayed to user

## Data Mapping

| Quiz Field | CRM Field |
|------------|-----------|
| Contact Name | `clientName.firstName`, `clientName.lastName` |
| Phone Number | `contactNumber.primaryPhoneNumber` |
| Email | Stored in `context` |
| Requirements | Stored in `context` |
| All Answers | JSON in `context` field |

## File Structure

```
src/
├── components/
│   └── QuoteQuiz/
│       └── index.jsx (updated with CRM integration)
└── config/
    └── crm.js (CRM configuration and helpers)
```

## Testing

1. Complete the quiz with test data
2. Check browser console for logs
3. Verify quote appears in CRM

## Troubleshooting

### White Screen Error
This was caused by using `process.env` instead of `import.meta.env` in Vite.

### 401 Unauthorized
Check your API token in the .env file.

### CORS Issues
Ensure your CRM server allows requests from your development domain.

## Reverting to Email

If you need to revert to the email-based system:
1. Copy `index_backup.jsx` to `index.jsx` in the QuoteQuiz folder
2. Update the submission logic as needed

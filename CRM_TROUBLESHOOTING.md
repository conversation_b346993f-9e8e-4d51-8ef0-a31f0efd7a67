# CRM Integration Troubleshooting Guide

## Common Issues and Solutions

### 1. "invalid input value for enum" Error

**Error**: `invalid input value for enum workspace_xxx."_quote_createdBySource_enum": "WEBSITE"`

**Solution**: 
- Check allowed values for the enum field in your CRM
- Update `CRM_CONFIG.QUOTE_DEFAULTS.source` to use an allowed value
- Common valid values: "FORM", "API", "EMAIL", "IMPORT"

### 2. Type Mismatch Errors

**Issue**: CRM expecting different data types

**Solutions**:
- **Budget**: Ensure it's a number, not a string
- **hasExistingWebsite**: Ensure it's a boolean, not "Yes"/"No"
- **Dates**: Use ISO string format (e.g., `new Date().toISOString()`)

### 3. Missing Required Fields

**Error**: "Field X is required"

**Solution**: 
- Check CRM schema for required fields
- Update `createQuotePayload` function to include all required fields
- Add default values for fields that might be empty

### 4. CORS Errors

**Error**: "Access to fetch at 'http://************:3000/rest/quotes' from origin..."

**Solutions**:
- Configure CORS on your CRM server to allow your domain
- For development, use a proxy in your Vite config
- Consider using a backend service as a proxy

### 5. Authentication Errors

**Error**: 401 Unauthorized

**Solutions**:
- Check your API token is correct in .env file
- Ensure the token hasn't expired
- Verify the Authorization header format is correct

## Testing the Integration

1. **Console Debugging**:
   ```javascript
   console.log("Quote data:", crmQuoteData);
   ```

2. **Test with Minimal Data**:
   ```javascript
   const testPayload = {
     name: "Test Quote",
     createdBy: { source: "FORM" },
     // Add only required fields
   };
   ```

3. **Check Field Mappings**:
   - Verify each quiz answer maps to the correct CRM field
   - Ensure data transformations are working correctly

## CRM Configuration Checklist

- [ ] All enum fields have the correct allowed values
- [ ] Number fields are set to accept numbers
- [ ] Boolean fields are set to accept true/false
- [ ] Date fields are set to accept ISO date strings
- [ ] All required fields are marked as such in the CRM

## Debugging Steps

1. Check browser console for errors
2. Inspect the network tab for the API request/response
3. Verify the payload structure matches CRM expectations
4. Test with hardcoded values to isolate the issue
5. Check CRM logs for more detailed error messages

## Contact

If issues persist, check with your CRM administrator about:
- Field types and constraints
- Allowed enum values
- Required fields
- API permissions

# WebP Image Optimization Guide for Auctus

## The Problem: Large Images

Your site's performance score is being affected by large image files, some exceeding 4MB in size. Even though we've optimized and replaced these images, we can do better by using WebP format, which typically reduces image size by 25-35% compared to JPEG/PNG while maintaining similar quality.

## Solution: WebP + Original Format (Best of Both Worlds)

I've enhanced the image optimization script to give you the best of both worlds:

```bash
npm run optimize-images:both
```

This command:
1. Replaces your original images with optimized versions (same format)
2. Creates WebP versions with the same filename but .webp extension
3. Keeps your current code working without changes
4. Enables you to optionally use WebP for better performance

## Using WebP in Your Components

### Option 1: Simple OptimizedImage Component (Recommended)

I've created a reusable `OptimizedImage` component that automatically checks for WebP versions:

```jsx
// Import the component in your file
import OptimizedImage from '@components/OptimizedImage';
import backgroundImage from '@assets/Background.png';

function MyComponent() {
  return (
    <OptimizedImage 
      src={backgroundImage} 
      alt="Background" 
      width={1200} 
      height={800} 
    />
  );
}
```

This component will:
- Automatically check if a WebP version exists (same path but .webp extension)
- Use the WebP in browsers that support it
- Fall back to your original format in browsers that don't support WebP
- Require no changes to your image imports

### Option 2: Manual Picture Element

For more control, you can explicitly import both formats:

```jsx
import backgroundImage from '@assets/Background.png';
import backgroundImageWebP from '@assets/Background.webp';

function MyComponent() {
  return (
    <picture>
      <source srcSet={backgroundImageWebP} type="image/webp" />
      <img 
        src={backgroundImage} 
        alt="Background" 
        width={1200} 
        height={800} 
      />
    </picture>
  );
}
```

## Step-by-Step Implementation

1. **Optimize your images:**
   ```bash
   npm run optimize-images:both
   ```

2. **Add the OptimizedImage component to your project:**
   This component has been added at: `/src/components/OptimizedImage.tsx`

3. **Update your imports (gradually):**
   Replace `<img>` tags with `<OptimizedImage>` components as you work on different parts of your site.

   ```jsx
   // Before
   <img src={myImage} alt="Description" />
   
   // After
   <OptimizedImage src={myImage} alt="Description" width={800} height={600} />
   ```

4. **Always include width and height attributes:**
   This prevents layout shifts during page load.

## Benefits

- **Better Performance:** WebP images are typically 25-35% smaller than JPEG/PNG
- **No Breaking Changes:** Your existing code continues to work
- **Progressive Enhancement:** Browsers that support WebP get better performance
- **SEO Benefit:** Google considers page load time in ranking, and WebP is preferred

## Testing

After implementing WebP optimization:

1. Run a build:
   ```bash
   npm run build
   ```

2. Check the build output to see the size reduction
3. Run Lighthouse test again to see improved performance scores:
   ```bash
   npm run preview:prod
   npm run lighthouse
   ```

The "Properly size images" warning should be resolved, and your overall performance score should improve significantly.

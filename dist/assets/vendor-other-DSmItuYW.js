function e(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var t={exports:{}},r={};
/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
!function(e){function t(e,t){var r=e.length;e.push(t);e:for(;0<r;){var n=r-1>>>1,a=e[n];if(!(0<o(a,t)))break e;e[n]=t,e[r]=a,r=n}}function r(e){return 0===e.length?null:e[0]}function n(e){if(0===e.length)return null;var t=e[0],r=e.pop();if(r!==t){e[0]=r;e:for(var n=0,a=e.length,i=a>>>1;n<i;){var s=2*(n+1)-1,l=e[s],c=s+1,u=e[c];if(0>o(l,r))c<a&&0>o(u,l)?(e[n]=u,e[c]=r,n=c):(e[n]=l,e[s]=r,n=s);else{if(!(c<a&&0>o(u,r)))break e;e[n]=u,e[c]=r,n=c}}}return t}function o(e,t){var r=e.sortIndex-t.sortIndex;return 0!==r?r:e.id-t.id}if("object"==typeof performance&&"function"==typeof performance.now){var a=performance;e.unstable_now=function(){return a.now()}}else{var i=Date,s=i.now();e.unstable_now=function(){return i.now()-s}}var l=[],c=[],u=1,d=null,p=3,f=!1,h=!1,m=!1,b="function"==typeof setTimeout?setTimeout:null,g="function"==typeof clearTimeout?clearTimeout:null,v="undefined"!=typeof setImmediate?setImmediate:null;function y(e){for(var o=r(c);null!==o;){if(null===o.callback)n(c);else{if(!(o.startTime<=e))break;n(c),o.sortIndex=o.expirationTime,t(l,o)}o=r(c)}}function w(e){if(m=!1,y(e),!h)if(null!==r(l))h=!0,O(x);else{var t=r(c);null!==t&&S(w,t.startTime-e)}}function x(t,o){h=!1,m&&(m=!1,g(C),C=-1),f=!0;var a=p;try{for(y(o),d=r(l);null!==d&&(!(d.expirationTime>o)||t&&!z());){var i=d.callback;if("function"==typeof i){d.callback=null,p=d.priorityLevel;var s=i(d.expirationTime<=o);o=e.unstable_now(),"function"==typeof s?d.callback=s:d===r(l)&&n(l),y(o)}else n(l);d=r(l)}if(null!==d)var u=!0;else{var b=r(c);null!==b&&S(w,b.startTime-o),u=!1}return u}finally{d=null,p=a,f=!1}}"undefined"!=typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var k,P=!1,E=null,C=-1,L=5,T=-1;function z(){return!(e.unstable_now()-T<L)}function I(){if(null!==E){var t=e.unstable_now();T=t;var r=!0;try{r=E(!0,t)}finally{r?k():(P=!1,E=null)}}else P=!1}if("function"==typeof v)k=function(){v(I)};else if("undefined"!=typeof MessageChannel){var A=new MessageChannel,j=A.port2;A.port1.onmessage=I,k=function(){j.postMessage(null)}}else k=function(){b(I,0)};function O(e){E=e,P||(P=!0,k())}function S(t,r){C=b((function(){t(e.unstable_now())}),r)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(e){e.callback=null},e.unstable_continueExecution=function(){h||f||(h=!0,O(x))},e.unstable_forceFrameRate=function(e){0>e||125<e||(L=0<e?Math.floor(1e3/e):5)},e.unstable_getCurrentPriorityLevel=function(){return p},e.unstable_getFirstCallbackNode=function(){return r(l)},e.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var r=p;p=t;try{return e()}finally{p=r}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var r=p;p=e;try{return t()}finally{p=r}},e.unstable_scheduleCallback=function(n,o,a){var i=e.unstable_now();switch("object"==typeof a&&null!==a?a="number"==typeof(a=a.delay)&&0<a?i+a:i:a=i,n){case 1:var s=-1;break;case 2:s=250;break;case 5:s=1073741823;break;case 4:s=1e4;break;default:s=5e3}return n={id:u++,callback:o,priorityLevel:n,startTime:a,expirationTime:s=a+s,sortIndex:-1},a>i?(n.sortIndex=a,t(c,n),null===r(l)&&n===r(c)&&(m?(g(C),C=-1):m=!0,S(w,a-i))):(n.sortIndex=s,t(l,n),h||f||(h=!0,O(x))),n},e.unstable_shouldYield=z,e.unstable_wrapCallback=function(e){var t=p;return function(){var r=p;p=t;try{return e.apply(this,arguments)}finally{p=r}}}}(r),t.exports=r;var n=t.exports,o={match:function(e,t){return u(e).some((function(e){var r=e.inverse,n="all"===e.type||t.type===e.type;if(n&&r||!n&&!r)return!1;var o=e.expressions.every((function(e){var r=e.feature,n=e.modifier,o=e.value,a=t[r];if(!a)return!1;switch(r){case"orientation":case"scan":return a.toLowerCase()===o.toLowerCase();case"width":case"height":case"device-width":case"device-height":o=f(o),a=f(a);break;case"resolution":o=p(o),a=p(a);break;case"aspect-ratio":case"device-aspect-ratio":case"device-pixel-ratio":o=d(o),a=d(a);break;case"grid":case"color":case"color-index":case"monochrome":o=parseInt(o,10)||1,a=parseInt(a,10)||0}switch(n){case"min":return a>=o;case"max":return a<=o;default:return a===o}}));return o&&!r||!o&&r}))}};o.parse=u;var a=/(?:(only|not)?\s*([^\s\(\)]+)(?:\s*and)?\s*)?(.+)?/i,i=/\(\s*([^\s\:\)]+)\s*(?:\:\s*([^\s\)]+))?\s*\)/,s=/^(?:(min|max)-)?(.+)/,l=/(em|rem|px|cm|mm|in|pt|pc)?$/,c=/(dpi|dpcm|dppx)?$/;function u(e){return e.split(",").map((function(e){var t=(e=e.trim()).match(a),r=t[1],n=t[2],o=t[3]||"",l={};return l.inverse=!!r&&"not"===r.toLowerCase(),l.type=n?n.toLowerCase():"all",o=o.match(/\([^\)]+\)/g)||[],l.expressions=o.map((function(e){var t=e.match(i),r=t[1].toLowerCase().match(s);return{modifier:r[1],feature:r[2],value:t[2]}})),l}))}function d(e){var t,r=Number(e);return r||(r=(t=e.match(/^(\d+)\s*\/\s*(\d+)$/))[1]/t[2]),r}function p(e){var t=parseFloat(e);switch(String(e).match(c)[1]){case"dpcm":return t/2.54;case"dppx":return 96*t;default:return t}}function f(e){var t=parseFloat(e);switch(String(e).match(l)[1]){case"em":case"rem":return 16*t;case"cm":return 96*t/2.54;case"mm":return 96*t/2.54/10;case"in":return 96*t;case"pt":return 72*t;case"pc":return 72*t/12;default:return t}}var h=o.match,m="undefined"!=typeof window?window.matchMedia:null;function b(e,t,r){var n,o=this;function a(e){o.matches=e.matches,o.media=e.media}m&&!r&&(n=m.call(window,e)),n?(this.matches=n.matches,this.media=n.media,n.addListener(a)):(this.matches=h(e,t),this.media=e),this.addListener=function(e){n&&n.addListener(e)},this.removeListener=function(e){n&&n.removeListener(e)},this.dispose=function(){n&&n.removeListener(a)}}const g=e((function(e,t,r){return new b(e,t,r)}));var v=/[A-Z]/g,y=/^ms-/,w={};function x(e){return"-"+e.toLowerCase()}function k(e){if(w.hasOwnProperty(e))return w[e];var t=e.replace(v,x);return w[e]=y.test(t)?"-"+t:t}function P(e,t){if(e===t)return!0;if(!e||!t)return!1;const r=Object.keys(e),n=Object.keys(t),o=r.length;if(n.length!==o)return!1;for(let a=0;a<o;a++){const n=r[a];if(e[n]!==t[n]||!Object.prototype.hasOwnProperty.call(t,n))return!1}return!0}var E={exports:{}};function C(){}function L(){}L.resetWarningCache=C;E.exports=function(){function e(e,t,r,n,o,a){if("SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"!==a){var i=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw i.name="Invariant Violation",i}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:L,resetWarningCache:C};return r.PropTypes=r,r}();const T=e(E.exports);
/**
 * @remix-run/router v1.19.2
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function z(){return z=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},z.apply(this,arguments)}var I,A;(A=I||(I={})).Pop="POP",A.Push="PUSH",A.Replace="REPLACE";const j="popstate";function O(e){return void 0===e&&(e={}),function(e,t,r,n){void 0===n&&(n={});let{window:o=document.defaultView,v5Compat:a=!1}=n,i=o.history,s=I.Pop,l=null,c=u();null==c&&(c=0,i.replaceState(z({},i.state,{idx:c}),""));function u(){return(i.state||{idx:null}).idx}function d(){s=I.Pop;let e=u(),t=null==e?null:e-c;c=e,l&&l({action:s,location:m.location,delta:t})}function p(e,t){s=I.Push;let r=M(m.location,e,t);c=u()+1;let n=$(r,c),d=m.createHref(r);try{i.pushState(n,"",d)}catch(p){if(p instanceof DOMException&&"DataCloneError"===p.name)throw p;o.location.assign(d)}a&&l&&l({action:s,location:m.location,delta:1})}function f(e,t){s=I.Replace;let r=M(m.location,e,t);c=u();let n=$(r,c),o=m.createHref(r);i.replaceState(n,"",o),a&&l&&l({action:s,location:m.location,delta:0})}function h(e){let t="null"!==o.location.origin?o.location.origin:o.location.href,r="string"==typeof e?e:N(e);return r=r.replace(/ $/,"%20"),S(t,"No window.location.(origin|href) available to create URL for href: "+r),new URL(r,t)}let m={get action(){return s},get location(){return e(o,i)},listen(e){if(l)throw new Error("A history only accepts one active listener");return o.addEventListener(j,d),l=e,()=>{o.removeEventListener(j,d),l=null}},createHref:e=>t(o,e),createURL:h,encodeLocation(e){let t=h(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:p,replace:f,go:e=>i.go(e)};return m}((function(e,t){let{pathname:r,search:n,hash:o}=e.location;return M("",{pathname:r,search:n,hash:o},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"==typeof t?t:N(t)}),0,e)}function S(e,t){if(!1===e||null==e)throw new Error(t)}function _(e,t){if(!e)try{throw new Error(t)}catch(r){}}function $(e,t){return{usr:e.state,key:e.key,idx:t}}function M(e,t,r,n){return void 0===r&&(r=null),z({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?W(t):t,{state:r,key:t&&t.key||n||Math.random().toString(36).substr(2,8)})}function N(e){let{pathname:t="/",search:r="",hash:n=""}=e;return r&&"?"!==r&&(t+="?"===r.charAt(0)?r:"?"+r),n&&"#"!==n&&(t+="#"===n.charAt(0)?n:"#"+n),t}function W(e){let t={};if(e){let r=e.indexOf("#");r>=0&&(t.hash=e.substr(r),e=e.substr(0,r));let n=e.indexOf("?");n>=0&&(t.search=e.substr(n),e=e.substr(0,n)),e&&(t.pathname=e)}return t}var R,G;function B(e,t,r){return void 0===r&&(r="/"),function(e,t,r,n){let o="string"==typeof t?W(t):t,a=te(o.pathname||"/",r);if(null==a)return null;let i=U(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let r=e.length===t.length&&e.slice(0,-1).every(((e,r)=>e===t[r]));return r?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(i);let s=null;for(let l=0;null==s&&l<i.length;++l){let e=ee(a);s=X(i[l],e,n)}return s}(e,t,r,!1)}function U(e,t,r,n){void 0===t&&(t=[]),void 0===r&&(r=[]),void 0===n&&(n="");let o=(e,o,a)=>{let i={relativePath:void 0===a?e.path||"":a,caseSensitive:!0===e.caseSensitive,childrenIndex:o,route:e};i.relativePath.startsWith("/")&&(S(i.relativePath.startsWith(n),'Absolute route path "'+i.relativePath+'" nested under path "'+n+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),i.relativePath=i.relativePath.slice(n.length));let s=ae([n,i.relativePath]),l=r.concat(i);e.children&&e.children.length>0&&(S(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+s+'".'),U(e.children,t,l,s)),(null!=e.path||e.index)&&t.push({path:s,score:Q(s,e.index),routesMeta:l})};return e.forEach(((e,t)=>{var r;if(""!==e.path&&null!=(r=e.path)&&r.includes("?"))for(let n of F(e.path))o(e,t,n);else o(e,t)})),t}function F(e){let t=e.split("/");if(0===t.length)return[];let[r,...n]=t,o=r.endsWith("?"),a=r.replace(/\?$/,"");if(0===n.length)return o?[a,""]:[a];let i=F(n.join("/")),s=[];return s.push(...i.map((e=>""===e?a:[a,e].join("/")))),o&&s.push(...i),s.map((t=>e.startsWith("/")&&""===t?"/":t))}(G=R||(R={})).data="data",G.deferred="deferred",G.redirect="redirect",G.error="error";const q=/^:[\w-]+$/,D=3,H=2,J=1,V=10,Y=-2,K=e=>"*"===e;function Q(e,t){let r=e.split("/"),n=r.length;return r.some(K)&&(n+=Y),t&&(n+=H),r.filter((e=>!K(e))).reduce(((e,t)=>e+(q.test(t)?D:""===t?J:V)),n)}function X(e,t,r){let{routesMeta:n}=e,o={},a="/",i=[];for(let s=0;s<n.length;++s){let e=n[s],l=s===n.length-1,c="/"===a?t:t.slice(a.length)||"/",u=Z({path:e.relativePath,caseSensitive:e.caseSensitive,end:l},c),d=e.route;if(!u&&l&&r&&!n[n.length-1].route.index&&(u=Z({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},c)),!u)return null;Object.assign(o,u.params),i.push({params:o,pathname:ae([a,u.pathname]),pathnameBase:ie(ae([a,u.pathnameBase])),route:d}),"/"!==u.pathnameBase&&(a=ae([a,u.pathnameBase]))}return i}function Z(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[r,n]=function(e,t,r){void 0===t&&(t=!1);void 0===r&&(r=!0);_("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let n=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,r)=>(n.push({paramName:t,isOptional:null!=r}),r?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(n.push({paramName:"*"}),o+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):r?o+="\\/*$":""!==e&&"/"!==e&&(o+="(?:(?=\\/|$))");let a=new RegExp(o,t?void 0:"i");return[a,n]}(e.path,e.caseSensitive,e.end),o=t.match(r);if(!o)return null;let a=o[0],i=a.replace(/(.)\/+$/,"$1"),s=o.slice(1);return{params:n.reduce(((e,t,r)=>{let{paramName:n,isOptional:o}=t;if("*"===n){let e=s[r]||"";i=a.slice(0,a.length-e.length).replace(/(.)\/+$/,"$1")}const l=s[r];return e[n]=o&&!l?void 0:(l||"").replace(/%2F/g,"/"),e}),{}),pathname:a,pathnameBase:i,pattern:e}}function ee(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return _(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function te(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let r=t.endsWith("/")?t.length-1:t.length,n=e.charAt(r);return n&&"/"!==n?null:e.slice(r)||"/"}function re(e,t,r,n){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(n)+"].  Please separate it out to the `to."+r+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function ne(e,t){let r=function(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}(e);return t?r.map(((e,t)=>t===r.length-1?e.pathname:e.pathnameBase)):r.map((e=>e.pathnameBase))}function oe(e,t,r,n){let o;void 0===n&&(n=!1),"string"==typeof e?o=W(e):(o=z({},e),S(!o.pathname||!o.pathname.includes("?"),re("?","pathname","search",o)),S(!o.pathname||!o.pathname.includes("#"),re("#","pathname","hash",o)),S(!o.search||!o.search.includes("#"),re("#","search","hash",o)));let a,i=""===e||""===o.pathname,s=i?"/":o.pathname;if(null==s)a=r;else{let e=t.length-1;if(!n&&s.startsWith("..")){let t=s.split("/");for(;".."===t[0];)t.shift(),e-=1;o.pathname=t.join("/")}a=e>=0?t[e]:"/"}let l=function(e,t){void 0===t&&(t="/");let{pathname:r,search:n="",hash:o=""}="string"==typeof e?W(e):e,a=r?r.startsWith("/")?r:function(e,t){let r=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?r.length>1&&r.pop():"."!==e&&r.push(e)})),r.length>1?r.join("/"):"/"}(r,t):t;return{pathname:a,search:se(n),hash:le(o)}}(o,a),c=s&&"/"!==s&&s.endsWith("/"),u=(i||"."===s)&&r.endsWith("/");return l.pathname.endsWith("/")||!c&&!u||(l.pathname+="/"),l}const ae=e=>e.join("/").replace(/\/\/+/g,"/"),ie=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),se=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",le=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";function ce(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}const ue=["post","put","patch","delete"];new Set(ue);const de=["get",...ue];new Set(de);const pe=e=>e;let fe=pe,he=pe;function me(e){let t;return()=>(void 0===t&&(t=e()),t)}const be=(e,t,r)=>{const n=t-e;return 0===n?1:(r-e)/n},ge=e=>1e3*e,ve=e=>e/1e3,ye=me((()=>void 0!==window.ScrollTimeline));class we{constructor(e){this.stop=()=>this.runAll("stop"),this.animations=e.filter(Boolean)}get finished(){return Promise.all(this.animations.map((e=>"finished"in e?e.finished:e)))}getAll(e){return this.animations[0][e]}setAll(e,t){for(let r=0;r<this.animations.length;r++)this.animations[r][e]=t}attachTimeline(e,t){const r=this.animations.map((r=>ye()&&r.attachTimeline?r.attachTimeline(e):"function"==typeof t?t(r):void 0));return()=>{r.forEach(((e,t)=>{e&&e(),this.animations[t].stop()}))}}get time(){return this.getAll("time")}set time(e){this.setAll("time",e)}get speed(){return this.getAll("speed")}set speed(e){this.setAll("speed",e)}get startTime(){return this.getAll("startTime")}get duration(){let e=0;for(let t=0;t<this.animations.length;t++)e=Math.max(e,this.animations[t].duration);return e}runAll(e){this.animations.forEach((t=>t[e]()))}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class xe extends we{then(e,t){return Promise.all(this.animations).then(e).catch(t)}}function ke(e,t){return e?e[t]||e.default||e:void 0}const Pe=2e4;function Ee(e){let t=0;let r=e.next(t);for(;!r.done&&t<Pe;)t+=50,r=e.next(t);return t>=Pe?1/0:t}function Ce(e){return"function"==typeof e}function Le(e,t){e.timeline=t,e.onfinish=null}const Te=e=>Array.isArray(e)&&"number"==typeof e[0],ze={linearEasing:void 0};function Ie(e,t){const r=me(e);return()=>{var e;return null!==(e=ze[t])&&void 0!==e?e:r()}}const Ae=Ie((()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0}),"linearEasing"),je=(e,t,r=10)=>{let n="";const o=Math.max(Math.round(t/r),2);for(let a=0;a<o;a++)n+=e(be(0,o-1,a))+", ";return`linear(${n.substring(0,n.length-2)})`};function Oe(e){return Boolean("function"==typeof e&&Ae()||!e||"string"==typeof e&&(e in _e||Ae())||Te(e)||Array.isArray(e)&&e.every(Oe))}const Se=([e,t,r,n])=>`cubic-bezier(${e}, ${t}, ${r}, ${n})`,_e={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:Se([0,.65,.55,1]),circOut:Se([.55,0,1,.45]),backIn:Se([.31,.01,.66,-.59]),backOut:Se([.33,1.53,.69,.99])};function $e(e,t){return e?"function"==typeof e&&Ae()?je(e,t):Te(e)?Se(e):Array.isArray(e)?e.map((e=>$e(e,t)||_e.easeOut)):_e[e]:void 0}const Me={x:!1,y:!1};function Ne(){return Me.x||Me.y}function We(e,t,r){if(e instanceof Element)return[e];if("string"==typeof e){const t=document.querySelectorAll(e);return t?Array.from(t):[]}return Array.from(e)}function Re(e,t){const r=We(e),n=new AbortController;return[r,{passive:!0,...t,signal:n.signal},()=>n.abort()]}function Ge(e){return t=>{"touch"===t.pointerType||Ne()||e(t)}}function Be(e,t,r={}){const[n,o,a]=Re(e,r),i=Ge((e=>{const{target:r}=e,n=t(e);if("function"!=typeof n||!r)return;const a=Ge((e=>{n(e),r.removeEventListener("pointerleave",a)}));r.addEventListener("pointerleave",a,o)}));return n.forEach((e=>{e.addEventListener("pointerenter",i,o)})),a}const Ue=(e,t)=>!!t&&(e===t||Ue(e,t.parentElement)),Fe=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary,qe=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);const De=new WeakSet;function He(e){return t=>{"Enter"===t.key&&e(t)}}function Je(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}function Ve(e){return Fe(e)&&!Ne()}function Ye(e,t,r={}){const[n,o,a]=Re(e,r),i=e=>{const n=e.currentTarget;if(!Ve(e)||De.has(n))return;De.add(n);const a=t(e),i=(e,t)=>{window.removeEventListener("pointerup",s),window.removeEventListener("pointercancel",l),Ve(e)&&De.has(n)&&(De.delete(n),"function"==typeof a&&a(e,{success:t}))},s=e=>{i(e,r.useGlobalTarget||Ue(n,e.target))},l=e=>{i(e,!1)};window.addEventListener("pointerup",s,o),window.addEventListener("pointercancel",l,o)};return n.forEach((e=>{(function(e){return qe.has(e.tagName)||-1!==e.tabIndex})(e)||null!==e.getAttribute("tabindex")||(e.tabIndex=0);(r.useGlobalTarget?window:e).addEventListener("pointerdown",i,o),e.addEventListener("focus",(e=>((e,t)=>{const r=e.currentTarget;if(!r)return;const n=He((()=>{if(De.has(r))return;Je(r,"down");const e=He((()=>{Je(r,"up")}));r.addEventListener("keyup",e,t),r.addEventListener("blur",(()=>Je(r,"cancel")),t)}));r.addEventListener("keydown",n,t),r.addEventListener("blur",(()=>r.removeEventListener("keydown",n)),t)})(e,o)),o)})),a}function Ke(e){return"x"===e||"y"===e?Me[e]?null:(Me[e]=!0,()=>{Me[e]=!1}):Me.x||Me.y?null:(Me.x=Me.y=!0,()=>{Me.x=Me.y=!1})}function Qe(e){var t,r,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(r=Qe(e[t]))&&(n&&(n+=" "),n+=r)}else for(r in e)e[r]&&(n&&(n+=" "),n+=r);return n}function Xe(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=Qe(e))&&(n&&(n+=" "),n+=t);return n}const Ze=e=>{const t=nt(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{const r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),et(r,t)||rt(e)},getConflictingClassGroupIds:(e,t)=>{const o=r[e]||[];return t&&n[e]?[...o,...n[e]]:o}}},et=(e,t)=>{var r;if(0===e.length)return t.classGroupId;const n=e[0],o=t.nextPart.get(n),a=o?et(e.slice(1),o):void 0;if(a)return a;if(0===t.validators.length)return;const i=e.join("-");return null==(r=t.validators.find((({validator:e})=>e(i))))?void 0:r.classGroupId},tt=/^\[(.+)\]$/,rt=e=>{if(tt.test(e)){const t=tt.exec(e)[1],r=null==t?void 0:t.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},nt=e=>{const{theme:t,prefix:r}=e,n={nextPart:new Map,validators:[]};return st(Object.entries(e.classGroups),r).forEach((([e,r])=>{ot(r,n,e,t)})),n},ot=(e,t,r,n)=>{e.forEach((e=>{if("string"!=typeof e){if("function"==typeof e)return it(e)?void ot(e(n),t,r,n):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach((([e,o])=>{ot(o,at(t,e),r,n)}))}else{(""===e?t:at(t,e)).classGroupId=r}}))},at=(e,t)=>{let r=e;return t.split("-").forEach((e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)})),r},it=e=>e.isThemeGetter,st=(e,t)=>t?e.map((([e,r])=>[e,r.map((e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map((([e,r])=>[t+e,r]))):e))])):e,lt=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,r=new Map,n=new Map;const o=(o,a)=>{r.set(o,a),t++,t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(o(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):o(e,t)}}},ct=e=>{const{separator:t,experimentalParseClassName:r}=e,n=1===t.length,o=t[0],a=t.length,i=e=>{const r=[];let i,s=0,l=0;for(let d=0;d<e.length;d++){let c=e[d];if(0===s){if(c===o&&(n||e.slice(d,d+a)===t)){r.push(e.slice(l,d)),l=d+a;continue}if("/"===c){i=d;continue}}"["===c?s++:"]"===c&&s--}const c=0===r.length?e:e.substring(l),u=c.startsWith("!");return{modifiers:r,hasImportantModifier:u,baseClassName:u?c.substring(1):c,maybePostfixModifierPosition:i&&i>l?i-l:void 0}};return r?e=>r({className:e,parseClassName:i}):i},ut=e=>{if(e.length<=1)return e;const t=[];let r=[];return e.forEach((e=>{"["===e[0]?(t.push(...r.sort(),e),r=[]):r.push(e)})),t.push(...r.sort()),t},dt=/\s+/;function pt(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=ft(e))&&(n&&(n+=" "),n+=t);return n}const ft=e=>{if("string"==typeof e)return e;let t,r="";for(let n=0;n<e.length;n++)e[n]&&(t=ft(e[n]))&&(r&&(r+=" "),r+=t);return r};function ht(e,...t){let r,n,o,a=function(s){const l=t.reduce(((e,t)=>t(e)),e());return r=(e=>({cache:lt(e.cacheSize),parseClassName:ct(e),...Ze(e)}))(l),n=r.cache.get,o=r.cache.set,a=i,i(s)};function i(e){const t=n(e);if(t)return t;const a=((e,t)=>{const{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o}=t,a=[],i=e.trim().split(dt);let s="";for(let l=i.length-1;l>=0;l-=1){const e=i[l],{modifiers:t,hasImportantModifier:c,baseClassName:u,maybePostfixModifierPosition:d}=r(e);let p=Boolean(d),f=n(p?u.substring(0,d):u);if(!f){if(!p){s=e+(s.length>0?" "+s:s);continue}if(f=n(u),!f){s=e+(s.length>0?" "+s:s);continue}p=!1}const h=ut(t).join(":"),m=c?h+"!":h,b=m+f;if(a.includes(b))continue;a.push(b);const g=o(f,p);for(let r=0;r<g.length;++r){const e=g[r];a.push(m+e)}s=e+(s.length>0?" "+s:s)}return s})(e,r);return o(e,a),a}return function(){return a(pt.apply(null,arguments))}}const mt=e=>{const t=t=>t[e]||[];return t.isThemeGetter=!0,t},bt=/^\[(?:([a-z-]+):)?(.+)\]$/i,gt=/^\d+\/\d+$/,vt=new Set(["px","full","screen"]),yt=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,wt=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,xt=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,kt=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Pt=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Et=e=>Lt(e)||vt.has(e)||gt.test(e),Ct=e=>Rt(e,"length",Gt),Lt=e=>Boolean(e)&&!Number.isNaN(Number(e)),Tt=e=>Rt(e,"number",Lt),zt=e=>Boolean(e)&&Number.isInteger(Number(e)),It=e=>e.endsWith("%")&&Lt(e.slice(0,-1)),At=e=>bt.test(e),jt=e=>yt.test(e),Ot=new Set(["length","size","percentage"]),St=e=>Rt(e,Ot,Bt),_t=e=>Rt(e,"position",Bt),$t=new Set(["image","url"]),Mt=e=>Rt(e,$t,Ft),Nt=e=>Rt(e,"",Ut),Wt=()=>!0,Rt=(e,t,r)=>{const n=bt.exec(e);return!!n&&(n[1]?"string"==typeof t?n[1]===t:t.has(n[1]):r(n[2]))},Gt=e=>wt.test(e)&&!xt.test(e),Bt=()=>!1,Ut=e=>kt.test(e),Ft=e=>Pt.test(e),qt=ht((()=>{const e=mt("colors"),t=mt("spacing"),r=mt("blur"),n=mt("brightness"),o=mt("borderColor"),a=mt("borderRadius"),i=mt("borderSpacing"),s=mt("borderWidth"),l=mt("contrast"),c=mt("grayscale"),u=mt("hueRotate"),d=mt("invert"),p=mt("gap"),f=mt("gradientColorStops"),h=mt("gradientColorStopPositions"),m=mt("inset"),b=mt("margin"),g=mt("opacity"),v=mt("padding"),y=mt("saturate"),w=mt("scale"),x=mt("sepia"),k=mt("skew"),P=mt("space"),E=mt("translate"),C=()=>["auto",At,t],L=()=>[At,t],T=()=>["",Et,Ct],z=()=>["auto",Lt,At],I=()=>["","0",At],A=()=>[Lt,At];return{cacheSize:500,separator:":",theme:{colors:[Wt],spacing:[Et,Ct],blur:["none","",jt,At],brightness:A(),borderColor:[e],borderRadius:["none","","full",jt,At],borderSpacing:L(),borderWidth:T(),contrast:A(),grayscale:I(),hueRotate:A(),invert:I(),gap:L(),gradientColorStops:[e],gradientColorStopPositions:[It,Ct],inset:C(),margin:C(),opacity:A(),padding:L(),saturate:A(),scale:A(),sepia:I(),skew:A(),space:L(),translate:L()},classGroups:{aspect:[{aspect:["auto","square","video",At]}],container:["container"],columns:[{columns:[jt]}],"break-after":[{"break-after":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-before":[{"break-before":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top",At]}],overflow:[{overflow:["auto","hidden","clip","visible","scroll"]}],"overflow-x":[{"overflow-x":["auto","hidden","clip","visible","scroll"]}],"overflow-y":[{"overflow-y":["auto","hidden","clip","visible","scroll"]}],overscroll:[{overscroll:["auto","contain","none"]}],"overscroll-x":[{"overscroll-x":["auto","contain","none"]}],"overscroll-y":[{"overscroll-y":["auto","contain","none"]}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[m]}],"inset-x":[{"inset-x":[m]}],"inset-y":[{"inset-y":[m]}],start:[{start:[m]}],end:[{end:[m]}],top:[{top:[m]}],right:[{right:[m]}],bottom:[{bottom:[m]}],left:[{left:[m]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",zt,At]}],basis:[{basis:C()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",At]}],grow:[{grow:I()}],shrink:[{shrink:I()}],order:[{order:["first","last","none",zt,At]}],"grid-cols":[{"grid-cols":[Wt]}],"col-start-end":[{col:["auto",{span:["full",zt,At]},At]}],"col-start":[{"col-start":z()}],"col-end":[{"col-end":z()}],"grid-rows":[{"grid-rows":[Wt]}],"row-start-end":[{row:["auto",{span:[zt,At]},At]}],"row-start":[{"row-start":z()}],"row-end":[{"row-end":z()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",At]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",At]}],gap:[{gap:[p]}],"gap-x":[{"gap-x":[p]}],"gap-y":[{"gap-y":[p]}],"justify-content":[{justify:["normal","start","end","center","between","around","evenly","stretch"]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal","start","end","center","between","around","evenly","stretch","baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":["start","end","center","between","around","evenly","stretch","baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[v]}],px:[{px:[v]}],py:[{py:[v]}],ps:[{ps:[v]}],pe:[{pe:[v]}],pt:[{pt:[v]}],pr:[{pr:[v]}],pb:[{pb:[v]}],pl:[{pl:[v]}],m:[{m:[b]}],mx:[{mx:[b]}],my:[{my:[b]}],ms:[{ms:[b]}],me:[{me:[b]}],mt:[{mt:[b]}],mr:[{mr:[b]}],mb:[{mb:[b]}],ml:[{ml:[b]}],"space-x":[{"space-x":[P]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[P]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",At,t]}],"min-w":[{"min-w":[At,t,"min","max","fit"]}],"max-w":[{"max-w":[At,t,"none","full","min","max","fit","prose",{screen:[jt]},jt]}],h:[{h:[At,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[At,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[At,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[At,t,"auto","min","max","fit"]}],"font-size":[{text:["base",jt,Ct]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Tt]}],"font-family":[{font:[Wt]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",At]}],"line-clamp":[{"line-clamp":["none",Lt,Tt]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Et,At]}],"list-image":[{"list-image":["none",At]}],"list-style-type":[{list:["none","disc","decimal",At]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[g]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[g]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:["solid","dashed","dotted","double","none","wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Et,Ct]}],"underline-offset":[{"underline-offset":["auto",Et,At]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:L()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",At]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",At]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[g]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top",_t]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",St]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},Mt]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[h]}],"gradient-via-pos":[{via:[h]}],"gradient-to-pos":[{to:[h]}],"gradient-from":[{from:[f]}],"gradient-via":[{via:[f]}],"gradient-to":[{to:[f]}],rounded:[{rounded:[a]}],"rounded-s":[{"rounded-s":[a]}],"rounded-e":[{"rounded-e":[a]}],"rounded-t":[{"rounded-t":[a]}],"rounded-r":[{"rounded-r":[a]}],"rounded-b":[{"rounded-b":[a]}],"rounded-l":[{"rounded-l":[a]}],"rounded-ss":[{"rounded-ss":[a]}],"rounded-se":[{"rounded-se":[a]}],"rounded-ee":[{"rounded-ee":[a]}],"rounded-es":[{"rounded-es":[a]}],"rounded-tl":[{"rounded-tl":[a]}],"rounded-tr":[{"rounded-tr":[a]}],"rounded-br":[{"rounded-br":[a]}],"rounded-bl":[{"rounded-bl":[a]}],"border-w":[{border:[s]}],"border-w-x":[{"border-x":[s]}],"border-w-y":[{"border-y":[s]}],"border-w-s":[{"border-s":[s]}],"border-w-e":[{"border-e":[s]}],"border-w-t":[{"border-t":[s]}],"border-w-r":[{"border-r":[s]}],"border-w-b":[{"border-b":[s]}],"border-w-l":[{"border-l":[s]}],"border-opacity":[{"border-opacity":[g]}],"border-style":[{border:["solid","dashed","dotted","double","none","hidden"]}],"divide-x":[{"divide-x":[s]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[s]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[g]}],"divide-style":[{divide:["solid","dashed","dotted","double","none"]}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["","solid","dashed","dotted","double","none"]}],"outline-offset":[{"outline-offset":[Et,At]}],"outline-w":[{outline:[Et,Ct]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:T()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[g]}],"ring-offset-w":[{"ring-offset":[Et,Ct]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",jt,Nt]}],"shadow-color":[{shadow:[Wt]}],opacity:[{opacity:[g]}],"mix-blend":[{"mix-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"]}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[n]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",jt,At]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[u]}],invert:[{invert:[d]}],saturate:[{saturate:[y]}],sepia:[{sepia:[x]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[n]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[u]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[g]}],"backdrop-saturate":[{"backdrop-saturate":[y]}],"backdrop-sepia":[{"backdrop-sepia":[x]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",At]}],duration:[{duration:A()}],ease:[{ease:["linear","in","out","in-out",At]}],delay:[{delay:A()}],animate:[{animate:["none","spin","ping","pulse","bounce",At]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[w]}],"scale-x":[{"scale-x":[w]}],"scale-y":[{"scale-y":[w]}],rotate:[{rotate:[zt,At]}],"translate-x":[{"translate-x":[E]}],"translate-y":[{"translate-y":[E]}],"skew-x":[{"skew-x":[k]}],"skew-y":[{"skew-y":[k]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",At]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",At]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":L()}],"scroll-mx":[{"scroll-mx":L()}],"scroll-my":[{"scroll-my":L()}],"scroll-ms":[{"scroll-ms":L()}],"scroll-me":[{"scroll-me":L()}],"scroll-mt":[{"scroll-mt":L()}],"scroll-mr":[{"scroll-mr":L()}],"scroll-mb":[{"scroll-mb":L()}],"scroll-ml":[{"scroll-ml":L()}],"scroll-p":[{"scroll-p":L()}],"scroll-px":[{"scroll-px":L()}],"scroll-py":[{"scroll-py":L()}],"scroll-ps":[{"scroll-ps":L()}],"scroll-pe":[{"scroll-pe":L()}],"scroll-pt":[{"scroll-pt":L()}],"scroll-pr":[{"scroll-pr":L()}],"scroll-pb":[{"scroll-pb":L()}],"scroll-pl":[{"scroll-pl":L()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",At]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Et,Ct,Tt]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}}));export{I as A,he as B,be as C,$e as D,me as E,Ae as F,Le as G,Oe as H,ke as I,xe as J,Fe as K,Ke as L,Be as M,Ye as N,We as O,T as P,ye as Q,fe as R,P as a,ne as b,Xe as c,te as d,B as e,ce as f,e as g,k as h,S as i,ae as j,O as k,N as l,g as m,Z as n,pe as o,W as p,Ce as q,oe as r,n as s,qt as t,ve as u,ge as v,Ee as w,Pe as x,je as y,Te as z};

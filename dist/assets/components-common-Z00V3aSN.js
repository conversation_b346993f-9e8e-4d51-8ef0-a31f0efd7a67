import{j as e}from"./vendor-react-BQEJZSNG.js";import{H as s,M as a}from"./components-other-CtgHTO3L.js";const r="_navbarContainer_a4c05_1",i="_navbarInner_a4c05_13",c="_logoContainer_a4c05_24",n="_logo_a4c05_24",l="_desktopNav_a4c05_37",o="_mobileNav_a4c05_42",d=()=>e.jsx("div",{className:r,children:e.jsxs("div",{className:i,children:[e.jsx("div",{className:c,children:e.jsx("img",{src:"/logo.webp",alt:"Logo",className:n})}),e.jsx("div",{className:l,children:e.jsx(s,{})}),e.jsx("div",{className:o,children:e.jsx(a,{})})]})}),h="_footerContainer_y9ybe_3",t="_footerContent_y9ybe_29",m="_footerLinks_y9ybe_67",_="_footerBottom_y9ybe_93",v="_footerBrand_y9ybe_108",x="_footerBottomLinks_y9ybe_115",N=()=>e.jsx("footer",{className:h,children:e.jsxs("div",{className:"container mx-auto",children:[e.jsx("div",{className:t,children:e.jsxs("div",{className:m,children:[e.jsx("a",{href:"/services",className:"hover:underline",children:"SERVICES"}),e.jsx("a",{href:"/services/web-development",className:"hover:underline",children:"WEB DEVELOPMENT"}),e.jsx("a",{href:"/services/app-development",className:"hover:underline",children:"APP DEVELOPMENT"}),e.jsx("a",{href:"/services/graphic-design",className:"hover:underline",children:"GRAPHIC DESIGN"}),e.jsx("a",{href:"/services/automation",className:"hover:underline",children:"AUTOMATION"}),e.jsx("a",{href:"/services/marketing",className:"hover:underline",children:"MARKETING"}),e.jsx("a",{href:"/services/email",className:"hover:underline",children:"EMAIL"}),e.jsx("a",{href:"/contact",className:"hover:underline",children:"CONTACT"})]})}),e.jsxs("div",{className:_,children:[e.jsx("p",{className:v,children:"Veltrix"}),e.jsxs("div",{className:x,children:[e.jsx("p",{children:"Privacy Policy"}),e.jsx("p",{children:"Websites by Us"}),e.jsx("p",{children:"2024 Veltrix"})]})]})]})});export{N as F,d as N};

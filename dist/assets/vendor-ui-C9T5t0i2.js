import{r as t,j as e}from"./vendor-react-BQEJZSNG.js";import{o as n,q as i,u as s,v as o,w as r,x as a,y as l,z as u,B as c,C as h,D as d,E as p,F as m,G as f,H as g,I as v,J as y,K as x,L as P,M as w,N as T,O as S,Q as b,R as A}from"./vendor-other-DSmItuYW.js";const E=t.createContext({});function V(e){const n=t.useRef(null);return null===n.current&&(n.current=e()),n.current}const C=t.createContext(null),M=t.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});class D extends t.Component{getSnapshotBeforeUpdate(t){const e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){const t=this.props.sizeRef.current;t.height=e.offsetHeight||0,t.width=e.offsetWidth||0,t.top=e.offsetTop,t.left=e.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function R({children:n,isPresent:i}){const s=t.useId(),o=t.useRef(null),r=t.useRef({width:0,height:0,top:0,left:0}),{nonce:a}=t.useContext(M);return t.useInsertionEffect((()=>{const{width:t,height:e,top:n,left:l}=r.current;if(i||!o.current||!t||!e)return;o.current.dataset.motionPopId=s;const u=document.createElement("style");return a&&(u.nonce=a),document.head.appendChild(u),u.sheet&&u.sheet.insertRule(`\n          [data-motion-pop-id="${s}"] {\n            position: absolute !important;\n            width: ${t}px !important;\n            height: ${e}px !important;\n            top: ${n}px !important;\n            left: ${l}px !important;\n          }\n        `),()=>{document.head.removeChild(u)}}),[i]),e.jsx(D,{isPresent:i,childRef:o,sizeRef:r,children:t.cloneElement(n,{ref:o})})}const k=({children:n,initial:i,isPresent:s,onExitComplete:o,custom:r,presenceAffectsLayout:a,mode:l})=>{const u=V(L),c=t.useId(),h=t.useCallback((t=>{u.set(t,!0);for(const e of u.values())if(!e)return;o&&o()}),[u,o]),d=t.useMemo((()=>({id:c,initial:i,isPresent:s,custom:r,onExitComplete:h,register:t=>(u.set(t,!1),()=>u.delete(t))})),a?[Math.random(),h]:[s,h]);return t.useMemo((()=>{u.forEach(((t,e)=>u.set(e,!1)))}),[s]),t.useEffect((()=>{!s&&!u.size&&o&&o()}),[s]),"popLayout"===l&&(n=e.jsx(R,{isPresent:s,children:n})),e.jsx(C.Provider,{value:d,children:n})};function L(){return new Map}function j(e=!0){const n=t.useContext(C);if(null===n)return[!0,null];const{isPresent:i,onExitComplete:s,register:o}=n,r=t.useId();t.useEffect((()=>{e&&o(r)}),[e]);const a=t.useCallback((()=>e&&s&&s(r)),[r,s,e]);return!i&&s?[!1,a]:[!0]}const B=t=>t.key||"";function F(e){const n=[];return t.Children.forEach(e,(e=>{t.isValidElement(e)&&n.push(e)})),n}const O="undefined"!=typeof window,I=O?t.useLayoutEffect:t.useEffect,U=({children:n,custom:i,initial:s=!0,onExitComplete:o,presenceAffectsLayout:r=!0,mode:a="sync",propagate:l=!1})=>{const[u,c]=j(l),h=t.useMemo((()=>F(n)),[n]),d=l&&!u?[]:h.map(B),p=t.useRef(!0),m=t.useRef(h),f=V((()=>new Map)),[g,v]=t.useState(h),[y,x]=t.useState(h);I((()=>{p.current=!1,m.current=h;for(let t=0;t<y.length;t++){const e=B(y[t]);d.includes(e)?f.delete(e):!0!==f.get(e)&&f.set(e,!1)}}),[y,d.length,d.join("-")]);const P=[];if(h!==g){let t=[...h];for(let e=0;e<y.length;e++){const n=y[e],i=B(n);d.includes(i)||(t.splice(e,0,n),P.push(n))}return"wait"===a&&P.length&&(t=P),x(F(t)),void v(h)}const{forceRender:w}=t.useContext(E);return e.jsx(e.Fragment,{children:y.map((t=>{const n=B(t),g=!(l&&!u)&&(h===y||d.includes(n));return e.jsx(k,{isPresent:g,initial:!(p.current&&!s)&&void 0,custom:g?void 0:i,presenceAffectsLayout:r,mode:a,onExitComplete:g?void 0:()=>{if(!f.has(n))return;f.set(n,!0);let t=!0;f.forEach((e=>{e||(t=!1)})),t&&(null==w||w(),x(m.current),l&&(null==c||c()),o&&o())},children:t},n)}))})},W=!1;const N=["read","resolveKeyframes","update","preRender","render","postRender"];function $(t,e){let n=!1,i=!0;const s={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,r=N.reduce(((t,e)=>(t[e]=function(t){let e=new Set,n=new Set,i=!1,s=!1;const o=new WeakSet;let r={delta:0,timestamp:0,isProcessing:!1};function a(e){o.has(e)&&(l.schedule(e),t()),e(r)}const l={schedule:(t,s=!1,r=!1)=>{const a=r&&i?e:n;return s&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{n.delete(t),o.delete(t)},process:t=>{r=t,i?s=!0:(i=!0,[e,n]=[n,e],e.forEach(a),e.clear(),i=!1,s&&(s=!1,l.process(t)))}};return l}(o),t)),{}),{read:a,resolveKeyframes:l,update:u,preRender:c,render:h,postRender:d}=r,p=()=>{const o=performance.now();n=!1,s.delta=i?1e3/60:Math.max(Math.min(o-s.timestamp,40),1),s.timestamp=o,s.isProcessing=!0,a.process(s),l.process(s),u.process(s),c.process(s),h.process(s),d.process(s),s.isProcessing=!1,n&&e&&(i=!1,t(p))};return{schedule:N.reduce(((e,o)=>{const a=r[o];return e[o]=(e,o=!1,r=!1)=>(n||(n=!0,i=!0,s.isProcessing||t(p)),a.schedule(e,o,r)),e}),{}),cancel:t=>{for(let e=0;e<N.length;e++)r[N[e]].cancel(t)},state:s,steps:r}}const{schedule:z,cancel:H,state:Y,steps:K}=$("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:n,!0),X=t.createContext({strict:!1}),G={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},q={};for(const Ra in G)q[Ra]={isEnabled:t=>G[Ra].some((e=>!!t[e]))};const Z=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function _(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||Z.has(t)}let J=t=>!_(t);try{(Q=require("@emotion/is-prop-valid").default)&&(J=t=>t.startsWith("on")?!_(t):Q(t))}catch(Da){}var Q;function tt(t){if("undefined"==typeof Proxy)return t;const e=new Map;return new Proxy(((...e)=>t(...e)),{get:(n,i)=>"create"===i?t:(e.has(i)||e.set(i,t(i)),e.get(i))})}const et=t.createContext({});function nt(t){return"string"==typeof t||Array.isArray(t)}function it(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}const st=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ot=["initial",...st];function rt(t){return it(t.animate)||ot.some((e=>nt(t[e])))}function at(t){return Boolean(rt(t)||t.variants)}function lt(e){const{initial:n,animate:i}=function(t,e){if(rt(t)){const{initial:e,animate:n}=t;return{initial:!1===e||nt(e)?e:void 0,animate:nt(n)?n:void 0}}return!1!==t.inherit?e:{}}(e,t.useContext(et));return t.useMemo((()=>({initial:n,animate:i})),[ut(n),ut(i)])}function ut(t){return Array.isArray(t)?t.join(" "):t}const ct=Symbol.for("motionComponentSymbol");function ht(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function dt(e,n,i){return t.useCallback((t=>{t&&e.onMount&&e.onMount(t),n&&(t?n.mount(t):n.unmount()),i&&("function"==typeof i?i(t):ht(i)&&(i.current=t))}),[n])}const pt=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),mt="data-"+pt("framerAppearId"),{schedule:ft,cancel:gt}=$(queueMicrotask,!1),vt=t.createContext({});function yt(e,n,i,s,o){var r,a;const{visualElement:l}=t.useContext(et),u=t.useContext(X),c=t.useContext(C),h=t.useContext(M).reducedMotion,d=t.useRef(null);s=s||u.renderer,!d.current&&s&&(d.current=s(e,{visualState:n,parent:l,props:i,presenceContext:c,blockInitialAnimation:!!c&&!1===c.initial,reducedMotionConfig:h}));const p=d.current,m=t.useContext(vt);!p||p.projection||!o||"html"!==p.type&&"svg"!==p.type||function(t,e,n,i){const{layoutId:s,layout:o,drag:r,dragConstraints:a,layoutScroll:l,layoutRoot:u}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:xt(t.parent)),t.projection.setOptions({layoutId:s,layout:o,alwaysMeasureLayout:Boolean(r)||a&&ht(a),visualElement:t,animationType:"string"==typeof o?o:"both",initialPromotionConfig:i,layoutScroll:l,layoutRoot:u})}(d.current,i,o,m);const f=t.useRef(!1);t.useInsertionEffect((()=>{p&&f.current&&p.update(i,c)}));const g=i[mt],v=t.useRef(Boolean(g)&&!(null===(r=window.MotionHandoffIsComplete)||void 0===r?void 0:r.call(window,g))&&(null===(a=window.MotionHasOptimisedAnimation)||void 0===a?void 0:a.call(window,g)));return I((()=>{p&&(f.current=!0,window.MotionIsMounted=!0,p.updateFeatures(),ft.render(p.render),v.current&&p.animationState&&p.animationState.animateChanges())})),t.useEffect((()=>{p&&(!v.current&&p.animationState&&p.animationState.animateChanges(),v.current&&(queueMicrotask((()=>{var t;null===(t=window.MotionHandoffMarkAsComplete)||void 0===t||t.call(window,g)})),v.current=!1))})),p}function xt(t){if(t)return!1!==t.options.allowProjection?t.projection:xt(t.parent)}function Pt({preloadedFeatures:n,createVisualElement:i,useRender:s,useVisualState:o,Component:r}){var a,l;function u(n,a){let l;const u={...t.useContext(M),...n,layoutId:wt(n)},{isStatic:c}=u,h=lt(n),d=o(n,c);if(!c&&O){t.useContext(X).strict;const e=function(t){const{drag:e,layout:n}=q;if(!e&&!n)return{};const i={...e,...n};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==n?void 0:n.isEnabled(t))?i.MeasureLayout:void 0,ProjectionNode:i.ProjectionNode}}(u);l=e.MeasureLayout,h.visualElement=yt(r,d,u,i,e.ProjectionNode)}return e.jsxs(et.Provider,{value:h,children:[l&&h.visualElement?e.jsx(l,{visualElement:h.visualElement,...u}):null,s(r,n,dt(d,h.visualElement,a),d,c,h.visualElement)]})}n&&function(t){for(const e in t)q[e]={...q[e],...t[e]}}(n),u.displayName=`motion.${"string"==typeof r?r:`create(${null!==(l=null!==(a=r.displayName)&&void 0!==a?a:r.name)&&void 0!==l?l:""})`}`;const c=t.forwardRef(u);return c[ct]=r,c}function wt({layoutId:e}){const n=t.useContext(E).id;return n&&void 0!==e?n+"-"+e:e}const Tt=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function St(t){return"string"==typeof t&&!t.includes("-")&&!!(Tt.indexOf(t)>-1||/[A-Z]/u.test(t))}function bt(t){const e=[{},{}];return null==t||t.values.forEach(((t,n)=>{e[0][n]=t.get(),e[1][n]=t.getVelocity()})),e}function At(t,e,n,i){if("function"==typeof e){const[s,o]=bt(i);e=e(void 0!==n?n:t.custom,s,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[s,o]=bt(i);e=e(void 0!==n?n:t.custom,s,o)}return e}const Et=t=>Array.isArray(t),Vt=t=>Boolean(t&&t.getVelocity);function Ct(t){const e=Vt(t)?t.get():t;return n=e,Boolean(n&&"object"==typeof n&&n.mix&&n.toValue)?e.toValue():e;var n}const Mt=e=>(n,i)=>{const s=t.useContext(et),o=t.useContext(C),r=()=>function({scrapeMotionValuesFromProps:t,createRenderState:e,onUpdate:n},i,s,o){const r={latestValues:Dt(i,s,o,t),renderState:e()};return n&&(r.onMount=t=>n({props:i,current:t,...r}),r.onUpdate=t=>n(t)),r}(e,n,s,o);return i?r():V(r)};function Dt(t,e,n,i){const s={},o=i(t,{});for(const d in o)s[d]=Ct(o[d]);let{initial:r,animate:a}=t;const l=rt(t),u=at(t);e&&u&&!l&&!1!==t.inherit&&(void 0===r&&(r=e.initial),void 0===a&&(a=e.animate));let c=!!n&&!1===n.initial;c=c||!1===r;const h=c?a:r;if(h&&"boolean"!=typeof h&&!it(h)){const e=Array.isArray(h)?h:[h];for(let n=0;n<e.length;n++){const i=At(t,e[n]);if(i){const{transitionEnd:t,transition:e,...n}=i;for(const i in n){let t=n[i];if(Array.isArray(t)){t=t[c?t.length-1:0]}null!==t&&(s[i]=t)}for(const i in t)s[i]=t[i]}}}return s}const Rt=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],kt=new Set(Rt),Lt=t=>e=>"string"==typeof e&&e.startsWith(t),jt=Lt("--"),Bt=Lt("var(--"),Ft=t=>!!Bt(t)&&Ot.test(t.split("/*")[0].trim()),Ot=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,It=(t,e)=>e&&"number"==typeof t?e.transform(t):t,Ut=(t,e,n)=>n>e?e:n<t?t:n,Wt={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},Nt={...Wt,transform:t=>Ut(0,1,t)},$t={...Wt,default:1},zt=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),Ht=zt("deg"),Yt=zt("%"),Kt=zt("px"),Xt=zt("vh"),Gt=zt("vw"),qt={...Yt,parse:t=>Yt.parse(t)/100,transform:t=>Yt.transform(100*t)},Zt={borderWidth:Kt,borderTopWidth:Kt,borderRightWidth:Kt,borderBottomWidth:Kt,borderLeftWidth:Kt,borderRadius:Kt,radius:Kt,borderTopLeftRadius:Kt,borderTopRightRadius:Kt,borderBottomRightRadius:Kt,borderBottomLeftRadius:Kt,width:Kt,maxWidth:Kt,height:Kt,maxHeight:Kt,top:Kt,right:Kt,bottom:Kt,left:Kt,padding:Kt,paddingTop:Kt,paddingRight:Kt,paddingBottom:Kt,paddingLeft:Kt,margin:Kt,marginTop:Kt,marginRight:Kt,marginBottom:Kt,marginLeft:Kt,backgroundPositionX:Kt,backgroundPositionY:Kt},_t={rotate:Ht,rotateX:Ht,rotateY:Ht,rotateZ:Ht,scale:$t,scaleX:$t,scaleY:$t,scaleZ:$t,skew:Ht,skewX:Ht,skewY:Ht,distance:Kt,translateX:Kt,translateY:Kt,translateZ:Kt,x:Kt,y:Kt,z:Kt,perspective:Kt,transformPerspective:Kt,opacity:Nt,originX:qt,originY:qt,originZ:Kt},Jt={...Wt,transform:Math.round},Qt={...Zt,..._t,zIndex:Jt,size:Kt,fillOpacity:Nt,strokeOpacity:Nt,numOctaves:Jt},te={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},ee=Rt.length;function ne(t,e,n){const{style:i,vars:s,transformOrigin:o}=t;let r=!1,a=!1;for(const l in e){const t=e[l];if(kt.has(l))r=!0;else if(jt(l))s[l]=t;else{const e=It(t,Qt[l]);l.startsWith("origin")?(a=!0,o[l]=e):i[l]=e}}if(e.transform||(r||n?i.transform=function(t,e,n){let i="",s=!0;for(let o=0;o<ee;o++){const r=Rt[o],a=t[r];if(void 0===a)continue;let l=!0;if(l="number"==typeof a?a===(r.startsWith("scale")?1:0):0===parseFloat(a),!l||n){const t=It(a,Qt[r]);l||(s=!1,i+=`${te[r]||r}(${t}) `),n&&(e[r]=t)}}return i=i.trim(),n?i=n(e,s?"":i):s&&(i="none"),i}(e,t.transform,n):i.transform&&(i.transform="none")),a){const{originX:t="50%",originY:e="50%",originZ:n=0}=o;i.transformOrigin=`${t} ${e} ${n}`}}const ie={offset:"stroke-dashoffset",array:"stroke-dasharray"},se={offset:"strokeDashoffset",array:"strokeDasharray"};function oe(t,e,n){return"string"==typeof t?t:Kt.transform(e+n*t)}function re(t,{attrX:e,attrY:n,attrScale:i,originX:s,originY:o,pathLength:r,pathSpacing:a=1,pathOffset:l=0,...u},c,h){if(ne(t,u,h),c)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:d,style:p,dimensions:m}=t;d.transform&&(m&&(p.transform=d.transform),delete d.transform),m&&(void 0!==s||void 0!==o||p.transform)&&(p.transformOrigin=function(t,e,n){return`${oe(e,t.x,t.width)} ${oe(n,t.y,t.height)}`}(m,void 0!==s?s:.5,void 0!==o?o:.5)),void 0!==e&&(d.x=e),void 0!==n&&(d.y=n),void 0!==i&&(d.scale=i),void 0!==r&&function(t,e,n=1,i=0,s=!0){t.pathLength=1;const o=s?ie:se;t[o.offset]=Kt.transform(-i);const r=Kt.transform(e),a=Kt.transform(n);t[o.array]=`${r} ${a}`}(d,r,a,l,!1)}const ae=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),le=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}}),ue=t=>"string"==typeof t&&"svg"===t.toLowerCase();function ce(t,{style:e,vars:n},i,s){Object.assign(t.style,e,s&&s.getProjectionStyles(i));for(const o in n)t.style.setProperty(o,n[o])}const he=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function de(t,e,n,i){ce(t,e,void 0,i);for(const s in e.attrs)t.setAttribute(he.has(s)?s:pt(s),e.attrs[s])}const pe={};function me(t,{layout:e,layoutId:n}){return kt.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!pe[t]||"opacity"===t)}function fe(t,e,n){var i;const{style:s}=t,o={};for(const r in s)(Vt(s[r])||e.style&&Vt(e.style[r])||me(r,t)||void 0!==(null===(i=null==n?void 0:n.getValue(r))||void 0===i?void 0:i.liveStyle))&&(o[r]=s[r]);return o}function ge(t,e,n){const i=fe(t,e,n);for(const s in t)if(Vt(t[s])||Vt(e[s])){i[-1!==Rt.indexOf(s)?"attr"+s.charAt(0).toUpperCase()+s.substring(1):s]=t[s]}return i}const ve=["x","y","width","height","cx","cy","r"],ye={useVisualState:Mt({scrapeMotionValuesFromProps:ge,createRenderState:le,onUpdate:({props:t,prevProps:e,current:n,renderState:i,latestValues:s})=>{if(!n)return;let o=!!t.drag;if(!o)for(const a in s)if(kt.has(a)){o=!0;break}if(!o)return;let r=!e;if(e)for(let a=0;a<ve.length;a++){const n=ve[a];t[n]!==e[n]&&(r=!0)}r&&z.read((()=>{!function(t,e){try{e.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(n){e.dimensions={x:0,y:0,width:0,height:0}}}(n,i),z.render((()=>{re(i,s,ue(n.tagName),t.transformTemplate),de(n,i)}))}))}})},xe={useVisualState:Mt({scrapeMotionValuesFromProps:fe,createRenderState:ae})};function Pe(t,e,n){for(const i in e)Vt(e[i])||me(i,n)||(t[i]=e[i])}function we(e,n){const i={};return Pe(i,e.style||{},e),Object.assign(i,function({transformTemplate:e},n){return t.useMemo((()=>{const t={style:{},transform:{},transformOrigin:{},vars:{}};return ne(t,n,e),Object.assign({},t.vars,t.style)}),[n])}(e,n)),i}function Te(t,e){const n={},i=we(t,e);return t.drag&&!1!==t.dragListener&&(n.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=i,n}function Se(e,n,i,s){const o=t.useMemo((()=>{const t={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};return re(t,n,ue(s),e.transformTemplate),{...t.attrs,style:{...t.style}}}),[n]);if(e.style){const t={};Pe(t,e.style,e),o.style={...t,...o.style}}return o}function be(e=!1){return(n,i,s,{latestValues:o},r)=>{const a=(St(n)?Se:Te)(i,o,r,n),l=function(t,e,n){const i={};for(const s in t)"values"===s&&"object"==typeof t.values||(J(s)||!0===n&&_(s)||!e&&!_(s)||t.draggable&&s.startsWith("onDrag"))&&(i[s]=t[s]);return i}(i,"string"==typeof n,e),u=n!==t.Fragment?{...l,...a,ref:s}:{},{children:c}=i,h=t.useMemo((()=>Vt(c)?c.get():c),[c]);return t.createElement(n,{...u,children:h})}}function Ae(t,e){return function(n,{forwardMotionProps:i}={forwardMotionProps:!1}){return Pt({...St(n)?ye:xe,preloadedFeatures:t,useRender:be(i),createVisualElement:e,Component:n})}}function Ee(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let i=0;i<n;i++)if(e[i]!==t[i])return!1;return!0}function Ve(t,e,n){const i=t.getProps();return At(i,e,void 0!==n?n:i.custom,t)}const Ce=new Set(["width","height","top","left","right","bottom",...Rt]);let Me;function De(){Me=void 0}const Re={now:()=>(void 0===Me&&Re.set(Y.isProcessing||W?Y.timestamp:performance.now()),Me),set:t=>{Me=t,queueMicrotask(De)}};function ke(t,e){-1===t.indexOf(e)&&t.push(e)}function Le(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}class je{constructor(){this.subscriptions=[]}add(t){return ke(this.subscriptions,t),()=>Le(this.subscriptions,t)}notify(t,e,n){const i=this.subscriptions.length;if(i)if(1===i)this.subscriptions[0](t,e,n);else for(let s=0;s<i;s++){const i=this.subscriptions[s];i&&i(t,e,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function Be(t,e){return e?t*(1e3/e):0}const Fe={current:void 0};class Oe{constructor(t,e={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{const n=Re.now();this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){var e;this.current=t,this.updatedAt=Re.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=(e=this.current,!isNaN(parseFloat(e))))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new je);const n=this.events[t].add(e);return"change"===t?()=>{n(),z.read((()=>{this.events.change.getSize()||this.stop()}))}:n}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,n){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-n}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return Fe.current&&Fe.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){const t=Re.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;const e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return Be(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise((e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()})).then((()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()}))}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Ie(t,e){return new Oe(t,e)}function Ue(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,Ie(n))}function We(t,e){const n=Ve(t,e);let{transitionEnd:i={},transition:s={},...o}=n||{};o={...o,...i};for(const a in o){Ue(t,a,(r=o[a],Et(r)?r[r.length-1]||0:r))}var r}function Ne(t,e){const n=t.getValue("willChange");if(i=n,Boolean(Vt(i)&&i.add))return n.add(e);var i}function $e(t){return t.props[mt]}const ze=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t;function He(t,e,i,s){if(t===e&&i===s)return n;const o=e=>function(t,e,n,i,s){let o,r,a=0;do{r=e+(n-e)/2,o=ze(r,i,s)-t,o>0?n=r:e=r}while(Math.abs(o)>1e-7&&++a<12);return r}(e,0,1,t,i);return t=>0===t||1===t?t:ze(o(t),e,s)}const Ye=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,Ke=t=>e=>1-t(1-e),Xe=He(.33,1.53,.69,.99),Ge=Ke(Xe),qe=Ye(Ge),Ze=t=>(t*=2)<1?.5*Ge(t):.5*(2-Math.pow(2,-10*(t-1))),_e=t=>1-Math.sin(Math.acos(t)),Je=Ke(_e),Qe=Ye(_e),tn=t=>/^0[^.\s]+$/u.test(t);const en=t=>Math.round(1e5*t)/1e5,nn=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;const sn=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,on=(t,e)=>n=>Boolean("string"==typeof n&&sn.test(n)&&n.startsWith(t)||e&&!function(t){return null==t}(n)&&Object.prototype.hasOwnProperty.call(n,e)),rn=(t,e,n)=>i=>{if("string"!=typeof i)return i;const[s,o,r,a]=i.match(nn);return{[t]:parseFloat(s),[e]:parseFloat(o),[n]:parseFloat(r),alpha:void 0!==a?parseFloat(a):1}},an={...Wt,transform:t=>Math.round((t=>Ut(0,255,t))(t))},ln={test:on("rgb","red"),parse:rn("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:i=1})=>"rgba("+an.transform(t)+", "+an.transform(e)+", "+an.transform(n)+", "+en(Nt.transform(i))+")"};const un={test:on("#"),parse:function(t){let e="",n="",i="",s="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),i=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),i=t.substring(3,4),s=t.substring(4,5),e+=e,n+=n,i+=i,s+=s),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(i,16),alpha:s?parseInt(s,16)/255:1}},transform:ln.transform},cn={test:on("hsl","hue"),parse:rn("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:i=1})=>"hsla("+Math.round(t)+", "+Yt.transform(en(e))+", "+Yt.transform(en(n))+", "+en(Nt.transform(i))+")"},hn={test:t=>ln.test(t)||un.test(t)||cn.test(t),parse:t=>ln.test(t)?ln.parse(t):cn.test(t)?cn.parse(t):un.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?ln.transform(t):cn.transform(t)},dn=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;const pn="number",mn="color",fn=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function gn(t){const e=t.toString(),n=[],i={color:[],number:[],var:[]},s=[];let o=0;const r=e.replace(fn,(t=>(hn.test(t)?(i.color.push(o),s.push(mn),n.push(hn.parse(t))):t.startsWith("var(")?(i.var.push(o),s.push("var"),n.push(t)):(i.number.push(o),s.push(pn),n.push(parseFloat(t))),++o,"${}"))).split("${}");return{values:n,split:r,indexes:i,types:s}}function vn(t){return gn(t).values}function yn(t){const{split:e,types:n}=gn(t),i=e.length;return t=>{let s="";for(let o=0;o<i;o++)if(s+=e[o],void 0!==t[o]){const e=n[o];s+=e===pn?en(t[o]):e===mn?hn.transform(t[o]):t[o]}return s}}const xn=t=>"number"==typeof t?0:t;const Pn={test:function(t){var e,n;return isNaN(t)&&"string"==typeof t&&((null===(e=t.match(nn))||void 0===e?void 0:e.length)||0)+((null===(n=t.match(dn))||void 0===n?void 0:n.length)||0)>0},parse:vn,createTransformer:yn,getAnimatableNone:function(t){const e=vn(t);return yn(t)(e.map(xn))}},wn=new Set(["brightness","contrast","saturate","opacity"]);function Tn(t){const[e,n]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;const[i]=n.match(nn)||[];if(!i)return t;const s=n.replace(i,"");let o=wn.has(e)?1:0;return i!==n&&(o*=100),e+"("+o+s+")"}const Sn=/\b([a-z-]*)\(.*?\)/gu,bn={...Pn,getAnimatableNone:t=>{const e=t.match(Sn);return e?e.map(Tn).join(" "):t}},An={...Qt,color:hn,backgroundColor:hn,outlineColor:hn,fill:hn,stroke:hn,borderColor:hn,borderTopColor:hn,borderRightColor:hn,borderBottomColor:hn,borderLeftColor:hn,filter:bn,WebkitFilter:bn},En=t=>An[t];function Vn(t,e){let n=En(t);return n!==bn&&(n=Pn),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Cn=new Set(["auto","none","0"]);const Mn=t=>t===Wt||t===Kt,Dn=(t,e)=>parseFloat(t.split(", ")[e]),Rn=(t,e)=>(n,{transform:i})=>{if("none"===i||!i)return 0;const s=i.match(/^matrix3d\((.+)\)$/u);if(s)return Dn(s[1],e);{const e=i.match(/^matrix\((.+)\)$/u);return e?Dn(e[1],t):0}},kn=new Set(["x","y","z"]),Ln=Rt.filter((t=>!kn.has(t)));const jn={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:Rn(4,13),y:Rn(5,14)};jn.translateX=jn.x,jn.translateY=jn.y;const Bn=new Set;let Fn=!1,On=!1;function In(){if(On){const t=Array.from(Bn).filter((t=>t.needsMeasurement)),e=new Set(t.map((t=>t.element))),n=new Map;e.forEach((t=>{const e=function(t){const e=[];return Ln.forEach((n=>{const i=t.getValue(n);void 0!==i&&(e.push([n,i.get()]),i.set(n.startsWith("scale")?1:0))})),e}(t);e.length&&(n.set(t,e),t.render())})),t.forEach((t=>t.measureInitialState())),e.forEach((t=>{t.render();const e=n.get(t);e&&e.forEach((([e,n])=>{var i;null===(i=t.getValue(e))||void 0===i||i.set(n)}))})),t.forEach((t=>t.measureEndState())),t.forEach((t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)}))}On=!1,Fn=!1,Bn.forEach((t=>t.complete())),Bn.clear()}function Un(){Bn.forEach((t=>{t.readKeyframes(),t.needsMeasurement&&(On=!0)}))}class Wn{constructor(t,e,n,i,s,o=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=n,this.motionValue=i,this.element=s,this.isAsync=o}scheduleResolve(){this.isScheduled=!0,this.isAsync?(Bn.add(this),Fn||(Fn=!0,z.read(Un),z.resolveKeyframes(In))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:t,name:e,element:n,motionValue:i}=this;for(let s=0;s<t.length;s++)if(null===t[s])if(0===s){const s=null==i?void 0:i.get(),o=t[t.length-1];if(void 0!==s)t[0]=s;else if(n&&e){const i=n.readValue(e,o);null!=i&&(t[0]=i)}void 0===t[0]&&(t[0]=o),i&&void 0===s&&i.set(t[0])}else t[s]=t[s-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),Bn.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,Bn.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}const Nn=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),$n=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function zn(t,e,n=1){const[i,s]=function(t){const e=$n.exec(t);if(!e)return[,];const[,n,i,s]=e;return[`--${null!=n?n:i}`,s]}(t);if(!i)return;const o=window.getComputedStyle(e).getPropertyValue(i);if(o){const t=o.trim();return Nn(t)?parseFloat(t):t}return Ft(s)?zn(s,e,n+1):s}const Hn=t=>e=>e.test(t),Yn=[Wt,Kt,Yt,Ht,Gt,Xt,{test:t=>"auto"===t,parse:t=>t}],Kn=t=>Yn.find(Hn(t));class Xn extends Wn{constructor(t,e,n,i,s){super(t,e,n,i,s,!0)}readKeyframes(){const{unresolvedKeyframes:t,element:e,name:n}=this;if(!e||!e.current)return;super.readKeyframes();for(let a=0;a<t.length;a++){let n=t[a];if("string"==typeof n&&(n=n.trim(),Ft(n))){const i=zn(n,e.current);void 0!==i&&(t[a]=i),a===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!Ce.has(n)||2!==t.length)return;const[i,s]=t,o=Kn(i),r=Kn(s);if(o!==r)if(Mn(o)&&Mn(r))for(let a=0;a<t.length;a++){const e=t[a];"string"==typeof e&&(t[a]=parseFloat(e))}else this.needsMeasurement=!0}resolveNoneKeyframes(){const{unresolvedKeyframes:t,name:e}=this,n=[];for(let s=0;s<t.length;s++)("number"==typeof(i=t[s])?0===i:null===i||"none"===i||"0"===i||tn(i))&&n.push(s);var i;n.length&&function(t,e,n){let i,s=0;for(;s<t.length&&!i;){const e=t[s];"string"==typeof e&&!Cn.has(e)&&gn(e).values.length&&(i=t[s]),s++}if(i&&n)for(const o of e)t[o]=Vn(n,i)}(t,n,e)}measureInitialState(){const{element:t,unresolvedKeyframes:e,name:n}=this;if(!t||!t.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=jn[n](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;const i=e[e.length-1];void 0!==i&&t.getValue(n,i).jump(i,!1)}measureEndState(){var t;const{element:e,name:n,unresolvedKeyframes:i}=this;if(!e||!e.current)return;const s=e.getValue(n);s&&s.jump(this.measuredOrigin,!1);const o=i.length-1,r=i[o];i[o]=jn[n](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),(null===(t=this.removedTransforms)||void 0===t?void 0:t.length)&&this.removedTransforms.forEach((([t,n])=>{e.getValue(t).set(n)})),this.resolveNoneKeyframes()}}const Gn=(t,e)=>"zIndex"!==e&&(!("number"!=typeof t&&!Array.isArray(t))||!("string"!=typeof t||!Pn.test(t)&&"0"!==t||t.startsWith("url(")));const qn=t=>null!==t;function Zn(t,{repeat:e,repeatType:n="loop"},i){const s=t.filter(qn),o=e&&"loop"!==n&&e%2==1?0:s.length-1;return o&&void 0!==i?i:s[o]}class _n{constructor({autoplay:t=!0,delay:e=0,type:n="keyframes",repeat:i=0,repeatDelay:s=0,repeatType:o="loop",...r}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=Re.now(),this.options={autoplay:t,delay:e,type:n,repeat:i,repeatDelay:s,repeatType:o,...r},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(Un(),In()),this._resolved}onKeyframesResolved(t,e){this.resolvedAt=Re.now(),this.hasAttemptedResolve=!0;const{name:n,type:s,velocity:o,delay:r,onComplete:a,onUpdate:l,isGenerator:u}=this.options;if(!u&&!function(t,e,n,s){const o=t[0];if(null===o)return!1;if("display"===e||"visibility"===e)return!0;const r=t[t.length-1],a=Gn(o,e),l=Gn(r,e);return!(!a||!l)&&(function(t){const e=t[0];if(1===t.length)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}(t)||("spring"===n||i(n))&&s)}(t,n,s,o)){if(!r)return l&&l(Zn(t,this.options,e)),a&&a(),void this.resolveFinishedPromise();this.options.duration=0}const c=this.initPlayback(t,e);!1!==c&&(this._resolved={keyframes:t,finalKeyframe:e,...c},this.onPostResolved())}onPostResolved(){}then(t,e){return this.currentFinishedPromise.then(t,e)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise((t=>{this.resolveFinishedPromise=t}))}}const Jn=(t,e,n)=>t+(e-t)*n;function Qn(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function ti(t,e){return n=>n>0?e:t}const ei=(t,e,n)=>{const i=t*t,s=n*(e*e-i)+i;return s<0?0:Math.sqrt(s)},ni=[un,ln,cn];function ii(t){const e=(n=t,ni.find((t=>t.test(n))));var n;if(!Boolean(e))return!1;let i=e.parse(t);return e===cn&&(i=function({hue:t,saturation:e,lightness:n,alpha:i}){t/=360,n/=100;let s=0,o=0,r=0;if(e/=100){const i=n<.5?n*(1+e):n+e-n*e,a=2*n-i;s=Qn(a,i,t+1/3),o=Qn(a,i,t),r=Qn(a,i,t-1/3)}else s=o=r=n;return{red:Math.round(255*s),green:Math.round(255*o),blue:Math.round(255*r),alpha:i}}(i)),i}const si=(t,e)=>{const n=ii(t),i=ii(e);if(!n||!i)return ti(t,e);const s={...n};return t=>(s.red=ei(n.red,i.red,t),s.green=ei(n.green,i.green,t),s.blue=ei(n.blue,i.blue,t),s.alpha=Jn(n.alpha,i.alpha,t),ln.transform(s))},oi=(t,e)=>n=>e(t(n)),ri=(...t)=>t.reduce(oi),ai=new Set(["none","hidden"]);function li(t,e){return n=>Jn(t,e,n)}function ui(t){return"number"==typeof t?li:"string"==typeof t?Ft(t)?ti:hn.test(t)?si:di:Array.isArray(t)?ci:"object"==typeof t?hn.test(t)?si:hi:ti}function ci(t,e){const n=[...t],i=n.length,s=t.map(((t,n)=>ui(t)(t,e[n])));return t=>{for(let e=0;e<i;e++)n[e]=s[e](t);return n}}function hi(t,e){const n={...t,...e},i={};for(const s in n)void 0!==t[s]&&void 0!==e[s]&&(i[s]=ui(t[s])(t[s],e[s]));return t=>{for(const e in i)n[e]=i[e](t);return n}}const di=(t,e)=>{const n=Pn.createTransformer(e),i=gn(t),s=gn(e);return i.indexes.var.length===s.indexes.var.length&&i.indexes.color.length===s.indexes.color.length&&i.indexes.number.length>=s.indexes.number.length?ai.has(t)&&!s.values.length||ai.has(e)&&!i.values.length?function(t,e){return ai.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}(t,e):ri(ci(function(t,e){var n;const i=[],s={color:0,var:0,number:0};for(let o=0;o<e.values.length;o++){const r=e.types[o],a=t.indexes[r][s[r]],l=null!==(n=t.values[a])&&void 0!==n?n:0;i[o]=l,s[r]++}return i}(i,s),s.values),n):ti(t,e)};function pi(t,e,n){if("number"==typeof t&&"number"==typeof e&&"number"==typeof n)return Jn(t,e,n);return ui(t)(t,e)}function mi(t,e,n){const i=Math.max(e-5,0);return Be(n-t(i),e-i)}const fi=100,gi=10,vi=1,yi=0,xi=800,Pi=.3,wi=.3,Ti={granular:.01,default:2},Si={granular:.005,default:.5},bi=.01,Ai=10,Ei=.05,Vi=1,Ci=.001;function Mi({duration:t=xi,bounce:e=Pi,velocity:n=yi,mass:i=vi}){let r,a,l=1-e;l=Ut(Ei,Vi,l),t=Ut(bi,Ai,s(t)),l<1?(r=e=>{const i=e*l,s=i*t,o=i-n,r=Ri(e,l),a=Math.exp(-s);return Ci-o/r*a},a=e=>{const i=e*l*t,s=i*n+n,o=Math.pow(l,2)*Math.pow(e,2)*t,a=Math.exp(-i),u=Ri(Math.pow(e,2),l);return(-r(e)+Ci>0?-1:1)*((s-o)*a)/u}):(r=e=>Math.exp(-e*t)*((e-n)*t+1)-.001,a=e=>Math.exp(-e*t)*(t*t*(n-e)));const u=function(t,e,n){let i=n;for(let s=1;s<Di;s++)i-=t(i)/e(i);return i}(r,a,5/t);if(t=o(t),isNaN(u))return{stiffness:fi,damping:gi,duration:t};{const e=Math.pow(u,2)*i;return{stiffness:e,damping:2*l*Math.sqrt(i*e),duration:t}}}const Di=12;function Ri(t,e){return t*Math.sqrt(1-e*e)}const ki=["duration","bounce"],Li=["stiffness","damping","mass"];function ji(t,e){return e.some((e=>void 0!==t[e]))}function Bi(t=wi,e=Pi){const n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:i,restDelta:u}=n;const c=n.keyframes[0],h=n.keyframes[n.keyframes.length-1],d={done:!1,value:c},{stiffness:p,damping:m,mass:f,duration:g,velocity:v,isResolvedFromDuration:y}=function(t){let e={velocity:yi,stiffness:fi,damping:gi,mass:vi,isResolvedFromDuration:!1,...t};if(!ji(t,Li)&&ji(t,ki))if(t.visualDuration){const n=t.visualDuration,i=2*Math.PI/(1.2*n),s=i*i,o=2*Ut(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:vi,stiffness:s,damping:o}}else{const n=Mi(t);e={...e,...n,mass:vi},e.isResolvedFromDuration=!0}return e}({...n,velocity:-s(n.velocity||0)}),x=v||0,P=m/(2*Math.sqrt(p*f)),w=h-c,T=s(Math.sqrt(p/f)),S=Math.abs(w)<5;let b;if(i||(i=S?Ti.granular:Ti.default),u||(u=S?Si.granular:Si.default),P<1){const t=Ri(T,P);b=e=>{const n=Math.exp(-P*T*e);return h-n*((x+P*T*w)/t*Math.sin(t*e)+w*Math.cos(t*e))}}else if(1===P)b=t=>h-Math.exp(-T*t)*(w+(x+T*w)*t);else{const t=T*Math.sqrt(P*P-1);b=e=>{const n=Math.exp(-P*T*e),i=Math.min(t*e,300);return h-n*((x+P*T*w)*Math.sinh(i)+t*w*Math.cosh(i))/t}}const A={calculatedDuration:y&&g||null,next:t=>{const e=b(t);if(y)d.done=t>=g;else{let n=0;P<1&&(n=0===t?o(x):mi(b,t,e));const s=Math.abs(n)<=i,r=Math.abs(h-e)<=u;d.done=s&&r}return d.value=d.done?h:e,d},toString:()=>{const t=Math.min(r(A),a),e=l((e=>A.next(t*e).value),t,30);return t+"ms "+e}};return A}function Fi({keyframes:t,velocity:e=0,power:n=.8,timeConstant:i=325,bounceDamping:s=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:u=.5,restSpeed:c}){const h=t[0],d={done:!1,value:h},p=t=>void 0===a?l:void 0===l||Math.abs(a-t)<Math.abs(l-t)?a:l;let m=n*e;const f=h+m,g=void 0===r?f:r(f);g!==f&&(m=g-h);const v=t=>-m*Math.exp(-t/i),y=t=>g+v(t),x=t=>{const e=v(t),n=y(t);d.done=Math.abs(e)<=u,d.value=d.done?g:n};let P,w;const T=t=>{var e;(e=d.value,void 0!==a&&e<a||void 0!==l&&e>l)&&(P=t,w=Bi({keyframes:[d.value,p(d.value)],velocity:mi(y,t,d.value),damping:s,stiffness:o,restDelta:u,restSpeed:c}))};return T(0),{calculatedDuration:null,next:t=>{let e=!1;return w||void 0!==P||(e=!0,x(t),T(t)),void 0!==P&&t>=P?w.next(t-P):(!e&&x(t),d)}}}const Oi=He(.42,0,1,1),Ii=He(0,0,.58,1),Ui=He(.42,0,.58,1),Wi={linear:n,easeIn:Oi,easeInOut:Ui,easeOut:Ii,circIn:_e,circInOut:Qe,circOut:Je,backIn:Ge,backInOut:qe,backOut:Xe,anticipate:Ze},Ni=t=>{if(u(t)){c(4===t.length);const[e,n,i,s]=t;return He(e,n,i,s)}return"string"==typeof t?(c(void 0!==Wi[t]),Wi[t]):t};function $i(t,e,{clamp:i=!0,ease:s,mixer:o}={}){const r=t.length;if(c(r===e.length),1===r)return()=>e[0];if(2===r&&e[0]===e[1])return()=>e[1];const a=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());const l=function(t,e,i){const s=[],o=i||pi,r=t.length-1;for(let a=0;a<r;a++){let i=o(t[a],t[a+1]);if(e){const t=Array.isArray(e)?e[a]||n:e;i=ri(t,i)}s.push(i)}return s}(e,s,o),u=l.length,d=n=>{if(a&&n<t[0])return e[0];let i=0;if(u>1)for(;i<t.length-2&&!(n<t[i+1]);i++);const s=h(t[i],t[i+1],n);return l[i](s)};return i?e=>d(Ut(t[0],t[r-1],e)):d}function zi(t){const e=[0];return function(t,e){const n=t[t.length-1];for(let i=1;i<=e;i++){const s=h(0,e,i);t.push(Jn(n,1,s))}}(e,t.length-1),e}function Hi({duration:t=300,keyframes:e,times:n,ease:i="easeInOut"}){const s=(t=>Array.isArray(t)&&"number"!=typeof t[0])(i)?i.map(Ni):Ni(i),o={done:!1,value:e[0]},r=function(t,e){return t.map((t=>t*e))}(n&&n.length===e.length?n:zi(e),t),a=$i(r,e,{ease:Array.isArray(s)?s:(l=e,u=s,l.map((()=>u||Ui)).splice(0,l.length-1))});var l,u;return{calculatedDuration:t,next:e=>(o.value=a(e),o.done=e>=t,o)}}const Yi=t=>{const e=({timestamp:e})=>t(e);return{start:()=>z.update(e,!0),stop:()=>H(e),now:()=>Y.isProcessing?Y.timestamp:Re.now()}},Ki={decay:Fi,inertia:Fi,tween:Hi,keyframes:Hi,spring:Bi},Xi=t=>t/100;class Gi extends _n{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();const{onStop:t}=this.options;t&&t()};const{name:e,motionValue:n,element:i,keyframes:s}=this.options,o=(null==i?void 0:i.KeyframeResolver)||Wn;this.resolver=new o(s,((t,e)=>this.onKeyframesResolved(t,e)),e,n,i),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){const{type:e="keyframes",repeat:n=0,repeatDelay:s=0,repeatType:o,velocity:a=0}=this.options,l=i(e)?e:Ki[e]||Hi;let u,c;l!==Hi&&"number"!=typeof t[0]&&(u=ri(Xi,pi(t[0],t[1])),t=[0,100]);const h=l({...this.options,keyframes:t});"mirror"===o&&(c=l({...this.options,keyframes:[...t].reverse(),velocity:-a})),null===h.calculatedDuration&&(h.calculatedDuration=r(h));const{calculatedDuration:d}=h,p=d+s;return{generator:h,mirroredGenerator:c,mapPercentToKeyframes:u,calculatedDuration:d,resolvedDuration:p,totalDuration:p*(n+1)-s}}onPostResolved(){const{autoplay:t=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&t?this.state=this.pendingPlayState:this.pause()}tick(t,e=!1){const{resolved:n}=this;if(!n){const{keyframes:t}=this.options;return{done:!0,value:t[t.length-1]}}const{finalKeyframe:i,generator:s,mirroredGenerator:o,mapPercentToKeyframes:r,keyframes:a,calculatedDuration:l,totalDuration:u,resolvedDuration:c}=n;if(null===this.startTime)return s.next(0);const{delay:h,repeat:d,repeatType:p,repeatDelay:m,onUpdate:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-u/this.speed,this.startTime)),e?this.currentTime=t:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;const g=this.currentTime-h*(this.speed>=0?1:-1),v=this.speed>=0?g<0:g>u;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let y=this.currentTime,x=s;if(d){const t=Math.min(this.currentTime,u)/c;let e=Math.floor(t),n=t%1;!n&&t>=1&&(n=1),1===n&&e--,e=Math.min(e,d+1);Boolean(e%2)&&("reverse"===p?(n=1-n,m&&(n-=m/c)):"mirror"===p&&(x=o)),y=Ut(0,1,n)*c}const P=v?{done:!1,value:a[0]}:x.next(y);r&&(P.value=r(P.value));let{done:w}=P;v||null===l||(w=this.speed>=0?this.currentTime>=u:this.currentTime<=0);const T=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return T&&void 0!==i&&(P.value=Zn(a,this.options,i)),f&&f(P.value),T&&this.finish(),P}get duration(){const{resolved:t}=this;return t?s(t.calculatedDuration):0}get time(){return s(this.currentTime)}set time(t){t=o(t),this.currentTime=t,null!==this.holdTime||0===this.speed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){const e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=s(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved)return void(this.pendingPlayState="running");if(this.isStopped)return;const{driver:t=Yi,onPlay:e,startTime:n}=this.options;this.driver||(this.driver=t((t=>this.tick(t)))),e&&e();const i=this.driver.now();null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=i):this.startTime=null!=n?n:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;this._resolved?(this.state="paused",this.holdTime=null!==(t=this.currentTime)&&void 0!==t?t:0):this.pendingPlayState="paused"}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";const{onComplete:t}=this.options;t&&t()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}const qi=new Set(["opacity","clipPath","filter","transform"]);const Zi=p((()=>Object.hasOwnProperty.call(Element.prototype,"animate")));const _i={anticipate:Ze,backInOut:qe,circInOut:Qe};class Ji extends _n{constructor(t){super(t);const{name:e,motionValue:n,element:i,keyframes:s}=this.options;this.resolver=new Xn(s,((t,e)=>this.onKeyframesResolved(t,e)),e,n,i),this.resolver.scheduleResolve()}initPlayback(t,e){let{duration:n=300,times:s,ease:o,type:r,motionValue:a,name:l,startTime:u}=this.options;if(!a.owner||!a.owner.current)return!1;var c;if("string"==typeof o&&m()&&o in _i&&(o=_i[o]),c=this.options,i(c.type)||"spring"===c.type||!g(c.ease)){const{onComplete:e,onUpdate:i,motionValue:a,element:l,...u}=this.options,c=function(t,e){const n=new Gi({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0});let i={done:!1,value:t[0]};const s=[];let o=0;for(;!i.done&&o<2e4;)i=n.sample(o),s.push(i.value),o+=10;return{times:void 0,keyframes:s,duration:o-10,ease:"linear"}}(t,u);1===(t=c.keyframes).length&&(t[1]=t[0]),n=c.duration,s=c.times,o=c.ease,r="keyframes"}const h=function(t,e,n,{delay:i=0,duration:s=300,repeat:o=0,repeatType:r="loop",ease:a="easeInOut",times:l}={}){const u={[e]:n};l&&(u.offset=l);const c=d(a,s);return Array.isArray(c)&&(u.easing=c),t.animate(u,{delay:i,duration:s,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:o+1,direction:"reverse"===r?"alternate":"normal"})}(a.owner.current,l,t,{...this.options,duration:n,times:s,ease:o});return h.startTime=null!=u?u:this.calcStartTime(),this.pendingTimeline?(f(h,this.pendingTimeline),this.pendingTimeline=void 0):h.onfinish=()=>{const{onComplete:n}=this.options;a.set(Zn(t,this.options,e)),n&&n(),this.cancel(),this.resolveFinishedPromise()},{animation:h,duration:n,times:s,type:r,ease:o,keyframes:t}}get duration(){const{resolved:t}=this;if(!t)return 0;const{duration:e}=t;return s(e)}get time(){const{resolved:t}=this;if(!t)return 0;const{animation:e}=t;return s(e.currentTime||0)}set time(t){const{resolved:e}=this;if(!e)return;const{animation:n}=e;n.currentTime=o(t)}get speed(){const{resolved:t}=this;if(!t)return 1;const{animation:e}=t;return e.playbackRate}set speed(t){const{resolved:e}=this;if(!e)return;const{animation:n}=e;n.playbackRate=t}get state(){const{resolved:t}=this;if(!t)return"idle";const{animation:e}=t;return e.playState}get startTime(){const{resolved:t}=this;if(!t)return null;const{animation:e}=t;return e.startTime}attachTimeline(t){if(this._resolved){const{resolved:e}=this;if(!e)return n;const{animation:i}=e;f(i,t)}else this.pendingTimeline=t;return n}play(){if(this.isStopped)return;const{resolved:t}=this;if(!t)return;const{animation:e}=t;"finished"===e.playState&&this.updateFinishedPromise(),e.play()}pause(){const{resolved:t}=this;if(!t)return;const{animation:e}=t;e.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();const{resolved:t}=this;if(!t)return;const{animation:e,keyframes:n,duration:i,type:s,ease:r,times:a}=t;if("idle"===e.playState||"finished"===e.playState)return;if(this.time){const{motionValue:t,onUpdate:e,onComplete:l,element:u,...c}=this.options,h=new Gi({...c,keyframes:n,duration:i,type:s,ease:r,times:a,isGenerator:!0}),d=o(this.time);t.setWithVelocity(h.sample(d-10).value,h.sample(d).value,10)}const{onStop:l}=this.options;l&&l(),this.cancel()}complete(){const{resolved:t}=this;t&&t.animation.finish()}cancel(){const{resolved:t}=this;t&&t.animation.cancel()}static supports(t){const{motionValue:e,name:n,repeatDelay:i,repeatType:s,damping:o,type:r}=t;if(!(e&&e.owner&&e.owner.current instanceof HTMLElement))return!1;const{onUpdate:a,transformTemplate:l}=e.owner.getProps();return Zi()&&n&&qi.has(n)&&!a&&!l&&!i&&"mirror"!==s&&0!==o&&"inertia"!==r}}const Qi={type:"spring",stiffness:500,damping:25,restSpeed:10},ts={type:"keyframes",duration:.8},es={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ns=(t,{keyframes:e})=>e.length>2?ts:kt.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:Qi:es;const is=(t,e,n,i={},s,r)=>a=>{const l=v(i,t)||{},u=l.delay||i.delay||0;let{elapsed:c=0}=i;c-=o(u);let h={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...l,delay:-c,onUpdate:t=>{e.set(t),l.onUpdate&&l.onUpdate(t)},onComplete:()=>{a(),l.onComplete&&l.onComplete()},name:t,motionValue:e,element:r?void 0:s};(function({when:t,delay:e,delayChildren:n,staggerChildren:i,staggerDirection:s,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length})(l)||(h={...h,...ns(t,h)}),h.duration&&(h.duration=o(h.duration)),h.repeatDelay&&(h.repeatDelay=o(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let d=!1;if((!1===h.type||0===h.duration&&!h.repeatDelay)&&(h.duration=0,0===h.delay&&(d=!0)),d&&!r&&void 0!==e.get()){const t=Zn(h.keyframes,l);if(void 0!==t)return z.update((()=>{h.onUpdate(t),h.onComplete()})),new y([])}return!r&&Ji.supports(h)?new Ji(h):new Gi(h)};function ss({protectedKeys:t,needsAnimating:e},n){const i=t.hasOwnProperty(n)&&!0!==e[n];return e[n]=!1,i}function os(t,e,{delay:n=0,transitionOverride:i,type:s}={}){var o;let{transition:r=t.getDefaultTransition(),transitionEnd:a,...l}=e;i&&(r=i);const u=[],c=s&&t.animationState&&t.animationState.getState()[s];for(const h in l){const e=t.getValue(h,null!==(o=t.latestValues[h])&&void 0!==o?o:null),i=l[h];if(void 0===i||c&&ss(c,h))continue;const s={delay:n,...v(r||{},h)};let a=!1;if(window.MotionHandoffAnimation){const e=$e(t);if(e){const t=window.MotionHandoffAnimation(e,h,z);null!==t&&(s.startTime=t,a=!0)}}Ne(t,h),e.start(is(h,e,i,t.shouldReduceMotion&&Ce.has(h)?{type:!1}:s,t,a));const d=e.animation;d&&u.push(d)}return a&&Promise.all(u).then((()=>{z.update((()=>{a&&We(t,a)}))})),u}function rs(t,e,n={}){var i;const s=Ve(t,e,"exit"===n.type?null===(i=t.presenceContext)||void 0===i?void 0:i.custom:void 0);let{transition:o=t.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(o=n.transitionOverride);const r=s?()=>Promise.all(os(t,s,n)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(i=0)=>{const{delayChildren:s=0,staggerChildren:r,staggerDirection:a}=o;return function(t,e,n=0,i=0,s=1,o){const r=[],a=(t.variantChildren.size-1)*i,l=1===s?(t=0)=>t*i:(t=0)=>a-t*i;return Array.from(t.variantChildren).sort(as).forEach(((t,i)=>{t.notify("AnimationStart",e),r.push(rs(t,e,{...o,delay:n+l(i)}).then((()=>t.notify("AnimationComplete",e))))})),Promise.all(r)}(t,e,s+i,r,a,n)}:()=>Promise.resolve(),{when:l}=o;if(l){const[t,e]="beforeChildren"===l?[r,a]:[a,r];return t().then((()=>e()))}return Promise.all([r(),a(n.delay)])}function as(t,e){return t.sortNodePosition(e)}function ls(t,e,n={}){let i;if(t.notify("AnimationStart",e),Array.isArray(e)){const s=e.map((e=>rs(t,e,n)));i=Promise.all(s)}else if("string"==typeof e)i=rs(t,e,n);else{const s="function"==typeof e?Ve(t,e,n.custom):e;i=Promise.all(os(t,s,n))}return i.then((()=>{t.notify("AnimationComplete",e)}))}const us=ot.length;function cs(t){if(!t)return;if(!t.isControllingVariants){const e=t.parent&&cs(t.parent)||{};return void 0!==t.props.initial&&(e.initial=t.props.initial),e}const e={};for(let n=0;n<us;n++){const i=ot[n],s=t.props[i];(nt(s)||!1===s)&&(e[i]=s)}return e}const hs=[...st].reverse(),ds=st.length;function ps(t){let e=function(t){return e=>Promise.all(e.map((({animation:e,options:n})=>ls(t,e,n))))}(t),n=gs(),i=!0;const s=e=>(n,i)=>{var s;const o=Ve(t,i,"exit"===e?null===(s=t.presenceContext)||void 0===s?void 0:s.custom:void 0);if(o){const{transition:t,transitionEnd:e,...i}=o;n={...n,...i,...e}}return n};function o(o){const{props:r}=t,a=cs(t.parent)||{},l=[],u=new Set;let c={},h=1/0;for(let e=0;e<ds;e++){const d=hs[e],p=n[d],m=void 0!==r[d]?r[d]:a[d],f=nt(m),g=d===o?p.isActive:null;!1===g&&(h=e);let v=m===a[d]&&m!==r[d]&&f;if(v&&i&&t.manuallyAnimateOnMount&&(v=!1),p.protectedKeys={...c},!p.isActive&&null===g||!m&&!p.prevProp||it(m)||"boolean"==typeof m)continue;const y=ms(p.prevProp,m);let x=y||d===o&&p.isActive&&!v&&f||e>h&&f,P=!1;const w=Array.isArray(m)?m:[m];let T=w.reduce(s(d),{});!1===g&&(T={});const{prevResolvedValues:S={}}=p,b={...S,...T},A=e=>{x=!0,u.has(e)&&(P=!0,u.delete(e)),p.needsAnimating[e]=!0;const n=t.getValue(e);n&&(n.liveStyle=!1)};for(const t in b){const e=T[t],n=S[t];if(c.hasOwnProperty(t))continue;let i=!1;i=Et(e)&&Et(n)?!Ee(e,n):e!==n,i?null!=e?A(t):u.add(t):void 0!==e&&u.has(t)?A(t):p.protectedKeys[t]=!0}p.prevProp=m,p.prevResolvedValues=T,p.isActive&&(c={...c,...T}),i&&t.blockInitialAnimation&&(x=!1);x&&(!(v&&y)||P)&&l.push(...w.map((t=>({animation:t,options:{type:d}}))))}if(u.size){const e={};u.forEach((n=>{const i=t.getBaseTarget(n),s=t.getValue(n);s&&(s.liveStyle=!0),e[n]=null!=i?i:null})),l.push({animation:e})}let d=Boolean(l.length);return!i||!1!==r.initial&&r.initial!==r.animate||t.manuallyAnimateOnMount||(d=!1),i=!1,d?e(l):Promise.resolve()}return{animateChanges:o,setActive:function(e,i){var s;if(n[e].isActive===i)return Promise.resolve();null===(s=t.variantChildren)||void 0===s||s.forEach((t=>{var n;return null===(n=t.animationState)||void 0===n?void 0:n.setActive(e,i)})),n[e].isActive=i;const r=o(e);for(const t in n)n[t].protectedKeys={};return r},setAnimateFunction:function(n){e=n(t)},getState:()=>n,reset:()=>{n=gs(),i=!0}}}function ms(t,e){return"string"==typeof e?e!==t:!!Array.isArray(e)&&!Ee(e,t)}function fs(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function gs(){return{animate:fs(!0),whileInView:fs(),whileHover:fs(),whileTap:fs(),whileDrag:fs(),whileFocus:fs(),exit:fs()}}class vs{constructor(t){this.isMounted=!1,this.node=t}update(){}}let ys=0;const xs={animation:{Feature:class extends vs{constructor(t){super(t),t.animationState||(t.animationState=ps(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();it(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null===(t=this.unmountControls)||void 0===t||t.call(this)}}},exit:{Feature:class extends vs{constructor(){super(...arguments),this.id=ys++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===n)return;const i=this.node.animationState.setActive("exit",!t);e&&!t&&i.then((()=>e(this.id)))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}}};function Ps(t,e,n,i={passive:!0}){return t.addEventListener(e,n,i),()=>t.removeEventListener(e,n)}function ws(t){return{point:{x:t.pageX,y:t.pageY}}}function Ts(t,e,n,i){return Ps(t,e,(t=>e=>x(e)&&t(e,ws(e)))(n),i)}const Ss=(t,e)=>Math.abs(t-e);class bs{constructor(t,e,{transformPagePoint:n,contextWindow:i,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!this.lastMoveEvent||!this.lastMoveEventInfo)return;const t=Vs(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,n=function(t,e){const n=Ss(t.x,e.x),i=Ss(t.y,e.y);return Math.sqrt(n**2+i**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!n)return;const{point:i}=t,{timestamp:s}=Y;this.history.push({...i,timestamp:s});const{onStart:o,onMove:r}=this.handlers;e||(o&&o(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),r&&r(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=As(e,this.transformPagePoint),z.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();const{onEnd:n,onSessionEnd:i,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!this.lastMoveEvent||!this.lastMoveEventInfo)return;const o=Vs("pointercancel"===t.type?this.lastMoveEventInfo:As(e,this.transformPagePoint),this.history);this.startEvent&&n&&n(t,o),i&&i(t,o)},!x(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=n,this.contextWindow=i||window;const o=As(ws(t),this.transformPagePoint),{point:r}=o,{timestamp:a}=Y;this.history=[{...r,timestamp:a}];const{onSessionStart:l}=e;l&&l(t,Vs(o,this.history)),this.removeListeners=ri(Ts(this.contextWindow,"pointermove",this.handlePointerMove),Ts(this.contextWindow,"pointerup",this.handlePointerUp),Ts(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),H(this.updatePoint)}}function As(t,e){return e?{point:e(t.point)}:t}function Es(t,e){return{x:t.x-e.x,y:t.y-e.y}}function Vs({point:t},e){return{point:t,delta:Es(t,Ms(e)),offset:Es(t,Cs(e)),velocity:Ds(e,.1)}}function Cs(t){return t[0]}function Ms(t){return t[t.length-1]}function Ds(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,i=null;const r=Ms(t);for(;n>=0&&(i=t[n],!(r.timestamp-i.timestamp>o(e)));)n--;if(!i)return{x:0,y:0};const a=s(r.timestamp-i.timestamp);if(0===a)return{x:0,y:0};const l={x:(r.x-i.x)/a,y:(r.y-i.y)/a};return l.x===1/0&&(l.x=0),l.y===1/0&&(l.y=0),l}function Rs(t){return t.max-t.min}function ks(t,e,n,i=.5){t.origin=i,t.originPoint=Jn(e.min,e.max,t.origin),t.scale=Rs(n)/Rs(e),t.translate=Jn(n.min,n.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function Ls(t,e,n,i){ks(t.x,e.x,n.x,i?i.originX:void 0),ks(t.y,e.y,n.y,i?i.originY:void 0)}function js(t,e,n){t.min=n.min+e.min,t.max=t.min+Rs(e)}function Bs(t,e,n){t.min=e.min-n.min,t.max=t.min+Rs(e)}function Fs(t,e,n){Bs(t.x,e.x,n.x),Bs(t.y,e.y,n.y)}function Os(t,e,n){return{min:void 0!==e?t.min+e:void 0,max:void 0!==n?t.max+n-(t.max-t.min):void 0}}function Is(t,e){let n=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,i]=[i,n]),{min:n,max:i}}const Us=.35;function Ws(t,e,n){return{min:Ns(t,e),max:Ns(t,n)}}function Ns(t,e){return"number"==typeof t?t:t[e]||0}const $s=()=>({x:{min:0,max:0},y:{min:0,max:0}});function zs(t){return[t("x"),t("y")]}function Hs({top:t,left:e,right:n,bottom:i}){return{x:{min:e,max:n},y:{min:t,max:i}}}function Ys(t){return void 0===t||1===t}function Ks({scale:t,scaleX:e,scaleY:n}){return!Ys(t)||!Ys(e)||!Ys(n)}function Xs(t){return Ks(t)||Gs(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function Gs(t){return qs(t.x)||qs(t.y)}function qs(t){return t&&"0%"!==t}function Zs(t,e,n){return n+e*(t-n)}function _s(t,e,n,i,s){return void 0!==s&&(t=Zs(t,s,i)),Zs(t,n,i)+e}function Js(t,e=0,n=1,i,s){t.min=_s(t.min,e,n,i,s),t.max=_s(t.max,e,n,i,s)}function Qs(t,{x:e,y:n}){Js(t.x,e.translate,e.scale,e.originPoint),Js(t.y,n.translate,n.scale,n.originPoint)}const to=.999999999999,eo=1.0000000000001;function no(t,e){t.min=t.min+e,t.max=t.max+e}function io(t,e,n,i,s=.5){Js(t,e,n,Jn(t.min,t.max,s),i)}function so(t,e){io(t.x,e.x,e.scaleX,e.scale,e.originX),io(t.y,e.y,e.scaleY,e.scale,e.originY)}function oo(t,e){return Hs(function(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:i.y,right:i.x}}(t.getBoundingClientRect(),e))}const ro=({current:t})=>t?t.ownerDocument.defaultView:null,ao=new WeakMap;class lo{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic={x:{min:0,max:0},y:{min:0,max:0}},this.visualElement=t}start(t,{snapToCursor:e=!1}={}){const{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;const{dragSnapToOrigin:i}=this.getProps();this.panSession=new bs(t,{onSessionStart:t=>{const{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(ws(t).point)},onStart:(t,e)=>{const{drag:n,dragPropagation:i,onDragStart:s}=this.getProps();if(n&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock=P(n),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),zs((t=>{let e=this.getAxisMotionValue(t).get()||0;if(Yt.test(e)){const{projection:n}=this.visualElement;if(n&&n.layout){const i=n.layout.layoutBox[t];if(i){e=Rs(i)*(parseFloat(e)/100)}}}this.originPoint[t]=e})),s&&z.postRender((()=>s(t,e))),Ne(this.visualElement,"transform");const{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(t,e)=>{const{dragPropagation:n,dragDirectionLock:i,onDirectionLock:s,onDrag:o}=this.getProps();if(!n&&!this.openDragLock)return;const{offset:r}=e;if(i&&null===this.currentDirection)return this.currentDirection=function(t,e=10){let n=null;Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x");return n}(r),void(null!==this.currentDirection&&s&&s(this.currentDirection));this.updateAxis("x",e.point,r),this.updateAxis("y",e.point,r),this.visualElement.render(),o&&o(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>zs((t=>{var e;return"paused"===this.getAnimationState(t)&&(null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.play())}))},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:ro(this.visualElement)})}stop(t,e){const n=this.isDragging;if(this.cancel(),!n)return;const{velocity:i}=e;this.startAnimation(i);const{onDragEnd:s}=this.getProps();s&&z.postRender((()=>s(t,e)))}cancel(){this.isDragging=!1;const{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,n){const{drag:i}=this.getProps();if(!n||!uo(t,i,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+n[t];this.constraints&&this.constraints[t]&&(o=function(t,{min:e,max:n},i){return void 0!==e&&t<e?t=i?Jn(e,t,i.min):Math.max(t,e):void 0!==n&&t>n&&(t=i?Jn(n,t,i.max):Math.min(t,n)),t}(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){var t;const{dragConstraints:e,dragElastic:n}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(t=this.visualElement.projection)||void 0===t?void 0:t.layout,s=this.constraints;e&&ht(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):this.constraints=!(!e||!i)&&function(t,{top:e,left:n,bottom:i,right:s}){return{x:Os(t.x,n,s),y:Os(t.y,e,i)}}(i.layoutBox,e),this.elastic=function(t=Us){return!1===t?t=0:!0===t&&(t=Us),{x:Ws(t,"left","right"),y:Ws(t,"top","bottom")}}(n),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&zs((t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){const n={};return void 0!==e.min&&(n.min=e.min-t.min),void 0!==e.max&&(n.max=e.max-t.min),n}(i.layoutBox[t],this.constraints[t]))}))}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:e}=this.getProps();if(!t||!ht(t))return!1;const n=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const s=function(t,e,n){const i=oo(t,n),{scroll:s}=e;return s&&(no(i.x,s.offset.x),no(i.y,s.offset.y)),i}(n,i.root,this.visualElement.getTransformPagePoint());let o=function(t,e){return{x:Is(t.x,e.x),y:Is(t.y,e.y)}}(i.layout.layoutBox,s);if(e){const t=e(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=Hs(t))}return o}startAnimation(t){const{drag:e,dragMomentum:n,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:r}=this.getProps(),a=this.constraints||{},l=zs((r=>{if(!uo(r,e,this.currentDirection))return;let l=a&&a[r]||{};o&&(l={min:0,max:0});const u=i?200:1e6,c=i?40:1e7,h={type:"inertia",velocity:n?t[r]:0,bounceStiffness:u,bounceDamping:c,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(r,h)}));return Promise.all(l).then(r)}startAxisValueAnimation(t,e){const n=this.getAxisMotionValue(t);return Ne(this.visualElement,t),n.start(is(t,n,0,e,this.visualElement,!1))}stopAnimation(){zs((t=>this.getAxisMotionValue(t).stop()))}pauseAnimation(){zs((t=>{var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.pause()}))}getAnimationState(t){var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.state}getAxisMotionValue(t){const e=`_drag${t.toUpperCase()}`,n=this.visualElement.getProps(),i=n[e];return i||this.visualElement.getValue(t,(n.initial?n.initial[t]:void 0)||0)}snapToCursor(t){zs((e=>{const{drag:n}=this.getProps();if(!uo(e,n,this.currentDirection))return;const{projection:i}=this.visualElement,s=this.getAxisMotionValue(e);if(i&&i.layout){const{min:n,max:o}=i.layout.layoutBox[e];s.set(t[e]-Jn(n,o,.5))}}))}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:e}=this.getProps(),{projection:n}=this.visualElement;if(!ht(e)||!n||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};zs((t=>{const e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){const n=e.get();i[t]=function(t,e){let n=.5;const i=Rs(t),s=Rs(e);return s>i?n=h(e.min,e.max-i,t.min):i>s&&(n=h(t.min,t.max-s,e.min)),Ut(0,1,n)}({min:n,max:n},this.constraints[t])}}));const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),zs((e=>{if(!uo(e,t,null))return;const n=this.getAxisMotionValue(e),{min:s,max:o}=this.constraints[e];n.set(Jn(s,o,i[e]))}))}addListeners(){if(!this.visualElement.current)return;ao.set(this.visualElement,this);const t=Ts(this.visualElement.current,"pointerdown",(t=>{const{drag:e,dragListener:n=!0}=this.getProps();e&&n&&this.start(t)})),e=()=>{const{dragConstraints:t}=this.getProps();ht(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,i=n.addEventListener("measure",e);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),z.read(e);const s=Ps(window,"resize",(()=>this.scalePositionWithinConstraints())),o=n.addEventListener("didUpdate",(({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(zs((e=>{const n=this.getAxisMotionValue(e);n&&(this.originPoint[e]+=t[e].translate,n.set(n.get()+t[e].translate))})),this.visualElement.render())}));return()=>{s(),t(),i(),o&&o()}}getProps(){const t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:n=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=Us,dragMomentum:r=!0}=t;return{...t,drag:e,dragDirectionLock:n,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:r}}}function uo(t,e,n){return!(!0!==e&&e!==t||null!==n&&n!==t)}const co=t=>(e,n)=>{t&&z.postRender((()=>t(e,n)))};const ho={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function po(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const mo={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!Kt.test(t))return t;t=parseFloat(t)}return`${po(t,e.target.x)}% ${po(t,e.target.y)}%`}},fo={correct:(t,{treeScale:e,projectionDelta:n})=>{const i=t,s=Pn.parse(t);if(s.length>5)return i;const o=Pn.createTransformer(t),r="number"!=typeof s[0]?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;s[0+r]/=a,s[1+r]/=l;const u=Jn(a,l,.5);return"number"==typeof s[2+r]&&(s[2+r]/=u),"number"==typeof s[3+r]&&(s[3+r]/=u),o(s)}};class go extends t.Component{componentDidMount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n,layoutId:i}=this.props,{projection:s}=t;var o;o=yo,Object.assign(pe,o),s&&(e.group&&e.group.add(s),n&&n.register&&i&&n.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",(()=>{this.safeToRemove()})),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),ho.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:e,visualElement:n,drag:i,isPresent:s}=this.props,o=n.projection;return o?(o.isPresent=s,i||t.layoutDependency!==e||void 0===e?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||z.postRender((()=>{const t=o.getStack();t&&t.members.length||this.safeToRemove()}))),null):null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),ft.postRender((()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()})))}componentWillUnmount(){const{visualElement:t,layoutGroup:e,switchLayoutGroup:n}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(i),n&&n.deregister&&n.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function vo(n){const[i,s]=j(),o=t.useContext(E);return e.jsx(go,{...n,layoutGroup:o,switchLayoutGroup:t.useContext(vt),isPresent:i,safeToRemove:s})}const yo={borderRadius:{...mo,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:mo,borderTopRightRadius:mo,borderBottomLeftRadius:mo,borderBottomRightRadius:mo,boxShadow:fo};const xo=(t,e)=>t.depth-e.depth;class Po{constructor(){this.children=[],this.isDirty=!1}add(t){ke(this.children,t),this.isDirty=!0}remove(t){Le(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(xo),this.isDirty=!1,this.children.forEach(t)}}const wo=["TopLeft","TopRight","BottomLeft","BottomRight"],To=wo.length,So=t=>"string"==typeof t?parseFloat(t):t,bo=t=>"number"==typeof t||Kt.test(t);function Ao(t,e){return void 0!==t[e]?t[e]:t.borderRadius}const Eo=Co(0,.5,Je),Vo=Co(.5,.95,n);function Co(t,e,n){return i=>i<t?0:i>e?1:n(h(t,e,i))}function Mo(t,e){t.min=e.min,t.max=e.max}function Do(t,e){Mo(t.x,e.x),Mo(t.y,e.y)}function Ro(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function ko(t,e,n,i,s){return t=Zs(t-=e,1/n,i),void 0!==s&&(t=Zs(t,1/s,i)),t}function Lo(t,e,[n,i,s],o,r){!function(t,e=0,n=1,i=.5,s,o=t,r=t){Yt.test(e)&&(e=parseFloat(e),e=Jn(r.min,r.max,e/100)-r.min);if("number"!=typeof e)return;let a=Jn(o.min,o.max,i);t===o&&(a-=e),t.min=ko(t.min,e,n,a,s),t.max=ko(t.max,e,n,a,s)}(t,e[n],e[i],e[s],e.scale,o,r)}const jo=["x","scaleX","originX"],Bo=["y","scaleY","originY"];function Fo(t,e,n,i){Lo(t.x,e,jo,n?n.x:void 0,i?i.x:void 0),Lo(t.y,e,Bo,n?n.y:void 0,i?i.y:void 0)}function Oo(t){return 0===t.translate&&1===t.scale}function Io(t){return Oo(t.x)&&Oo(t.y)}function Uo(t,e){return t.min===e.min&&t.max===e.max}function Wo(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function No(t,e){return Wo(t.x,e.x)&&Wo(t.y,e.y)}function $o(t){return Rs(t.x)/Rs(t.y)}function zo(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class Ho{constructor(){this.members=[]}add(t){ke(this.members,t),t.scheduleRender()}remove(t){if(Le(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){const e=this.members.findIndex((e=>t===e));if(0===e)return!1;let n;for(let i=e;i>=0;i--){const t=this.members[i];if(!1!==t.isPresent){n=t;break}}return!!n&&(this.promote(n),!0)}promote(t,e){const n=this.lead;if(t!==n&&(this.prevLead=n,this.lead=t,t.show(),n)){n.instance&&n.scheduleRender(),t.scheduleRender(),t.resumeFrom=n,e&&(t.resumeFrom.preserveOpacity=!0),n.snapshot&&(t.snapshot=n.snapshot,t.snapshot.latestValues=n.animationValues||n.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;!1===i&&n.hide()}}exitAnimationComplete(){this.members.forEach((t=>{const{options:e,resumingFrom:n}=t;e.onExitComplete&&e.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()}))}scheduleRender(){this.members.forEach((t=>{t.instance&&t.scheduleRender(!1)}))}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}const Yo={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},Ko="undefined"!=typeof window&&void 0!==window.MotionDebug,Xo=["","X","Y","Z"],Go={visibility:"hidden"};let qo=0;function Zo(t,e,n,i){const{latestValues:s}=e;s[t]&&(n[t]=s[t],e.setStaticValue(t,0),i&&(i[t]=0))}function _o(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=$e(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:e,layoutId:i}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",z,!(e||i))}const{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&_o(i)}function Jo({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:i,resetTransform:s}){return class{constructor(t={},n=(null==e?void 0:e())){this.id=qo++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,Ko&&(Yo.totalNodes=Yo.resolvedTargetDeltas=Yo.recalculatedProjection=0),this.nodes.forEach(er),this.nodes.forEach(lr),this.nodes.forEach(ur),this.nodes.forEach(nr),Ko&&window.MotionDebug.record(Yo)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new Po)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new je),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){const n=this.eventHandlers.get(t);n&&n.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,n=this.root.hasTreeAnimated){if(this.instance)return;var i;this.isSVG=(i=e)instanceof SVGElement&&"svg"!==i.tagName,this.instance=e;const{layoutId:s,layout:o,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),n&&(o||s)&&(this.isLayoutDirty=!0),t){let n;const i=()=>this.root.updateBlockedByResize=!1;t(e,(()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(t,e){const n=Re.now(),i=({timestamp:s})=>{const o=s-n;o>=e&&(H(i),t(o-e))};return z.read(i,!0),()=>H(i)}(i,250),ho.hasAnimatedSinceResize&&(ho.hasAnimatedSinceResize=!1,this.nodes.forEach(ar))}))}s&&this.root.registerSharedNode(s,this),!1!==this.options.animate&&r&&(s||o)&&this.addEventListener("didUpdate",(({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:n,layout:i})=>{if(this.isTreeAnimationBlocked())return this.target=void 0,void(this.relativeTarget=void 0);const s=this.options.transition||r.getDefaultTransition()||fr,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=r.getProps(),l=!this.targetLayout||!No(this.targetLayout,i)||n,u=!e&&n;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);const e={...v(s,"layout"),onPlay:o,onComplete:a};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||ar(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i}))}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,H(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(cr),this.animationId++)}getTransformTemplate(){const{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked())return void(this.options.onExitComplete&&this.options.onExitComplete());if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&_o(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let s=0;s<this.path.length;s++){const t=this.path[s];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}const{layoutId:e,layout:n}=this.options;if(void 0===e&&!n)return;const i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){this.updateScheduled=!1;if(this.isUpdateBlocked())return this.unblockUpdate(),this.clearAllSnapshots(),void this.nodes.forEach(sr);this.isUpdating||this.nodes.forEach(or),this.isUpdating=!1,this.nodes.forEach(rr),this.nodes.forEach(Qo),this.nodes.forEach(tr),this.clearAllSnapshots();const t=Re.now();Y.delta=Ut(0,1e3/60,t-Y.timestamp),Y.timestamp=t,Y.isProcessing=!0,K.update.process(Y),K.preRender.process(Y),K.render.process(Y),Y.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,ft.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(ir),this.sharedNodes.forEach(hr)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,z.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){z.postRender((()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()}))}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance)return;if(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead()||this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let n=0;n<this.path.length;n++){this.path[n].updateScroll()}const t=this.layout;this.layout=this.measure(!1),this.layoutCorrected={x:{min:0,max:0},y:{min:0,max:0}},this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=Boolean(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){const e=i(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;const t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!Io(this.projectionDelta),n=this.getTransformTemplate(),i=n?n(this.latestValues,""):void 0,o=i!==this.prevTransformTemplateValue;t&&(e||Xs(this.latestValues)||o)&&(s(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){const e=this.measurePageBox();let n=this.removeElementScroll(e);var i;return t&&(n=this.removeTransform(n)),yr((i=n).x),yr(i.y),{animationId:this.root.animationId,measuredBox:e,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){var t;const{visualElement:e}=this.options;if(!e)return{x:{min:0,max:0},y:{min:0,max:0}};const n=e.measureViewportBox();if(!((null===(t=this.scroll)||void 0===t?void 0:t.wasRoot)||this.path.some(Pr))){const{scroll:t}=this.root;t&&(no(n.x,t.offset.x),no(n.y,t.offset.y))}return n}removeElementScroll(t){var e;const n={x:{min:0,max:0},y:{min:0,max:0}};if(Do(n,t),null===(e=this.scroll)||void 0===e?void 0:e.wasRoot)return n;for(let i=0;i<this.path.length;i++){const e=this.path[i],{scroll:s,options:o}=e;e!==this.root&&s&&o.layoutScroll&&(s.wasRoot&&Do(n,t),no(n.x,s.offset.x),no(n.y,s.offset.y))}return n}applyTransform(t,e=!1){const n={x:{min:0,max:0},y:{min:0,max:0}};Do(n,t);for(let i=0;i<this.path.length;i++){const t=this.path[i];!e&&t.options.layoutScroll&&t.scroll&&t!==t.root&&so(n,{x:-t.scroll.offset.x,y:-t.scroll.offset.y}),Xs(t.latestValues)&&so(n,t.latestValues)}return Xs(this.latestValues)&&so(n,this.latestValues),n}removeTransform(t){const e={x:{min:0,max:0},y:{min:0,max:0}};Do(e,t);for(let n=0;n<this.path.length;n++){const t=this.path[n];if(!t.instance)continue;if(!Xs(t.latestValues))continue;Ks(t.latestValues)&&t.updateSnapshot();const i={x:{min:0,max:0},y:{min:0,max:0}};Do(i,t.measurePageBox()),Fo(e,t.latestValues,t.snapshot?t.snapshot.layoutBox:void 0,i)}return Xs(this.latestValues)&&Fo(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Y.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e;const n=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=n.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=n.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=n.isSharedProjectionDirty);const i=Boolean(this.resumingFrom)||this!==n;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:s,layoutId:o}=this.options;if(this.layout&&(s||o)){if(this.resolvedRelativeTargetAt=Y.timestamp,!this.targetDelta&&!this.relativeTarget){const t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Fs(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),Do(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){var r,a,l;if(this.target||(this.target={x:{min:0,max:0},y:{min:0,max:0}},this.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}}),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),r=this.target,a=this.relativeTarget,l=this.relativeParent.target,js(r.x,a.x,l.x),js(r.y,a.y,l.y)):this.targetDelta?(Boolean(this.resumingFrom)?this.target=this.applyTransform(this.layout.layoutBox):Do(this.target,this.layout.layoutBox),Qs(this.target,this.targetDelta)):Do(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const t=this.getClosestProjectingParent();t&&Boolean(t.resumingFrom)===Boolean(this.resumingFrom)&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget={x:{min:0,max:0},y:{min:0,max:0}},this.relativeTargetOrigin={x:{min:0,max:0},y:{min:0,max:0}},Fs(this.relativeTargetOrigin,this.target,t.target),Do(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}Ko&&Yo.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(this.parent&&!Ks(this.parent.latestValues)&&!Gs(this.parent.latestValues))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return Boolean((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;const e=this.getLead(),n=Boolean(this.resumingFrom)||this!==e;let i=!0;if((this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty))&&(i=!1),n&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===Y.timestamp&&(i=!1),i)return;const{layout:s,layoutId:o}=this.options;if(this.isTreeAnimating=Boolean(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!s&&!o)return;Do(this.layoutCorrected,this.layout.layoutBox);const r=this.treeScale.x,a=this.treeScale.y;!function(t,e,n,i=!1){const s=n.length;if(!s)return;let o,r;e.x=e.y=1;for(let a=0;a<s;a++){o=n[a],r=o.projectionDelta;const{visualElement:s}=o.options;s&&s.props.style&&"contents"===s.props.style.display||(i&&o.options.layoutScroll&&o.scroll&&o!==o.root&&so(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,Qs(t,r)),i&&Xs(o.latestValues)&&so(t,o.latestValues))}e.x<eo&&e.x>to&&(e.x=1),e.y<eo&&e.y>to&&(e.y=1)}(this.layoutCorrected,this.treeScale,this.path,n),!e.layout||e.target||1===this.treeScale.x&&1===this.treeScale.y||(e.target=e.layout.layoutBox,e.targetWithTransforms={x:{min:0,max:0},y:{min:0,max:0}});const{target:l}=e;l?(this.projectionDelta&&this.prevProjectionDelta?(Ro(this.prevProjectionDelta.x,this.projectionDelta.x),Ro(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),Ls(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===r&&this.treeScale.y===a&&zo(this.projectionDelta.x,this.prevProjectionDelta.x)&&zo(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),Ko&&Yo.recalculatedProjection++):this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender())}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){var e;if(null===(e=this.options.visualElement)||void 0===e||e.scheduleRender(),t){const t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDelta={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}},this.projectionDeltaWithTransform={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}}}setAnimationOrigin(t,e=!1){const n=this.snapshot,i=n?n.latestValues:{},s={...this.latestValues},o={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;const r={x:{min:0,max:0},y:{min:0,max:0}},a=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),l=this.getStack(),u=!l||l.members.length<=1,c=Boolean(a&&!u&&!0===this.options.crossfade&&!this.path.some(mr));let h;this.animationProgress=0,this.mixTargetDelta=e=>{const n=e/1e3;var l,d,p,m,f,g;dr(o.x,t.x,n),dr(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Fs(r,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=r,g=n,pr(p.x,m.x,f.x,g),pr(p.y,m.y,f.y,g),h&&(l=this.relativeTarget,d=h,Uo(l.x,d.x)&&Uo(l.y,d.y))&&(this.isProjectionDirty=!1),h||(h={x:{min:0,max:0},y:{min:0,max:0}}),Do(h,this.relativeTarget)),a&&(this.animationValues=s,function(t,e,n,i,s,o){s?(t.opacity=Jn(0,void 0!==n.opacity?n.opacity:1,Eo(i)),t.opacityExit=Jn(void 0!==e.opacity?e.opacity:1,0,Vo(i))):o&&(t.opacity=Jn(void 0!==e.opacity?e.opacity:1,void 0!==n.opacity?n.opacity:1,i));for(let r=0;r<To;r++){const s=`border${wo[r]}Radius`;let o=Ao(e,s),a=Ao(n,s);void 0===o&&void 0===a||(o||(o=0),a||(a=0),0===o||0===a||bo(o)===bo(a)?(t[s]=Math.max(Jn(So(o),So(a),i),0),(Yt.test(a)||Yt.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||n.rotate)&&(t.rotate=Jn(e.rotate||0,n.rotate||0,i))}(s,i,this.latestValues,n,c,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(H(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=z.update((()=>{ho.hasAnimatedSinceResize=!0,this.currentAnimation=function(t,e,n){const i=Vt(t)?t:Ie(t);return i.start(is("",i,e,n)),i.animation}(0,1e3,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0}))}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const t=this.getLead();let{targetWithTransforms:e,target:n,layout:i,latestValues:s}=t;if(e&&n&&i){if(this!==t&&this.layout&&i&&xr(this.options.animationType,this.layout.layoutBox,i.layoutBox)){n=this.target||{x:{min:0,max:0},y:{min:0,max:0}};const e=Rs(this.layout.layoutBox.x);n.x.min=t.target.x.min,n.x.max=n.x.min+e;const i=Rs(this.layout.layoutBox.y);n.y.min=t.target.y.min,n.y.max=n.y.min+i}Do(e,n),so(e,s),Ls(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new Ho);this.sharedNodes.get(t).add(e);const n=e.options.initialPromotionConfig;e.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(e):void 0})}isLead(){const t=this.getStack();return!t||t.lead===this}getLead(){var t;const{layoutId:e}=this.options;return e&&(null===(t=this.getStack())||void 0===t?void 0:t.lead)||this}getPrevLead(){var t;const{layoutId:e}=this.options;return e?null===(t=this.getStack())||void 0===t?void 0:t.prevLead:void 0}getStack(){const{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:n}={}){const i=this.getStack();i&&i.promote(this,n),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){const t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){const{visualElement:t}=this.options;if(!t)return;let e=!1;const{latestValues:n}=t;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(e=!0),!e)return;const i={};n.z&&Zo("z",t,i,this.animationValues);for(let s=0;s<Xo.length;s++)Zo(`rotate${Xo[s]}`,t,i,this.animationValues),Zo(`skew${Xo[s]}`,t,i,this.animationValues);t.render();for(const s in i)t.setStaticValue(s,i[s]),this.animationValues&&(this.animationValues[s]=i[s]);t.scheduleRender()}getProjectionStyles(t){var e,n;if(!this.instance||this.isSVG)return;if(!this.isVisible)return Go;const i={visibility:""},s=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,i.opacity="",i.pointerEvents=Ct(null==t?void 0:t.pointerEvents)||"",i.transform=s?s(this.latestValues,""):"none",i;const o=this.getLead();if(!this.projectionDelta||!this.layout||!o.target){const e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=Ct(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!Xs(this.latestValues)&&(e.transform=s?s({},""):"none",this.hasProjected=!1),e}const r=o.animationValues||o.latestValues;this.applyTransformsToTarget(),i.transform=function(t,e,n){let i="";const s=t.x.translate/e.x,o=t.y.translate/e.y,r=(null==n?void 0:n.z)||0;if((s||o||r)&&(i=`translate3d(${s}px, ${o}px, ${r}px) `),1===e.x&&1===e.y||(i+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:t,rotate:e,rotateX:s,rotateY:o,skewX:r,skewY:a}=n;t&&(i=`perspective(${t}px) ${i}`),e&&(i+=`rotate(${e}deg) `),s&&(i+=`rotateX(${s}deg) `),o&&(i+=`rotateY(${o}deg) `),r&&(i+=`skewX(${r}deg) `),a&&(i+=`skewY(${a}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return 1===a&&1===l||(i+=`scale(${a}, ${l})`),i||"none"}(this.projectionDeltaWithTransform,this.treeScale,r),s&&(i.transform=s(r,i.transform));const{x:a,y:l}=this.projectionDelta;i.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,o.animationValues?i.opacity=o===this?null!==(n=null!==(e=r.opacity)&&void 0!==e?e:this.latestValues.opacity)&&void 0!==n?n:1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:i.opacity=o===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0;for(const u in pe){if(void 0===r[u])continue;const{correct:t,applyTo:e}=pe[u],n="none"===i.transform?r[u]:t(r[u],o);if(e){const t=e.length;for(let s=0;s<t;s++)i[e[s]]=n}else i[u]=n}return this.options.layoutId&&(i.pointerEvents=o===this?Ct(null==t?void 0:t.pointerEvents)||"":"none"),i}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach((t=>{var e;return null===(e=t.currentAnimation)||void 0===e?void 0:e.stop()})),this.root.nodes.forEach(sr),this.root.sharedNodes.clear()}}}function Qo(t){t.updateLayout()}function tr(t){var e;const n=(null===(e=t.resumeFrom)||void 0===e?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&n&&t.hasListeners("didUpdate")){const{layoutBox:e,measuredBox:i}=t.layout,{animationType:s}=t.options,o=n.source!==t.layout.source;"size"===s?zs((t=>{const i=o?n.measuredBox[t]:n.layoutBox[t],s=Rs(i);i.min=e[t].min,i.max=i.min+s})):xr(s,n.layoutBox,e)&&zs((i=>{const s=o?n.measuredBox[i]:n.layoutBox[i],r=Rs(e[i]);s.max=s.min+r,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[i].max=t.relativeTarget[i].min+r)}));const r={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};Ls(r,e,n.layoutBox);const a={x:{translate:0,scale:1,origin:0,originPoint:0},y:{translate:0,scale:1,origin:0,originPoint:0}};o?Ls(a,t.applyTransform(i,!0),n.measuredBox):Ls(a,e,n.layoutBox);const l=!Io(r);let u=!1;if(!t.resumeFrom){const i=t.getClosestProjectingParent();if(i&&!i.resumeFrom){const{snapshot:s,layout:o}=i;if(s&&o){const r={x:{min:0,max:0},y:{min:0,max:0}};Fs(r,n.layoutBox,s.layoutBox);const a={x:{min:0,max:0},y:{min:0,max:0}};Fs(a,e,o.layoutBox),No(r,a)||(u=!0),i.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=r,t.relativeParent=i)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:n,delta:a,layoutDelta:r,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(t.isLead()){const{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function er(t){Ko&&Yo.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=Boolean(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function nr(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function ir(t){t.clearSnapshot()}function sr(t){t.clearMeasurements()}function or(t){t.isLayoutDirty=!1}function rr(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function ar(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function lr(t){t.resolveTargetDelta()}function ur(t){t.calcProjection()}function cr(t){t.resetSkewAndRotation()}function hr(t){t.removeLeadSnapshot()}function dr(t,e,n){t.translate=Jn(e.translate,0,n),t.scale=Jn(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function pr(t,e,n,i){t.min=Jn(e.min,n.min,i),t.max=Jn(e.max,n.max,i)}function mr(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}const fr={duration:.45,ease:[.4,0,.1,1]},gr=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),vr=gr("applewebkit/")&&!gr("chrome/")?Math.round:n;function yr(t){t.min=vr(t.min),t.max=vr(t.max)}function xr(t,e,n){return"position"===t||"preserve-aspect"===t&&(i=$o(e),s=$o(n),o=.2,!(Math.abs(i-s)<=o));var i,s,o}function Pr(t){var e;return t!==t.root&&(null===(e=t.scroll)||void 0===e?void 0:e.wasRoot)}const wr=Jo({attachResizeListener:(t,e)=>Ps(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),Tr={current:void 0},Sr=Jo({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!Tr.current){const t=new wr({});t.mount(window),t.setOptions({layoutScroll:!0}),Tr.current=t}return Tr.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>Boolean("fixed"===window.getComputedStyle(t).position)}),br={pan:{Feature:class extends vs{constructor(){super(...arguments),this.removePointerDownListener=n}onPointerDown(t){this.session=new bs(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ro(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:e,onPan:n,onPanEnd:i}=this.node.getProps();return{onSessionStart:co(t),onStart:co(e),onMove:n,onEnd:(t,e)=>{delete this.session,i&&z.postRender((()=>i(t,e)))}}}mount(){this.removePointerDownListener=Ts(this.node.current,"pointerdown",(t=>this.onPointerDown(t)))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}},drag:{Feature:class extends vs{constructor(t){super(t),this.removeGroupControls=n,this.removeListeners=n,this.controls=new lo(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||n}unmount(){this.removeGroupControls(),this.removeListeners()}},ProjectionNode:Sr,MeasureLayout:vo}};function Ar(t,e,n){const{props:i}=t;t.animationState&&i.whileHover&&t.animationState.setActive("whileHover","Start"===n);const s=i["onHover"+n];s&&z.postRender((()=>s(e,ws(e))))}function Er(t,e,n){const{props:i}=t;t.animationState&&i.whileTap&&t.animationState.setActive("whileTap","Start"===n);const s=i["onTap"+("End"===n?"":n)];s&&z.postRender((()=>s(e,ws(e))))}const Vr=new WeakMap,Cr=new WeakMap,Mr=t=>{const e=Vr.get(t.target);e&&e(t)},Dr=t=>{t.forEach(Mr)};function Rr(t,e,n){const i=function({root:t,...e}){const n=t||document;Cr.has(n)||Cr.set(n,{});const i=Cr.get(n),s=JSON.stringify(e);return i[s]||(i[s]=new IntersectionObserver(Dr,{root:t,...e})),i[s]}(e);return Vr.set(t,n),i.observe(t),()=>{Vr.delete(t),i.unobserve(t)}}const kr={some:0,all:1};const Lr={inView:{Feature:class extends vs{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:e,margin:n,amount:i="some",once:s}=t,o={root:e?e.current:void 0,rootMargin:n,threshold:"number"==typeof i?i:kr[i]};return Rr(this.node.current,o,(t=>{const{isIntersecting:e}=t;if(this.isInView===e)return;if(this.isInView=e,s&&!e&&this.hasEnteredView)return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);const{onViewportEnter:n,onViewportLeave:i}=this.node.getProps(),o=e?n:i;o&&o(t)}))}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;const{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}(t,e))&&this.startObserver()}unmount(){}}},tap:{Feature:class extends vs{mount(){const{current:t}=this.node;t&&(this.unmount=T(t,(t=>(Er(this.node,t,"Start"),(t,{success:e})=>Er(this.node,t,e?"End":"Cancel"))),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}},focus:{Feature:class extends vs{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=ri(Ps(this.node.current,"focus",(()=>this.onFocus())),Ps(this.node.current,"blur",(()=>this.onBlur())))}unmount(){}}},hover:{Feature:class extends vs{mount(){const{current:t}=this.node;t&&(this.unmount=w(t,(t=>(Ar(this.node,t,"Start"),t=>Ar(this.node,t,"End")))))}unmount(){}}}},jr={layout:{ProjectionNode:Sr,MeasureLayout:vo}},Br={current:null},Fr={current:!1};const Or=[...Yn,hn,Pn],Ir=new WeakMap;const Ur=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Wr{scrapeMotionValuesFromProps(t,e,n){return{}}constructor({parent:t,props:e,presenceContext:n,reducedMotionConfig:i,blockInitialAnimation:s,visualState:o},r={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=Wn,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const t=Re.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,z.render(this.render,!1,!0))};const{latestValues:a,renderState:l,onUpdate:u}=o;this.onUpdate=u,this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=n,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=r,this.blockInitialAnimation=Boolean(s),this.isControllingVariants=rt(e),this.isVariantNode=at(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=Boolean(t&&t.current);const{willChange:c,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(const d in h){const t=h[d];void 0!==a[d]&&Vt(t)&&t.set(a[d],!1)}}mount(t){this.current=t,Ir.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach(((t,e)=>this.bindToMotionValue(e,t))),Fr.current||function(){if(Fr.current=!0,O)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>Br.current=t.matches;t.addListener(e),e()}else Br.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||Br.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){Ir.delete(this.current),this.projection&&this.projection.unmount(),H(this.notifyUpdate),H(this.render),this.valueSubscriptions.forEach((t=>t())),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features){const e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();const n=kt.has(t),i=e.on("change",(e=>{this.latestValues[t]=e,this.props.onUpdate&&z.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)})),s=e.on("renderRequest",this.scheduleRender);let o;window.MotionCheckAppearSync&&(o=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,(()=>{i(),s(),o&&o(),e.owner&&e.stop()}))}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in q){const e=q[t];if(!e)continue;const{isEnabled:n,Feature:i}=e;if(!this.features[t]&&i&&n(this.props)&&(this.features[t]=new i(this)),this.features[t]){const e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):{x:{min:0,max:0},y:{min:0,max:0}}}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let n=0;n<Ur.length;n++){const e=Ur[n];this.propEventSubscriptions[e]&&(this.propEventSubscriptions[e](),delete this.propEventSubscriptions[e]);const i=t["on"+e];i&&(this.propEventSubscriptions[e]=this.on(e,i))}this.prevMotionValues=function(t,e,n){for(const i in e){const s=e[i],o=n[i];if(Vt(s))t.addValue(i,s);else if(Vt(o))t.addValue(i,Ie(s,{owner:t}));else if(o!==s)if(t.hasValue(i)){const e=t.getValue(i);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{const e=t.getStaticValue(i);t.addValue(i,Ie(void 0!==e?e:s,{owner:t}))}}for(const i in n)void 0===e[i]&&t.removeValue(i);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){const e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){const n=this.values.get(t);e!==n&&(n&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);const e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let n=this.values.get(t);return void 0===n&&void 0!==e&&(n=Ie(null===e?void 0:e,{owner:this}),this.addValue(t,n)),n}readValue(t,e){var n;let i=void 0===this.latestValues[t]&&this.current?null!==(n=this.getBaseTargetFromProps(this.props,t))&&void 0!==n?n:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];var s;return null!=i&&("string"==typeof i&&(Nn(i)||tn(i))?i=parseFloat(i):(s=i,!Or.find(Hn(s))&&Pn.test(e)&&(i=Vn(t,e))),this.setBaseTarget(t,Vt(i)?i.get():i)),Vt(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;const{initial:n}=this.props;let i;if("string"==typeof n||"object"==typeof n){const s=At(this.props,n,null===(e=this.presenceContext)||void 0===e?void 0:e.custom);s&&(i=s[t])}if(n&&void 0!==i)return i;const s=this.getBaseTargetFromProps(this.props,t);return void 0===s||Vt(s)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new je),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class Nr extends Wr{constructor(){super(...arguments),this.KeyframeResolver=Xn}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:n}){delete e[t],delete n[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;Vt(t)&&(this.childSubscription=t.on("change",(t=>{this.current&&(this.current.textContent=`${t}`)})))}}class $r extends Nr{constructor(){super(...arguments),this.type="html",this.renderInstance=ce}readValueFromInstance(t,e){if(kt.has(e)){const t=En(e);return t&&t.default||0}{const i=(n=t,window.getComputedStyle(n)),s=(jt(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}var n}measureInstanceViewportBox(t,{transformPagePoint:e}){return oo(t,e)}build(t,e,n){ne(t,e,n.transformTemplate)}scrapeMotionValuesFromProps(t,e,n){return fe(t,e,n)}}class zr extends Nr{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=$s}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(kt.has(e)){const t=En(e);return t&&t.default||0}return e=he.has(e)?e:pt(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,n){return ge(t,e,n)}build(t,e,n){re(t,e,this.isSVGTag,n.transformTemplate)}renderInstance(t,e,n,i){de(t,e,0,i)}mount(t){this.isSVGTag=ue(t.tagName),super.mount(t)}}const Hr=tt(Ae({...xs,...Lr,...br,...jr},((e,n)=>St(e)?new zr(n):new $r(n,{allowProjection:e!==t.Fragment}))));function Yr(t,e){let n;const i=()=>{const{currentTime:i}=e,s=(null===i?0:i.value)/100;n!==s&&t(s),n=s};return z.update(i,!0),()=>H(i)}const Kr=new WeakMap;let Xr;function Gr({target:t,contentRect:e,borderBoxSize:n}){var i;null===(i=Kr.get(t))||void 0===i||i.forEach((i=>{i({target:t,contentSize:e,get size(){return function(t,e){if(e){const{inlineSize:t,blockSize:n}=e[0];return{width:t,height:n}}return t instanceof SVGElement&&"getBBox"in t?t.getBBox():{width:t.offsetWidth,height:t.offsetHeight}}(t,n)}})}))}function qr(t){t.forEach(Gr)}function Zr(t,e){Xr||"undefined"!=typeof ResizeObserver&&(Xr=new ResizeObserver(qr));const n=S(t);return n.forEach((t=>{let n=Kr.get(t);n||(n=new Set,Kr.set(t,n)),n.add(e),null==Xr||Xr.observe(t)})),()=>{n.forEach((t=>{const n=Kr.get(t);null==n||n.delete(e),(null==n?void 0:n.size)||null==Xr||Xr.unobserve(t)}))}}const _r=new Set;let Jr;function Qr(t){return _r.add(t),Jr||(Jr=()=>{const t={width:window.innerWidth,height:window.innerHeight},e={target:window,size:t,contentSize:t};_r.forEach((t=>t(e)))},window.addEventListener("resize",Jr)),()=>{_r.delete(t),!_r.size&&Jr&&(Jr=void 0)}}const ta={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function ea(t,e,n,i){const s=n[e],{length:o,position:r}=ta[e],a=s.current,l=n.time;s.current=t[`scroll${r}`],s.scrollLength=t[`scroll${o}`]-t[`client${o}`],s.offset.length=0,s.offset[0]=0,s.offset[1]=s.scrollLength,s.progress=h(0,s.scrollLength,s.current);const u=i-l;s.velocity=u>50?0:Be(s.current-a,u)}const na={start:0,center:.5,end:1};function ia(t,e,n=0){let i=0;if(t in na&&(t=na[t]),"string"==typeof t){const e=parseFloat(t);t.endsWith("px")?i=e:t.endsWith("%")?t=e/100:t.endsWith("vw")?i=e/100*document.documentElement.clientWidth:t.endsWith("vh")?i=e/100*document.documentElement.clientHeight:t=e}return"number"==typeof t&&(i=e*t),n+i}const sa=[0,0];function oa(t,e,n,i){let s=Array.isArray(t)?t:sa,o=0,r=0;return"number"==typeof t?s=[t,t]:"string"==typeof t&&(s=(t=t.trim()).includes(" ")?t.split(" "):[t,na[t]?t:"0"]),o=ia(s[0],n,i),r=ia(s[1],e),o-r}const ra={Enter:[[0,1],[1,1]],Exit:[[0,0],[1,0]],Any:[[1,0],[0,1]],All:[[0,0],[1,1]]},aa={x:0,y:0};function la(t,e,n){const{offset:i=ra.All}=n,{target:s=t,axis:o="y"}=n,r="y"===o?"height":"width",a=s!==t?function(t,e){const n={x:0,y:0};let i=t;for(;i&&i!==e;)if(i instanceof HTMLElement)n.x+=i.offsetLeft,n.y+=i.offsetTop,i=i.offsetParent;else if("svg"===i.tagName){const t=i.getBoundingClientRect();i=i.parentElement;const e=i.getBoundingClientRect();n.x+=t.left-e.left,n.y+=t.top-e.top}else{if(!(i instanceof SVGGraphicsElement))break;{const{x:t,y:e}=i.getBBox();n.x+=t,n.y+=e;let s=null,o=i.parentNode;for(;!s;)"svg"===o.tagName&&(s=o),o=i.parentNode;i=s}}return n}(s,t):aa,l=s===t?{width:t.scrollWidth,height:t.scrollHeight}:function(t){return"getBBox"in t&&"svg"!==t.tagName?t.getBBox():{width:t.clientWidth,height:t.clientHeight}}(s),u={width:t.clientWidth,height:t.clientHeight};e[o].offset.length=0;let c=!e[o].interpolate;const h=i.length;for(let d=0;d<h;d++){const t=oa(i[d],u[r],l[r],a[o]);c||t===e[o].interpolatorOffsets[d]||(c=!0),e[o].offset[d]=t}c&&(e[o].interpolate=$i(e[o].offset,zi(i),{clamp:!1}),e[o].interpolatorOffsets=[...e[o].offset]),e[o].progress=Ut(0,1,e[o].interpolate(e[o].current))}function ua(t,e,n,i={}){return{measure:()=>function(t,e=t,n){if(n.x.targetOffset=0,n.y.targetOffset=0,e!==t){let i=e;for(;i&&i!==t;)n.x.targetOffset+=i.offsetLeft,n.y.targetOffset+=i.offsetTop,i=i.offsetParent}n.x.targetLength=e===t?e.scrollWidth:e.clientWidth,n.y.targetLength=e===t?e.scrollHeight:e.clientHeight,n.x.containerLength=t.clientWidth,n.y.containerLength=t.clientHeight}(t,i.target,n),update:e=>{!function(t,e,n){ea(t,"x",e,n),ea(t,"y",e,n),e.time=n}(t,n,e),(i.offset||i.target)&&la(t,n,i)},notify:()=>e(n)}}const ca=new WeakMap,ha=new WeakMap,da=new WeakMap,pa=t=>t===document.documentElement?window:t;function ma(t,{container:e=document.documentElement,...n}={}){let i=da.get(e);i||(i=new Set,da.set(e,i));const s=ua(e,t,{time:0,x:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0},y:{current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}},n);if(i.add(s),!ca.has(e)){const t=()=>{for(const t of i)t.measure()},n=()=>{for(const t of i)t.update(Y.timestamp)},s=()=>{for(const t of i)t.notify()},a=()=>{z.read(t,!1,!0),z.read(n,!1,!0),z.update(s,!1,!0)};ca.set(e,a);const l=pa(e);window.addEventListener("resize",a,{passive:!0}),e!==document.documentElement&&ha.set(e,(r=a,"function"==typeof(o=e)?Qr(o):Zr(o,r))),l.addEventListener("scroll",a,{passive:!0})}var o,r;const a=ca.get(e);return z.read(a,!1,!0),()=>{var t;H(a);const n=da.get(e);if(!n)return;if(n.delete(s),n.size)return;const i=ca.get(e);ca.delete(e),i&&(pa(e).removeEventListener("scroll",i),null===(t=ha.get(e))||void 0===t||t(),window.removeEventListener("resize",i))}}const fa=new Map;function ga({source:t,container:e=document.documentElement,axis:n="y"}={}){t&&(e=t),fa.has(e)||fa.set(e,{});const i=fa.get(e);return i[n]||(i[n]=b()?new ScrollTimeline({source:e,axis:n}):function({source:t,container:e,axis:n="y"}){t&&(e=t);const i={value:0},s=ma((t=>{i.value=100*t[n].progress}),{container:e,axis:n});return{currentTime:i,cancel:s}}({source:e,axis:n})),i[n]}function va(t){return t&&(t.target||t.offset)}function ya(t,{axis:e="y",...i}={}){const s={axis:e,...i};return"function"==typeof t?function(t,e){return function(t){return 2===t.length}(t)||va(e)?ma((n=>{t(n[e.axis].progress,n)}),e):Yr(t,ga(e))}(t,s):function(t,e){if(t.flatten(),va(e))return t.pause(),ma((n=>{t.time=t.duration*n[e.axis].progress}),e);{const i=ga(e);return t.attachTimeline?t.attachTimeline(i,(t=>(t.pause(),Yr((e=>{t.time=t.duration*e}),i)))):n}}(t,s)}function xa(t,e){A(Boolean(!e||e.current))}const Pa=()=>({scrollX:Ie(0),scrollY:Ie(0),scrollXProgress:Ie(0),scrollYProgress:Ie(0)});function wa({container:e,target:n,layoutEffect:i=!0,...s}={}){const o=V(Pa);return(i?I:t.useEffect)((()=>(xa(0,n),xa(0,e),ya(((t,{x:e,y:n})=>{o.scrollX.set(e.current),o.scrollXProgress.set(e.progress),o.scrollY.set(n.current),o.scrollYProgress.set(n.progress)}),{...s,container:(null==e?void 0:e.current)||void 0,target:(null==n?void 0:n.current)||void 0}))),[e,n,JSON.stringify(s.offset)]),o}function Ta(e,n){const i=function(e){const n=V((()=>Ie(e))),{isStatic:i}=t.useContext(M);if(i){const[,i]=t.useState(e);t.useEffect((()=>n.on("change",i)),[])}return n}(n()),s=()=>i.set(n());return s(),I((()=>{const t=()=>z.preRender(s,!1,!0),n=e.map((e=>e.on("change",t)));return()=>{n.forEach((t=>t())),H(s)}})),i}function Sa(t,e,n,i){if("function"==typeof t)return function(t){Fe.current=[],t();const e=Ta(Fe.current,t);return Fe.current=void 0,e}(t);const s="function"==typeof e?e:function(...t){const e=!Array.isArray(t[0]),n=e?0:-1,i=t[0+n],s=t[1+n],o=t[2+n],r=t[3+n],a=$i(s,o,{mixer:(l=o[0],(t=>t&&"object"==typeof t&&t.mix)(l)?l.mix:void 0),...r});var l;return e?a(i):a}(e,n,i);return Array.isArray(t)?ba(t,s):ba([t],(([t])=>s(t)))}function ba(t,e){const n=V((()=>[]));return Ta(t,(()=>{n.length=0;const i=t.length;for(let e=0;e<i;e++)n[e]=t[e].get();return e(n)}))}function Aa(t,e){[...e].reverse().forEach((n=>{const i=t.getVariant(n);i&&We(t,i),t.variantChildren&&t.variantChildren.forEach((t=>{Aa(t,e)}))}))}function Ea(){const t=new Set,e={subscribe:e=>(t.add(e),()=>{t.delete(e)}),start(e,n){const i=[];return t.forEach((t=>{i.push(ls(t,e,{transitionOverride:n}))})),Promise.all(i)},set:e=>t.forEach((t=>{!function(t,e){Array.isArray(e)?Aa(t,e):"string"==typeof e?Aa(t,[e]):We(t,e)}(t,e)})),stop(){t.forEach((t=>{!function(t){t.values.forEach((t=>t.stop()))}(t)}))},mount:()=>()=>{e.stop()}};return e}const Va=function(){const t=V(Ea);return I(t.mount,[]),t},Ca={some:0,all:1};function Ma(e,{root:n,margin:i,amount:s,once:o=!1}={}){const[r,a]=t.useState(!1);return t.useEffect((()=>{if(!e.current||o&&r)return;const t={root:n&&n.current||void 0,margin:i,amount:s};return function(t,e,{root:n,margin:i,amount:s="some"}={}){const o=S(t),r=new WeakMap,a=new IntersectionObserver((t=>{t.forEach((t=>{const n=r.get(t.target);if(t.isIntersecting!==Boolean(n))if(t.isIntersecting){const n=e(t);"function"==typeof n?r.set(t.target,n):a.unobserve(t.target)}else"function"==typeof n&&(n(t),r.delete(t.target))}))}),{root:n,rootMargin:i,threshold:"number"==typeof s?s:Ca[s]});return o.forEach((t=>a.observe(t))),()=>a.disconnect()}(e.current,(()=>(a(!0),o?void 0:()=>a(!1))),t)}),[n,e,i,o,s]),r}export{U as A,Sa as a,Ma as b,Va as c,Hr as m,wa as u};

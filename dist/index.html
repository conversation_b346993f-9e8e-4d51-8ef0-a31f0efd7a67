<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="Auctus - Digital Creative Agency specializing in web development, app development, and graphic design services." />
    
    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    
    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Preload critical resources -->
    <link rel="preload" href="data:application/octet-stream;base64,CmltcG9ydCB7IFN0cmljdE1vZGUgfSBmcm9tICdyZWFjdCcKaW1wb3J0IHsgY3JlYXRlUm9vdCB9IGZyb20gJ3JlYWN0LWRvbS9jbGllbnQnCmltcG9ydCBBcHAgZnJvbSAiLi9BcHAiCmltcG9ydCAnLi9pbmRleC5jc3MnCgpjb25zdCByb290RWxlbWVudCA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdyb290Jyk7CgppZiAocm9vdEVsZW1lbnQpIHsKICBjcmVhdGVSb290KHJvb3RFbGVtZW50KS5yZW5kZXIoCiAgICA8U3RyaWN0TW9kZT4KICAgICAgPEFwcCAvPgogICAgPC9TdHJpY3RNb2RlPgogICk7Cn0gZWxzZSB7CiAgY29uc29sZS5lcnJvcignUm9vdCBlbGVtZW50IG5vdCBmb3VuZCcpOwp9Cg==" as="script" type="module">
    
    <!-- Preload font -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600&display=swap" as="style">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600&display=swap">
    
    <title>Auctus - Digital Creative Agency</title>
    
    <!-- Critical CSS inline (consider extracting your most important styles here) -->
    <style>
      body {
        margin: 0;
        padding: 0;
        font-family: 'Montserrat', sans-serif;
        background-color: #121B1B;
        color: #ffffff;
      }
      #root {
        display: flex;
        flex-direction: column;
        min-height: 100vh;
      }
    </style>
    <script type="module" crossorigin src="/assets/index-BWF5eo3F.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/vendor-other-DSmItuYW.js">
    <link rel="modulepreload" crossorigin href="/assets/vendor-react-BQEJZSNG.js">
    <link rel="modulepreload" crossorigin href="/assets/vendor-ui-C9T5t0i2.js">
    <link rel="modulepreload" crossorigin href="/assets/utils-k4nC7ovr.js">
    <link rel="modulepreload" crossorigin href="/assets/components-other-CtgHTO3L.js">
    <link rel="modulepreload" crossorigin href="/assets/components-common-Z00V3aSN.js">
    <link rel="modulepreload" crossorigin href="/assets/components-animation-zKjHGkHQ.js">
    <link rel="stylesheet" crossorigin href="/assets/components-other.p0fozmBy.css">
    <link rel="stylesheet" crossorigin href="/assets/components-common.DOlGk8ak.css">
    <link rel="stylesheet" crossorigin href="/assets/components-animation.DA0rb9Y2.css">
    <link rel="stylesheet" crossorigin href="/assets/index.C-EwIKTC.css">
  </head>
  <body>
    <div id="root"></div>
    
    <!-- Main script -->
  </body>
</html>
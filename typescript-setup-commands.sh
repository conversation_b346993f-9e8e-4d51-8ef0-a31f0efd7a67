#!/bin/bash

# Install TypeScript and core type definitions
npm install --save-dev typescript

# Install type definitions for libraries you're using
npm install --save-dev @types/node @types/react-router-dom @types/react-slick @types/react-transition-group

# Create tsconfig.json
echo '{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    
    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    
    /* Type Checking */
    "strict": true,
    "noImplicitAny": false,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noFallthroughCasesInSwitch": true,
    
    /* Paths */
    "baseUrl": ".",
    "paths": {
      "@assets/*": ["./src/assets/*"],
      "@components/*": ["./src/components/*"],
      "@pages/*": ["./src/pages/*"],
      "@templates/*": ["./src/pages/templates/*"]
    }
  },
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}' > tsconfig.json

# Create tsconfig.node.json for Vite
echo '{
  "compilerOptions": {
    "composite": true,
    "skipLibCheck": true,
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowSyntheticDefaultImports": true
  },
  "include": ["vite.config.ts"]
}' > tsconfig.node.json

# Create types directory
mkdir -p src/types

# Create base types file
echo '// src/types/index.ts

// Pricing Plan Types
export interface PricingFeature {
  title: string;
  description: string;
}

export interface PricingPlan {
  plan: string;
  price: string;
  regularPrice: string;
  description: string;
  features: PricingFeature[];
  isPopular: boolean;
}

// Blog Post Types
export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  coverImage: string;
  publishedAt: string;
  category: string;
  author: {
    name: string;
    avatar?: string;
  };
}

// Portfolio Project Types
export interface PortfolioProject {
  id: string;
  title: string;
  slug: string;
  description: string;
  category: "web-development" | "app-development" | "graphic-design";
  coverImage: string;
  images: string[];
  technologies: string[];
  clientName?: string;
  projectUrl?: string;
  completed: string; // Date
}

// Quote Quiz Types
export interface QuizAnswer {
  websiteType: string;
  businessType: string;
  hasExistingWebsite: string;
  timeline: string;
  budget: string;
  requirements: string;
  email: string;
  clientName: string;
  contactNumber: string;
}
' > src/types/index.ts

# Update package.json scripts
npx -y json -I -f package.json -e 'this.scripts.typecheck="tsc"'
npx -y json -I -f package.json -e 'this.scripts.build="tsc && vite build"'

# Convert vite.config.js to vite.config.ts
mv vite.config.js vite.config.ts

# Create conversion utility for later use
echo '// File: scripts/convertJsxToTsx.js
const fs = require("fs");
const path = require("path");

function findFiles(dir, extension) {
  let results = [];
  const list = fs.readdirSync(dir);
  
  list.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory() && file !== "node_modules") {
      // Recurse into subdirectory
      results = results.concat(findFiles(filePath, extension));
    } else if (file.endsWith(extension)) {
      results.push(filePath);
    }
  });
  
  return results;
}

// Find all .jsx files
const jsxFiles = findFiles("src", ".jsx");

console.log(`Found ${jsxFiles.length} JSX files to convert.`);

// Process them one by one
jsxFiles.forEach(file => {
  const tsxFile = file.replace(".jsx", ".tsx");
  let contents = fs.readFileSync(file, "utf8");
  
  // Add React import if missing
  if (!contents.includes("import React")) {
    contents = `import React from "react";\n${contents}`;
  }
  
  console.log(`Converting ${file} to ${tsxFile}`);
  fs.writeFileSync(tsxFile, contents);
});

console.log("Conversion completed!");
' > scripts/convertJsxToTsx.js

echo "TypeScript setup complete! Run the following commands to convert your project:"
echo "1. npm run typecheck - to verify TypeScript setup"
echo "2. node scripts/convertJsxToTsx.js - to convert JSX files to TSX"

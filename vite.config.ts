import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import { resolve, dirname } from "path";
import { fileURLToPath } from "url";
import compression from "vite-plugin-compression";

// Define __dirname
const __dirname = dirname(fileURLToPath(import.meta.url));

export default defineConfig({
  plugins: [
    react(),
    compression({
      algorithm: "gzip", // You can also use 'brotliCompress' for better compression
      ext: ".gz",
      threshold: 10240, // Only compress files > 10kb
      deleteOriginFile: false,
      verbose: true,
    }),
  ],
  resolve: {
    alias: {
      "@assets": resolve(__dirname, "./src/assets"),
      "@components": resolve(__dirname, "./src/components"),
      "@pages": resolve(__dirname, "./src/pages"),
      "@templates": resolve(__dirname, "./src/pages/templates"),
    },
    extensions: ['.js', '.jsx', '.ts', '.tsx', '.json'],
  },
  build: {
    // Enable minification for better performance
    minify: "terser",
    terserOptions: {
      compress: {
        drop_console: true, // Remove console.log in production
        drop_debugger: true,
      },
    },
    // Improve chunk size - using function form to avoid warning
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Vendor chunk for node_modules
          if (id.includes('node_modules')) {
            if (id.includes('react') || id.includes('react-dom') || id.includes('react-router')) {
              return 'vendor-react';
            }
            if (id.includes('framer-motion') || id.includes('slick-carousel') || id.includes('react-slick')) {
              return 'vendor-ui';
            }
            return 'vendor-other';
          }
          // Separate utility functions
          if (id.includes('/utils/') || id.includes('/helpers/') || id.includes('/lib/')) {
            return 'utils';
          }
          // Separate components by type
          if (id.includes('/components/')) {
            // Common and small components go together
            if (id.includes('/common/') || id.includes('/Navbar/') || id.includes('/Footer/')) {
              return 'components-common';
            }
            // Animation-heavy components in their own chunk
            if (id.includes('/AnimatedCards/') || id.includes('/ParallaxHero/') || id.includes('/CardCarousel/')) {
              return 'components-animation';
            }
            // Other components
            return 'components-other';
          }
        },
        // Ensure assets are properly hashed for better caching
        assetFileNames: 'assets/[name].[hash].[ext]',
      },
    },
    // Report on chunk sizes after build
    reportCompressedSize: true,
    // Chunk size warning limit (2MB)
    chunkSizeWarningLimit: 2000,
  },
  server: {
    host: "0.0.0.0", // Local network access
    port: 5173,      // Default port
    proxy: {
      "/responses": "http://localhost:5000",
    },
  },
  // Optimize CSS
  css: {
    devSourcemap: true,
    preprocessorOptions: {
      scss: {
        quietDeps: true, // Reduces SCSS warnings
      },
    },
  },
  // Optimize preview server
  preview: {
    port: 4173,
    host: "0.0.0.0",
    // Enable compression for preview server
    compress: true,
  },
});

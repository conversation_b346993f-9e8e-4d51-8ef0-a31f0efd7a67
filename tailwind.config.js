/** @type {import('tailwindcss').Config} */
/* eslint-env node */
/* eslint-disable */
const flattenColorPalette = require('tailwindcss/lib/util/flattenColorPalette');

module.exports = {
  content: [
    "./index.html", // This is your existing content configuration
    "./src/**/*.{js,ts,jsx,tsx}", // This includes all relevant file types
    "./pages/**/*.{js,ts,jsx,tsx}", // This includes all relevant file types
  ],
  darkMode: "class", // Enable dark mode based on the "class" strategy
  theme: {
    extend: {
      spacing: {
        '30': '30px',
        '100': '100px',
        '150': '150px',
        '200': '200px',
        '250': '250px',
        '300': '300px',
        '350': '350px',
        '400': '400px',
      },
      colors: {
        darkGreen: '#1A2626',
        sDarkGreen: '#121B1B',
        altGreen: '#2E4445',
        lightGreen: '#3fa98e',
        LightestGreen: '#AACEC5',
        white: '#ffffff',
      },
      boxShadow: {
        input: `0px 2px 3px -1px rgba(0,0,0,0.1), 0px 1px 0px 0px rgba(25,28,33,0.02), 0px 0px 0px 1px rgba(25,28,33,0.08)`,
      },
      borderColor: {
        lightGreen30: 'rgba(63, 169, 142, 0.3)', // Light green with 30% opacity
      },
      borderWidth: {
        '0.5': '0.5px', // Add the 0.5px border width
      }
    },
  },
  plugins: [
    function addVariablesForColors({ addBase, theme }) {
      let allColors = flattenColorPalette(theme("colors"));
      let newVars = Object.fromEntries(
        Object.entries(allColors).map(([key, val]) => [`--${key}`, val])
      );
      addBase({
        ":root": newVars,
      });
    },
  ],
};